package com.extracme.evcard.mtc.bo;

import lombok.Data;

import java.util.List;

@Data
public class CreateVehicleRepairTaskReqBO{
    /**
     * 维修类型（ 1：事故维修 2：自费维修 3：车辆保养  7：终端维修 9：短租包修 10：短租包修）
     */
    private Integer repairType;

    /**
     * 出险日期/送修日期
     */
    private String sendRepairTime;

    /**
     * 所属公司id
     */
    private String orgId;

    /**
     * 所属公司名称
     */
    private String orgName;

    /**
     * 任务状态 1-待分配 2-送修中 3-待接车 4-进保预审 5-维修报价 6-核损核价 7-车辆维修 8-改派申请 9-车辆验收 10-已完成 11-已关闭
     */
    private Integer repairTaskStatus;

    /**
     * 修理厂ID
     */
    private String repairDepotId;

    /**
     * 修理厂名称
     */
    private String repairDepotName;

    /**
     * 送修方式 1-我方送修 2-非我方送修
     */
    private Integer sendRepairMethodType;

    /**
     * 情况描述
     */
    private String miscDesc;

    /**
     * 照片/视频
     */
    private List<AttachmentInfoBO> attachmentInfos;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 里程数
     */
    private Integer vehicleMile;

    /**
     * 产品线 1:车管中心 2:长租 3:短租 4:公务用车
     */
    private Integer productLine;

    /**
     * 子产品线 1:携程短租-短租 2:门店-短租 3.分时-短租 4.普通-长租 5.时行-长租 6.平台业务-长租 7.政企业务-长租 8.网约车业务-长租 9-公务用车
     */
    private Integer subProductLine;

    /**
     * 车辆所属公司id
     */
    private String vehicleOrgId;

    /**
     * 车辆所属公司名称
     */
    private String vehicleOrgName;

    /**
     * 车辆运营公司id
     */
    private String vehicleOperateOrgId;

    /**
     * 车辆运营公司名称
     */
    private String vehicleOperateOrgName;

    /**
     * 实际运营标签 1.未投车辆 2.分时运营车辆（废） 3.短租运营车辆 4.长租运营车辆 5.待退运车辆 6.已处置车辆 7.特殊车辆 8.备库车辆
     */
    private Integer factOperateTag;

    /**
     * 车型id
     */
    private Long vehicleModelSeq;

    /**
     * 车型名称
     */
    private String vehicleModelName;

    /**
     * 关联订单
     */
    private String orderNo;

    /**
     * 订单类型 1=长租订单 2=门店订单 3=渠道订单 4=内部订单 5=分时订单
     */
    private Integer orderType;

    /**
     * 关联类型： 1=订单匹配 2=手工关联 3=无关联订单
     */
    private Integer relateType;

    /**
     * 关联事故
     */
    private String accidentNo;

    /**
     * 订单备注
     */
    private String orderRemark;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 修理厂类型(1:合作修理厂 2:非合作修理厂)
     */
    private Integer repairDepotType;

    /**
     * 修理厂等级
     */
    private String repairDepotGrade;

    /**
     * 受损部分描述
     */
    private String damagedPartDescribe;

    /**
     * 网关处理字段
     */
    private String userName;

    /**
     * 维修类型列表-适用于同时创建多个维修任务（ 1：事故维修 2：自费维修 3：车辆保养  7：终端维修 9：短租包修 10：短租包修）
     */
    private List<Integer> repairTypeList;

    /**
     * 当前用户
     */
    private CurrentUserBO currentUser;
}
