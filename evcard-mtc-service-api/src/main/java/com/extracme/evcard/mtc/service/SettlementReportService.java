package com.extracme.evcard.mtc.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.extracme.evcard.mtc.bo.OfferStatisticsBO;
import com.extracme.evcard.mtc.bo.SettlementReportBO;
import com.extracme.evcard.mtc.bo.SubSettlementReportBO;
import com.extracme.evcard.mtc.bo.TimeoutTaskStatisticsBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 项目名称：evcard-mtc-service-api 类名称：SettlementReportService 类描述：统计报表业务层接口
 * 创建人：sunb-孙彬 创建时间：2017年12月13日上午9:30:27 修改备注 @version1.0
 */
public interface SettlementReportService {

    /**
     * 统计报表一览
     *
     * @param pageNum                页码
     * @param pageSize               每页显示条数
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param repairDepotId
     * @param renttype               业务状态
     * @param reviewToSelFeeFlag
     * @return
     */
    PageInfo<SettlementReportBO> getSettlementReportInfo(Integer pageNum, Integer pageSize, String orgId,
                                                         Integer repairTypeId, String taskNo, String vehicleNo, String vehicleModelSeq, String taskInflowStartTime,
                                                         String taskInflowEndTime, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                         String vehicleRepairStartTime, String vehicleRepairEndTime, String vehicleCheckStartTime,
                                                         String vehicleCheckEndTime, String verificationLossCheckStartTime, String verificationLossCheckEndTime,
                                                         String repairDepotId, Integer renttype, List<Integer> renttypeList,
                                                         Integer noDeductiblesFlag,Integer factOperateTag, 
                                                         String accidentNo, Integer reviewToSelFeeFlag, HttpServletRequest request);

    /**
     * 统计子报表一览
     *
     * @param pageNum                页码
     * @param pageSize               每页显示条数
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param repairDepotId
     * @param renttype               业务状态
     * @param subType                类型
     * @param isAll                  是否显示总数
     * @return
     */
    PageInfo<SubSettlementReportBO> getSubSettlementReportInfo(Integer pageNum, Integer pageSize, String orgId,
                                                               Integer repairTypeId, String taskNo, String vehicleNo, String vehicleModelSeq, String taskInflowStartTime,
                                                               String taskInflowEndTime, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                               String vehicleRepairStartTime, String vehicleRepairEndTime, String vehicleCheckStartTime,
                                                               String vehicleCheckEndTime, String repairDepotId, Integer renttype, List<Integer> renttypeList,
                                                               Integer subType, String projectType, Integer isAll, HttpServletRequest request);

    /**
     * 统计报表导出
     *
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param repairDepotId
     * @param renttype               业务状态
     * @param reviewToSelFeeFlag
     * @return
     */
    DefaultServiceRespDTO operateExportSettlementReportInfo(String orgId, Integer repairTypeId, String taskNo,
                                                            String vehicleNo, String vehicleModelSeq, String taskInflowStartTime, String taskInflowEndTime,
                                                            String vehicleReciveStartTime, String vehicleReciveEndTime, String vehicleRepairStartTime,
                                                            String vehicleRepairEndTime, String vehicleCheckStartTime, String vehicleCheckEndTime,
                                                            String verificationLossCheckStartTime,String verificationLossCheckEndTime,String repairDepotId,
                                                            Integer renttype, List<Integer> renttypeList, Integer noDeductiblesFlag, Integer factOperateTag,
                                                            String accidentNo, Integer reviewToSelFeeFlag, HttpServletRequest request);

    /**
     * 统计报表导出
     *
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param repairDepotId
     * @param renttype               业务状态
     * @param projectType            项目名称
     * @return
     */
    DefaultServiceRespDTO operateExportSubSettlementReportInfo(String orgId, Integer repairTypeId, String taskNo,
                                                               String vehicleNo, String vehicleModelSeq, String taskInflowStartTime, String taskInflowEndTime,
                                                               String vehicleReciveStartTime, String vehicleReciveEndTime, String vehicleRepairStartTime,
                                                               String vehicleRepairEndTime, String vehicleCheckStartTime, String vehicleCheckEndTime, String repairDepotId,
                                                               Integer renttype, List<Integer> renttypeList, Integer subType, String projectType, HttpServletRequest request);

    /**
     * 超时任务统计一览
     *
     * @param pageNum                页码
     * @param pageSize               每页显示条数
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param repairGrade            修理级别
     * @param vehicleNo              车牌号
     * @param repairDepotName        修理厂名称
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 实际修理完成开始时间
     * @param vehicleRepairEndTime   实际修理完成结束时间
     * @param renttype               业务状态
     * @return
     */
    public PageInfo<TimeoutTaskStatisticsBO> getTimeoutTaskList(Integer pageNum, Integer pageSize, String orgId,
                                                                Integer repairTypeId, String repairGrade, String vehicleNo, String repairDepotName,
                                                                String vehicleReciveStartTime, String vehicleReciveEndTime, String vehicleRepairStartTime,
                                                                String vehicleRepairEndTime, Integer renttype, List<Integer> renttypeList, Integer factOperateTag,
                                                                HttpServletRequest request);

    /**
     * 超时任务统计一览
     *
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param repairGrade            修理级别
     * @param vehicleNo              车牌号
     * @param repairDepotName        修理厂名称
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 实际修理完成开始时间
     * @param vehicleRepairEndTime   实际修理完成结束时间
     * @param renttype               业务状态
     * @return
     */
    public DefaultServiceRespDTO exportTimeoutTask(String orgId, Integer repairTypeId, String repairGrade,
                                                   String vehicleNo, String repairDepotName, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                   String vehicleRepairStartTime, String vehicleRepairEndTime, Integer renttype, List<Integer> renttypeList,
                                                   Integer factOperateTag, HttpServletRequest request, HttpServletResponse response);

    /**
     * 报价统计一览
     *
     * @param pageNum                        页码
     * @param pageSize                       每页显示条数
     * @param orgId                          车辆运营单位
     * @param repairGrade                    修理级别
     * @param vehicleNo                      车牌号
     * @param repairDepotName                修理厂名称
     * @param vehicleReciveStartTime         车辆接收开始时间
     * @param vehicleReciveEndTime           车辆接收结束时间
     * @param verificationLossCheckStartTime 核价通过开始时间
     * @param verificationLossCheckEndTime   核价通过结束时间
     * @param renttype                       业务状态
     * @param request
     * @return
     */
    public PageInfo<OfferStatisticsBO> getOfferStatisticsList(Integer pageNum, Integer pageSize, String orgId,
                                                              String repairGrade, String vehicleNo, String repairDepotName, String vehicleReciveStartTime,
                                                              String vehicleReciveEndTime, String verificationLossCheckStartTime, String verificationLossCheckEndTime,
                                                              String taskNo, Integer renttype, List<Integer> renttypeList, Integer factOperateTag, HttpServletRequest request);

    /**
     * 导出报价统计
     *
     * @param orgId                          车辆运营单位
     * @param repairGrade                    修理级别
     * @param vehicleNo                      车牌号
     * @param repairDepotName                修理厂名称
     * @param vehicleReciveStartTime         车辆接收开始时间
     * @param vehicleReciveEndTime           车辆接收结束时间
     * @param verificationLossCheckStartTime 核价通过开始时间
     * @param verificationLossCheckEndTime   核价通过结束时间
     * @param repairTypeId
     * @param renttype                       业务状态
     * @param request
     * @return
     */
    public DefaultServiceRespDTO exportOfferStatistics(String orgId, String repairGrade, String vehicleNo,
                                                       String repairDepotName, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                       String verificationLossCheckStartTime, String verificationLossCheckEndTime, String taskNo, Integer renttype,
                                                       List<Integer> renttypeList, Integer factOperateTag, HttpServletRequest request, HttpServletResponse response);

}
