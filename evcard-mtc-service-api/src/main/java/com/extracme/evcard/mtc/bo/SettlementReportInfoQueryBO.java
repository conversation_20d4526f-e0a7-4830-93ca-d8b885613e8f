package com.extracme.evcard.mtc.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> zhai
 * @date 2019/9/9 0009
 */
@Data
public class SettlementReportInfoQueryBO {

    private String orgId;

    private Integer repairTypeId;

    private String taskNo;

    private String vehicleNo;

    private String vehicleModelSeq;

    private String taskInflowStartTime;

    private String taskInflowEndTime;

    private String vehicleReciveStartTime;

    private String vehicleReciveEndTime;

    private String vehicleRepairStartTime;

    private String vehicleRepairEndTime;

    private String vehicleCheckStartTime;

    private String vehicleCheckEndTime;

    private String verificationLossCheckStartTime;
    
    private String verificationLossCheckEndTime;
    
    private List<String> repairDepotIds;

    private Integer renttype;

    private List<Integer> renttypeList;

    private Integer pageNum;

    private Integer pageSize;

    private String queryIndex;

    private List<SettlementReportBO> list;
    
    private Integer noDeductiblesFlag;

    /**
     * 类型 1：配件项目、2：工时项目、3：辅料项目、4：拖车费用
     */
    private Integer subType;

    private Integer factOperateTag;

    private String accidentNo;
    
    private Integer reviewToSelFeeFlag;

    private String projectType;
}
