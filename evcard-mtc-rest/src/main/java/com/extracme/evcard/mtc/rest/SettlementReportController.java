package com.extracme.evcard.mtc.rest;

import com.extracme.evcard.mtc.bo.OfferStatisticsBO;
import com.extracme.evcard.mtc.bo.SettlementReportBO;
import com.extracme.evcard.mtc.bo.SubSettlementReportBO;
import com.extracme.evcard.mtc.bo.TimeoutTaskStatisticsBO;
import com.extracme.evcard.mtc.common.ComUtil;
import com.extracme.evcard.mtc.common.Contants;
import com.extracme.evcard.mtc.common.ThreadPoolUtils;
import com.extracme.evcard.mtc.service.SettlementReportService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目名称：evcard-mtc-rest 类名称：SettlementReportController 类描述：统计报表控制层 创建人：sunb-孙彬
 * 创建时间：2017年12月11日下午8:59:41 修改备注 @version1.0
 */

@RestController
@RequestMapping("api")
public class SettlementReportController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final Integer YEAR_OF_DAYS = 366;

    @Resource
    private SettlementReportService settlementReportServiceImpl;

    /**
     * 统计报表一览
     *
     * @param pageNum                页码
     * @param pageSize               每页显示条数
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param renttype               业务状态
     * @return
     */
    @RequestMapping(value = "settlementReportInfo", method = RequestMethod.GET)
    public DefaultWebRespVO querySettlementReportInfo(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "orgId", required = false) String orgId,
            @RequestParam(value = "repairTypeId", required = false) Integer repairTypeId,
            @RequestParam(value = "taskNo", required = false) String taskNo,
            @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
            @RequestParam(value = "vehicleModelSeq", required = false) String vehicleModelSeq,
            @RequestParam(value = "taskInflowStartTime", required = false) String taskInflowStartTime,
            @RequestParam(value = "taskInflowEndTime", required = false) String taskInflowEndTime,
            @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
            @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
            @RequestParam(value = "vehicleRepairStartTime", required = false) String vehicleRepairStartTime,
            @RequestParam(value = "vehicleRepairEndTime", required = false) String vehicleRepairEndTime,
            @RequestParam(value = "vehicleCheckStartTime", required = false) String vehicleCheckStartTime,
            @RequestParam(value = "vehicleCheckEndTime", required = false) String vehicleCheckEndTime,
            @RequestParam(value = "verificationLossCheckStartTime",required = false) String verificationLossCheckStartTime,
            @RequestParam(value = "verificationLossCheckEndTime",required = false) String verificationLossCheckEndTime,
            @RequestParam(value = "repairDepotId", required = false) String repairDepotId,
            @RequestParam(value = "renttype", required = false) Integer renttype,
            @RequestParam(value = "renttypeList", required = false) String renttypeList,
            @RequestParam(value = "noDeductiblesFlag", required = false) Integer noDeductiblesFlag,
            @RequestParam(value = "factOperateTag", required = false) Integer factOperateTag,
            @RequestParam(value = "accidentNo", required = false) String accidentNo,
            @RequestParam(value = "reviewToSelFeeFlag", required = false) Integer reviewToSelFeeFlag,
            HttpServletRequest request) {

        FailCheck failCheck = new FailCheck(taskNo, vehicleNo, vehicleCheckStartTime, vehicleCheckEndTime, renttypeList,
                repairDepotId).invoke();
        if (failCheck.is()) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_STR_CODE, failCheck.getMessage());
        }

        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {

            PageInfo<SettlementReportBO> bo = settlementReportServiceImpl.getSettlementReportInfo(pageNum, pageSize,
                    orgId, repairTypeId, taskNo, vehicleNo, vehicleModelSeq, taskInflowStartTime, taskInflowEndTime,
                    vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime,
                    vehicleCheckStartTime, vehicleCheckEndTime, verificationLossCheckStartTime, verificationLossCheckEndTime,
                    repairDepotId, renttype, failCheck.getRenttypeIntList(), noDeductiblesFlag,factOperateTag, accidentNo,
                    reviewToSelFeeFlag, request);
            vo.setData(bo);
            vo.setMessage(Contants.SUCCESS_INFO);
            log.info("统计报表查询成功");
            return vo;

        } catch (Exception e) {
            log.error("统计报表一览", e);
            vo.setCode(Contants.RETURN_ERROR_STR_CODE);
            vo.setMessage("维修系统异常");
            log.error("维修系统异常:" + e.toString());
            return vo;
        }
    }

    /**
     * 统计子报表一览
     *
     * @param pageNum                页码
     * @param pageSize               每页显示条数
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param renttype               业务状态
     * @param subType                类型
     * @param projectType            项目名称
     * @param isAll                  是否显示总数
     * @return
     */
    @RequestMapping(value = "subSettlementReportInfo", method = RequestMethod.GET)
    public DefaultWebRespVO querySubSettlementReportInfo(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "orgId", required = false) String orgId,
            @RequestParam(value = "repairTypeId", required = false) Integer repairTypeId,
            @RequestParam(value = "taskNo", required = false) String taskNo,
            @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
            @RequestParam(value = "vehicleModelSeq", required = false) String vehicleModelSeq,
            @RequestParam(value = "taskInflowStartTime", required = false) String taskInflowStartTime,
            @RequestParam(value = "taskInflowEndTime", required = false) String taskInflowEndTime,
            @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
            @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
            @RequestParam(value = "vehicleRepairStartTime", required = false) String vehicleRepairStartTime,
            @RequestParam(value = "vehicleRepairEndTime", required = false) String vehicleRepairEndTime,
            @RequestParam(value = "vehicleCheckStartTime", required = false) String vehicleCheckStartTime,
            @RequestParam(value = "vehicleCheckEndTime", required = false) String vehicleCheckEndTime,
            @RequestParam(value = "repairDepotId", required = false) String repairDepotId,
            @RequestParam(value = "renttype", required = false) Integer renttype,
            @RequestParam(value = "renttypeList", required = false) String renttypeList,
            @RequestParam(value = "subType", required = false) Integer subType,
            @RequestParam(value = "projectType", required = false) String projectType,
            @RequestParam(value = "isAll") Integer isAll, HttpServletRequest request) {

        FailCheck failCheck = new FailCheck(taskNo, vehicleNo, vehicleCheckStartTime, vehicleCheckEndTime, renttypeList,
                repairDepotId).invoke();
        if (failCheck.is()) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_STR_CODE, failCheck.getMessage());
        }

        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageInfo<SubSettlementReportBO> bo = settlementReportServiceImpl.getSubSettlementReportInfo(pageNum,
                    pageSize, orgId, repairTypeId, taskNo, vehicleNo, vehicleModelSeq, taskInflowStartTime,
                    taskInflowEndTime, vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime,
                    vehicleRepairEndTime, vehicleCheckStartTime, vehicleCheckEndTime, repairDepotId, renttype,
                    failCheck.getRenttypeIntList(), subType, projectType, isAll, request);
            vo.setData(bo);
            vo.setMessage(Contants.SUCCESS_INFO);
            log.info("统计子报表查询成功");
            return vo;

        } catch (Exception e) {
            log.error("统计子报表一览", e);
            vo.setCode(Contants.RETURN_ERROR_STR_CODE);
            vo.setMessage("维修系统异常");
            log.error("维修系统异常:" + e.toString());
            return vo;
        }
    }

    /**
     * 统计报表导出
     *
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param renttype               业务状态
     * @return
     */
    @RequestMapping(value = "exportSettlementReportInfo", method = RequestMethod.GET)
    public DefaultWebRespVO exportSettlementReportInfo(@RequestParam(value = "orgId", required = false) String orgId,
                                                       @RequestParam(value = "repairTypeId", required = false) Integer repairTypeId,
                                                       @RequestParam(value = "taskNo", required = false) String taskNo,
                                                       @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
                                                       @RequestParam(value = "vehicleModelSeq", required = false) String vehicleModelSeq,
                                                       @RequestParam(value = "taskInflowStartTime", required = false) String taskInflowStartTime,
                                                       @RequestParam(value = "taskInflowEndTime", required = false) String taskInflowEndTime,
                                                       @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
                                                       @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
                                                       @RequestParam(value = "vehicleRepairStartTime", required = false) String vehicleRepairStartTime,
                                                       @RequestParam(value = "vehicleRepairEndTime", required = false) String vehicleRepairEndTime,
                                                       @RequestParam(value = "vehicleCheckStartTime", required = false) String vehicleCheckStartTime,
                                                       @RequestParam(value = "vehicleCheckEndTime", required = false) String vehicleCheckEndTime,
                                                       @RequestParam(value = "verificationLossCheckStartTime",required = false) String verificationLossCheckStartTime,
                                                       @RequestParam(value = "verificationLossCheckEndTime",required = false) String verificationLossCheckEndTime,
                                                       @RequestParam(value = "repairDepotId", required = false) String repairDepotId,
                                                       @RequestParam(value = "renttype", required = false) Integer renttype,
                                                       @RequestParam(value = "renttypeList", required = false) String renttypeList,
                                                       @RequestParam(value = "noDeductiblesFlag", required = false) Integer noDeductiblesFlag,
                                                       @RequestParam(value = "factOperateTag", required = false) Integer factOperateTag,
                                                       @RequestParam(value = "accidentNo", required = false) String accidentNo,
                                                       @RequestParam(value = "reviewToSelFeeFlag", required = false) Integer reviewToSelFeeFlag,
                                                       HttpServletRequest request) {

        FailCheck failCheck = new FailCheck(taskNo, vehicleNo, vehicleCheckStartTime, vehicleCheckEndTime, renttypeList,
                repairDepotId).invoke();
        if (failCheck.is()) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_STR_CODE, failCheck.getMessage());
        }

        ThreadPoolUtils.EXECUTOR.submit(() -> {
            settlementReportServiceImpl.operateExportSettlementReportInfo(orgId, repairTypeId, taskNo, vehicleNo,
                    vehicleModelSeq, taskInflowStartTime, taskInflowEndTime, vehicleReciveStartTime,
                    vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime, vehicleCheckStartTime,
                    vehicleCheckEndTime,verificationLossCheckStartTime, verificationLossCheckEndTime, repairDepotId, renttype, failCheck.getRenttypeIntList(), noDeductiblesFlag, factOperateTag, accidentNo, reviewToSelFeeFlag ,
                    request);
        });

        log.info("统计报表导出成功");
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 统计子报表导出
     *
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param taskNo                 任务编号
     * @param vehicleNo              车牌号
     * @param vehicleModelSeq        车型
     * @param taskInflowStartTime    任务流入开始时间
     * @param taskInflowEndTime      任务流入结束时间
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 维修完成开始时间
     * @param vehicleRepairEndTime   维修完成结束时间
     * @param vehicleCheckStartTime  验收完成开始时间
     * @param vehicleCheckEndTime    验收完成结束时间
     * @param renttype               业务状态
     * @param subType                类型
     * @param projectType            项目名称
     * @return
     */
    @RequestMapping(value = "exportSubSettlementReportInfo", method = RequestMethod.GET)
    public DefaultWebRespVO exportSubSettlementReportInfo(@RequestParam(value = "orgId", required = false) String orgId,
                                                          @RequestParam(value = "repairTypeId", required = false) Integer repairTypeId,
                                                          @RequestParam(value = "taskNo", required = false) String taskNo,
                                                          @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
                                                          @RequestParam(value = "vehicleModelSeq", required = false) String vehicleModelSeq,
                                                          @RequestParam(value = "taskInflowStartTime", required = false) String taskInflowStartTime,
                                                          @RequestParam(value = "taskInflowEndTime", required = false) String taskInflowEndTime,
                                                          @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
                                                          @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
                                                          @RequestParam(value = "vehicleRepairStartTime", required = false) String vehicleRepairStartTime,
                                                          @RequestParam(value = "vehicleRepairEndTime", required = false) String vehicleRepairEndTime,
                                                          @RequestParam(value = "vehicleCheckStartTime", required = false) String vehicleCheckStartTime,
                                                          @RequestParam(value = "vehicleCheckEndTime", required = false) String vehicleCheckEndTime,
                                                          @RequestParam(value = "repairDepotId", required = false) String repairDepotId,
                                                          @RequestParam(value = "renttype", required = false) Integer renttype,
                                                          @RequestParam(value = "renttypeList", required = false) String renttypeList,
                                                          @RequestParam(value = "subType", required = false) Integer subType,
                                                          @RequestParam(value = "projectType", required = false) String projectType, 
                                                          HttpServletRequest request) {

        FailCheck failCheck = new FailCheck(taskNo, vehicleNo, vehicleCheckStartTime, vehicleCheckEndTime, renttypeList,
                repairDepotId).invoke();
        if (failCheck.is()) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_STR_CODE, failCheck.getMessage());
        }

        ThreadPoolUtils.EXECUTOR.submit(() -> {
            settlementReportServiceImpl.operateExportSubSettlementReportInfo(orgId, repairTypeId, taskNo, vehicleNo,
                    vehicleModelSeq, taskInflowStartTime, taskInflowEndTime, vehicleReciveStartTime,
                    vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime, vehicleCheckStartTime,
                    vehicleCheckEndTime, repairDepotId, renttype, failCheck.getRenttypeIntList(), subType, projectType, request);
        });

        log.info("统计子报表导出成功");
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 超时任务统计一览
     *
     * @param pageNum                页码
     * @param pageSize               每页显示条数
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param repairGrade            修理级别
     * @param vehicleNo              车牌号
     * @param repairDepotName        修理厂名称
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 实际修理完成开始时间
     * @param vehicleRepairEndTime   实际修理完成结束时间
     * @param renttype               业务状态
     * @return
     */
    @RequestMapping(value = "timeoutTaskInfo", method = RequestMethod.GET)
    public DefaultWebRespVO getTimeoutTaskList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                               @RequestParam(value = "orgId", required = false) String orgId,
                                               @RequestParam(value = "repairTypeId", required = false) Integer repairTypeId,
                                               @RequestParam(value = "repairGrade", required = false) String repairGrade,
                                               @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
                                               @RequestParam(value = "repairDepotName", required = false) String repairDepotName,
                                               @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
                                               @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
                                               @RequestParam(value = "vehicleRepairStartTime", required = false) String vehicleRepairStartTime,
                                               @RequestParam(value = "vehicleRepairEndTime", required = false) String vehicleRepairEndTime,
                                               @RequestParam(value = "renttype", required = false) Integer renttype,
                                               @RequestParam(value = "renttypeList", required = false) String renttypeList,
                                               @RequestParam(value = "factOperateTag", required = false) Integer factOperateTag, HttpServletRequest request) {

        List<Integer> renttypeIntList = new ArrayList<>();
        if (StringUtils.isNotBlank(renttypeList)) {
            for (String type : renttypeList.split(",")) {
                renttypeIntList.add(Integer.parseInt(type));
            }
        }

        DefaultWebRespVO vo = new DefaultWebRespVO();
        PageInfo<TimeoutTaskStatisticsBO> bo = settlementReportServiceImpl.getTimeoutTaskList(pageNum, pageSize, orgId,
                repairTypeId, repairGrade, vehicleNo, repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime,
                vehicleRepairStartTime, vehicleRepairEndTime, renttype, renttypeIntList, factOperateTag, request);
        vo.setData(bo);
        vo.setMessage(Contants.SUCCESS_INFO);
        log.debug("超时任务统计一览...");

        return vo;
    }

    /**
     * 导出超时任务统计
     *
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param repairGrade            修理级别
     * @param vehicleNo              车牌号
     * @param repairDepotName        修理厂名称
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 实际修理完成开始时间
     * @param vehicleRepairEndTime   实际修理完成结束时间
     * @param renttype               业务状态
     * @return
     */
    @RequestMapping(value = "exportTimeoutTask", method = RequestMethod.GET)
    public DefaultWebRespVO exportTimeoutTask(@RequestParam(value = "orgId", required = false) String orgId,
                                              @RequestParam(value = "repairTypeId", required = false) Integer repairTypeId,
                                              @RequestParam(value = "repairGrade", required = false) String repairGrade,
                                              @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
                                              @RequestParam(value = "repairDepotName", required = false) String repairDepotName,
                                              @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
                                              @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
                                              @RequestParam(value = "vehicleRepairStartTime", required = false) String vehicleRepairStartTime,
                                              @RequestParam(value = "vehicleRepairEndTime", required = false) String vehicleRepairEndTime,
                                              @RequestParam(value = "renttype", required = false) Integer renttype,
                                              @RequestParam(value = "renttypeList", required = false) String renttypeList,
                                              @RequestParam(value = "factOperateTag", required = false) Integer factOperateTag,
                                              HttpServletRequest request,
                                              HttpServletResponse response) {

        List<Integer> renttypeIntList = new ArrayList<>();
        if (StringUtils.isNotBlank(renttypeList)) {
            for (String type : renttypeList.split(",")) {
                renttypeIntList.add(Integer.parseInt(type));
            }
        }

        DefaultServiceRespDTO respDto = settlementReportServiceImpl.exportTimeoutTask(orgId, repairTypeId, repairGrade,
                vehicleNo, repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime,
                vehicleRepairEndTime, renttype, renttypeIntList, factOperateTag, request, response);
        DefaultWebRespVO vo = new DefaultWebRespVO();
        vo.setCode(String.valueOf(respDto.getCode()));
        vo.setMessage(respDto.getMessage());
        log.debug("导出超时任务统计...");

        return vo;
    }

    /**
     * 报价统计一览
     *
     * @param pageNum                        页码
     * @param pageSize                       每页显示条数
     * @param orgId                          车辆运营单位
     * @param repairGrade                    修理级别
     * @param vehicleNo                      车牌号
     * @param repairDepotName                修理厂名称
     * @param vehicleReciveStartTime         车辆接收开始时间
     * @param vehicleReciveEndTime           车辆接收结束时间
     * @param verificationLossCheckStartTime 核价通过开始时间
     * @param verificationLossCheckEndTime   核价通过结束时间
     * @param renttype                       业务状态
     * @param request
     * @return
     */
    @RequestMapping(value = "offerStatisticskInfo", method = RequestMethod.GET)
    public DefaultWebRespVO getOfferStatisticsList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @RequestParam(value = "orgId", required = false) String orgId,
                                                   @RequestParam(value = "repairGrade", required = false) String repairGrade,
                                                   @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
                                                   @RequestParam(value = "repairDepotName", required = false) String repairDepotName,
                                                   @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
                                                   @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
                                                   @RequestParam(value = "verificationLossCheckStartTime", required = false) String verificationLossCheckStartTime,
                                                   @RequestParam(value = "verificationLossCheckEndTime", required = false) String verificationLossCheckEndTime,
                                                   @RequestParam(value = "taskNo", required = false) String taskNo,
                                                   @RequestParam(value = "renttype", required = false) Integer renttype,
                                                   @RequestParam(value = "renttypeList", required = false) String renttypeList,
                                                   @RequestParam(value = "factOperateTag", required = false) Integer factOperateTag, HttpServletRequest request) {

        List<Integer> renttypeIntList = new ArrayList<>();
        if (StringUtils.isNotBlank(renttypeList)) {
            for (String type : renttypeList.split(",")) {
                renttypeIntList.add(Integer.parseInt(type));
            }
        }

        DefaultWebRespVO vo = new DefaultWebRespVO();
        PageInfo<OfferStatisticsBO> bo = settlementReportServiceImpl.getOfferStatisticsList(pageNum, pageSize, orgId,
                repairGrade, vehicleNo, repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime,
                verificationLossCheckStartTime, verificationLossCheckEndTime, taskNo, renttype, renttypeIntList, factOperateTag,
                request);
        vo.setData(bo);
        vo.setMessage(Contants.SUCCESS_INFO);
        log.debug("报价统计一览...");

        return vo;
    }

    /**
     * 导出报价统计
     *
     * @param orgId                          车辆运营单位
     * @param repairGrade                    修理级别
     * @param vehicleNo                      车牌号
     * @param repairDepotName                修理厂名称
     * @param vehicleReciveStartTime         车辆接收开始时间
     * @param vehicleReciveEndTime           车辆接收结束时间
     * @param verificationLossCheckStartTime 核价通过开始时间
     * @param verificationLossCheckEndTime   核价通过结束时间
     * @param request
     * @return
     */
    @RequestMapping(value = "exportOfferStatisticskInfo", method = RequestMethod.GET)
    public DefaultWebRespVO getOfferStatisticsList(@RequestParam(value = "orgId", required = false) String orgId,
                                                   @RequestParam(value = "repairGrade", required = false) String repairGrade,
                                                   @RequestParam(value = "vehicleNo", required = false) String vehicleNo,
                                                   @RequestParam(value = "repairDepotName", required = false) String repairDepotName,
                                                   @RequestParam(value = "vehicleReciveStartTime", required = false) String vehicleReciveStartTime,
                                                   @RequestParam(value = "vehicleReciveEndTime", required = false) String vehicleReciveEndTime,
                                                   @RequestParam(value = "verificationLossCheckStartTime", required = false) String verificationLossCheckStartTime,
                                                   @RequestParam(value = "verificationLossCheckEndTime", required = false) String verificationLossCheckEndTime,
                                                   @RequestParam(value = "taskNo", required = false) String taskNo,
                                                   @RequestParam(value = "renttype", required = false) Integer renttype,
                                                   @RequestParam(value = "renttypeList", required = false) String renttypeList,
                                                   @RequestParam(value = "factOperateTag", required = false) Integer factOperateTag,
                                                   HttpServletRequest request,
                                                   HttpServletResponse response) {

        List<Integer> renttypeIntList = new ArrayList<>();
        if (StringUtils.isNotBlank(renttypeList)) {
            for (String type : renttypeList.split(",")) {
                renttypeIntList.add(Integer.parseInt(type));
            }
        }

        DefaultServiceRespDTO respDto = settlementReportServiceImpl.exportOfferStatistics(orgId, repairGrade, vehicleNo,
                repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime, verificationLossCheckStartTime,
                verificationLossCheckEndTime, taskNo, renttype, renttypeIntList, factOperateTag, request, response);
        DefaultWebRespVO vo = new DefaultWebRespVO();
        vo.setCode(String.valueOf(respDto.getCode()));
        vo.setMessage(respDto.getMessage());
        log.debug("导出报价统计...");
        return vo;
    }

    private class FailCheck {
        private boolean myResult;
        private String taskNo;
        private String vehicleNo;
        private String vehicleCheckStartTime;
        private String vehicleCheckEndTime;
        private String renttypeList;
        private List<Integer> renttypeIntList;
        private String repairDepotId;
        private String[] repairDepotIds;
        private String message;

        public FailCheck(String taskNo, String vehicleNo, String vehicleCheckStartTime, String vehicleCheckEndTime,
                         String renttypeList, String repairDepotId) {
            this.taskNo = taskNo;
            this.vehicleNo = vehicleNo;
            this.vehicleCheckStartTime = vehicleCheckStartTime;
            this.vehicleCheckEndTime = vehicleCheckEndTime;
            this.renttypeList = renttypeList;
            this.repairDepotId = repairDepotId;
        }

        boolean is() {
            return myResult;
        }

        public List<Integer> getRenttypeIntList() {
            return renttypeIntList;
        }

        public String getMessage() {
            return message;
        }

        public FailCheck invoke() {
            renttypeIntList = new ArrayList<>();
            if (StringUtils.isNotBlank(renttypeList)) {
                for (String type : renttypeList.split(",")) {
                    renttypeIntList.add(Integer.parseInt(type));
                }
            }

            if (StringUtils.isNotBlank(repairDepotId)) {
                repairDepotIds = repairDepotId.split(",");
            } else {
                repairDepotIds = new String[]{};
            }

            if (StringUtils.isEmpty(taskNo) && StringUtils.isEmpty(vehicleNo) && repairDepotIds.length != 1) {
                if (StringUtils.isNotEmpty(vehicleCheckStartTime)) {
                    if (StringUtils.isNotEmpty(vehicleCheckEndTime)) {
                        if (ComUtil.AddDay(vehicleCheckStartTime, ComUtil.DATE_TYPE5, YEAR_OF_DAYS)
                                .compareTo(vehicleCheckEndTime) <= 0) {
                            myResult = true;
                            message = "验收完成时间跨度不能超过一年";
                            return this;
                        }
                    } else {
                        if (ComUtil.AddDay(vehicleCheckStartTime, ComUtil.DATE_TYPE5, YEAR_OF_DAYS)
                                .compareTo(vehicleCheckEndTime) <= 0) {
                            myResult = true;
                            message = "验收完成时间跨度不能超过一年";
                            return this;
                        }
                    }
                }
            }

            myResult = false;
            return this;
        }
    }

}
