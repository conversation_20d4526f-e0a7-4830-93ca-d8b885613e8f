package com.evcard.mtc.provider.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class QueryRepairTaskStatusDTO implements Serializable {

    private String vehicleNo;

    private String orderStartTime;

    private String orderEndTime;

    private List<Integer> repairTypeIds;

    private String insuranceReportNumber;

    public QueryRepairTaskStatusDTO() {
    }

    public QueryRepairTaskStatusDTO(String vehicleNo, String orderStartTime, String orderEndTime, List<Integer> repairTypeIds, String insuranceReportNumber) {
        this.vehicleNo = vehicleNo;
        this.orderStartTime = orderStartTime;
        this.orderEndTime = orderEndTime;
        this.repairTypeIds = repairTypeIds;
        this.insuranceReportNumber = insuranceReportNumber;
    }
}
