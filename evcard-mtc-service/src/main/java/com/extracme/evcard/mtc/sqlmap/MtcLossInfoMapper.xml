<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- MtcLossInfoMapper，，对应表mtc_loss_info -->
<mapper namespace="com.extracme.evcard.mtc.dao.MtcLossInfoMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.mtc.model.MtcLossInfo"> 
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
        <result column="veh_certain_code" jdbcType="VARCHAR" property="vehCertainCode" />
        <result column="veh_certain_name" jdbcType="VARCHAR" property="vehCertainName" />
        <result column="veh_group_code" jdbcType="VARCHAR" property="vehGroupCode" />
        <result column="group_name" jdbcType="VARCHAR" property="groupName" />
        <result column="veh_brand_code" jdbcType="VARCHAR" property="vehBrandCode" />
        <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
        <result column="self_config_flag" jdbcType="VARCHAR" property="selfConfigFlag" />
        <result column="salvage_fee" jdbcType="DECIMAL" property="salvageFee" />
        <result column="remnant_fee" jdbcType="DECIMAL" property="remnantFee" />
        <result column="manage_fee" jdbcType="DECIMAL" property="manageFee" />
        <result column="eval_part_sum" jdbcType="DECIMAL" property="evalPartSum" />
        <result column="eval_repair_sum" jdbcType="DECIMAL" property="evalRepairSum" />
        <result column="eval_mate_sum" jdbcType="DECIMAL" property="evalMateSum" />
        <result column="self_pay_sum" jdbcType="DECIMAL" property="selfPaySum" />
        <result column="outer_sum" jdbcType="DECIMAL" property="outerSum" />
        <result column="derogation_sum" jdbcType="DECIMAL" property="derogationSum" />
        <result column="sum_loss_amount" jdbcType="DECIMAL" property="sumLossAmount" />
        <result column="handler_code" jdbcType="VARCHAR" property="handlerCode" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="price_type" jdbcType="VARCHAR" property="priceType" />
        <result column="repair_fac_id" jdbcType="VARCHAR" property="repairFacId" />
        <result column="repair_fac_code" jdbcType="VARCHAR" property="repairFacCode" />
        <result column="repair_fac_type" jdbcType="VARCHAR" property="repairFacType" />
        <result column="factory_qualification" jdbcType="VARCHAR" property="factoryQualification" />
        <result column="repair_fac_phone" jdbcType="VARCHAR" property="repairFacPhone" />
        <result column="repair_fac_name" jdbcType="VARCHAR" property="repairFacName" />
        <result column="vin_no" jdbcType="VARCHAR" property="vinNo" />
        <result column="engine_no" jdbcType="VARCHAR" property="engineNo" />
        <result column="plate_no" jdbcType="VARCHAR" property="plateNo" />
        <result column="enrol_date" jdbcType="VARCHAR" property="enrolDate" />
        <result column="self_esti_flag" jdbcType="VARCHAR" property="selfEstiFlag" />
        <result column="self_approve_flag" jdbcType="VARCHAR" property="selfApproveFlag" />
        <result column="insurance_code" jdbcType="VARCHAR" property="insuranceCode" />
        <result column="insurance_name" jdbcType="VARCHAR" property="insuranceName" />
        <result column="mix_code" jdbcType="VARCHAR" property="mixCode" />
        <result column="vehicle_setting_mode" jdbcType="VARCHAR" property="vehicleSettingMode" />
        <result column="model_match_flag" jdbcType="VARCHAR" property="modelMatchFlag" />
        <result column="eval_type_code" jdbcType="VARCHAR" property="evalTypeCode" />
        <result column="accident_cause_code" jdbcType="VARCHAR" property="accidentCauseCode" />
        <result column="clm_tms" jdbcType="VARCHAR" property="clmTms" />
        <result column="all_lose_sum" jdbcType="DECIMAL" property="allLoseSum" />
        <result column="all_lose_remains_sum" jdbcType="DECIMAL" property="allLoseRemainsSum" />
        <result column="all_lose_salv_sum" jdbcType="DECIMAL" property="allLoseSalvSum" />
        <result column="all_lose_total_sum" jdbcType="DECIMAL" property="allLoseTotalSum" />
        <result column="part_discount_percent" jdbcType="DECIMAL" property="partDiscountPercent" />
        <result column="engine_type" jdbcType="VARCHAR" property="engineType" />
        <result column="fuel_type" jdbcType="VARCHAR" property="fuelType" />
        <result column="vehicle_origin" jdbcType="VARCHAR" property="vehicleOrigin" />
        <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType" />
        <result column="audit_salvage_fee" jdbcType="DECIMAL" property="auditSalvageFee" />
        <result column="audit_remnant_fee" jdbcType="DECIMAL" property="auditRemnantFee" />
        <result column="audit_part_sum" jdbcType="DECIMAL" property="auditPartSum" />
        <result column="audit_repiar_sum" jdbcType="DECIMAL" property="auditRepiarSum" />
        <result column="audit_mate_sum" jdbcType="DECIMAL" property="auditMateSum" />
        <result column="total_manage_sum" jdbcType="DECIMAL" property="totalManageSum" />
        <result column="audit_self_pay_sum" jdbcType="DECIMAL" property="auditSelfPaySum" />
        <result column="audit_outer_sum" jdbcType="DECIMAL" property="auditOuterSum" />
        <result column="audit_derogation_sum" jdbcType="DECIMAL" property="auditDerogationSum" />
        <result column="audit_handler_code" jdbcType="VARCHAR" property="auditHandlerCode" />
        <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
        <result column="total_sum" jdbcType="DECIMAL" property="totalSum" />
        <result column="audit_all_lose_sum" jdbcType="DECIMAL" property="auditAllLoseSum" />
        <result column="audit_all_lose_remains_sum" jdbcType="DECIMAL" property="auditAllLoseRemainsSum" />
        <result column="audit_all_lose_salv_sum" jdbcType="DECIMAL" property="auditAllLoseSalvSum" />
        <result column="audit_all_lose_total_sum" jdbcType="DECIMAL" property="auditAllLoseTotalSum" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="misc_Desc" jdbcType="VARCHAR" property="miscDesc" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
        <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
        <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    </resultMap>
    
    <!--数据列-->
    <sql id="Base_Column_List" >
            id,
            task_no,
            veh_certain_code,
            veh_certain_name,
            veh_group_code,
            group_name,
            veh_brand_code,
            brand_name,
            self_config_flag,
            salvage_fee,
            remnant_fee,
            manage_fee,
            eval_part_sum,
            eval_repair_sum,
            eval_mate_sum,
            self_pay_sum,
            outer_sum,
            derogation_sum,
            sum_loss_amount,
            handler_code,
            remark,
            price_type,
            repair_fac_id,
            repair_fac_code,
            repair_fac_type,
            factory_qualification,
            repair_fac_phone,
            repair_fac_name,
            vin_no,
            engine_no,
            plate_no,
            enrol_date,
            self_esti_flag,
            self_approve_flag,
            insurance_code,
            insurance_name,
            mix_code,
            vehicle_setting_mode,
            model_match_flag,
            eval_type_code,
            accident_cause_code,
            clm_tms,
            all_lose_sum,
            all_lose_remains_sum,
            all_lose_salv_sum,
            all_lose_total_sum,
            part_discount_percent,
            engine_type,
            fuel_type,
            vehicle_origin,
            vehicle_type,
            audit_salvage_fee,
            audit_remnant_fee,
            audit_part_sum,
            audit_repiar_sum,
            audit_mate_sum,
            total_manage_sum,
            audit_self_pay_sum,
            audit_outer_sum,
            audit_derogation_sum,
            audit_handler_code,
            audit_remark,
            total_sum,
            audit_all_lose_sum,
            audit_all_lose_remains_sum,
            audit_all_lose_salv_sum,
            audit_all_lose_total_sum,
            status,
            misc_Desc,
            create_time,
            create_oper_id,
            create_oper_name,
            update_time,
            update_oper_id,
            update_oper_name
    </sql>
    
    
    <!-- 保存数据 -->
    <insert id="save" parameterType="com.extracme.evcard.mtc.model.MtcLossInfo">
        insert into ${mtcSchema}.mtc_loss_info (
            task_no,
            veh_certain_code,
            veh_certain_name,
            veh_group_code,
            group_name,
            veh_brand_code,
            brand_name,
            self_config_flag,
            salvage_fee,
            remnant_fee,
            manage_fee,
            eval_part_sum,
            eval_repair_sum,
            eval_mate_sum,
            self_pay_sum,
            outer_sum,
            derogation_sum,
            sum_loss_amount,
            handler_code,
            remark,
            price_type,
            repair_fac_id,
            repair_fac_code,
            repair_fac_type,
            factory_qualification,
            repair_fac_phone,
            repair_fac_name,
            vin_no,
            engine_no,
            plate_no,
            enrol_date,
            self_esti_flag,
            self_approve_flag,
            insurance_code,
            insurance_name,
            mix_code,
            vehicle_setting_mode,
            model_match_flag,
            eval_type_code,
            accident_cause_code,
            clm_tms,
            all_lose_sum,
            all_lose_remains_sum,
            all_lose_salv_sum,
            all_lose_total_sum,
            part_discount_percent,
            engine_type,
            fuel_type,
            vehicle_origin,
            vehicle_type,
            audit_salvage_fee,
            audit_remnant_fee,
            audit_part_sum,
            audit_repiar_sum,
            audit_mate_sum,
            total_manage_sum,
            audit_self_pay_sum,
            audit_outer_sum,
            audit_derogation_sum,
            audit_handler_code,
            audit_remark,
            total_sum,
            audit_all_lose_sum,
            audit_all_lose_remains_sum,
            audit_all_lose_salv_sum,
            audit_all_lose_total_sum,
            create_time,
            create_oper_id,
            create_oper_name,
            update_time,
            update_oper_id,
            update_oper_name
        ) values (
            #{taskNo,jdbcType=VARCHAR},
            #{vehCertainCode,jdbcType=VARCHAR},
            #{vehCertainName,jdbcType=VARCHAR},
            #{vehGroupCode,jdbcType=VARCHAR},
            #{groupName,jdbcType=VARCHAR},
            #{vehBrandCode,jdbcType=VARCHAR},
            #{brandName,jdbcType=VARCHAR},
            #{selfConfigFlag,jdbcType=VARCHAR},
            #{salvageFee,jdbcType=DECIMAL},
            #{remnantFee,jdbcType=DECIMAL},
            #{manageFee,jdbcType=DECIMAL},
            #{evalPartSum,jdbcType=DECIMAL},
            #{evalRepairSum,jdbcType=DECIMAL},
            #{evalMateSum,jdbcType=DECIMAL},
            #{selfPaySum,jdbcType=DECIMAL},
            #{outerSum,jdbcType=DECIMAL},
            #{derogationSum,jdbcType=DECIMAL},
            #{sumLossAmount,jdbcType=DECIMAL},
            #{handlerCode,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},
            #{priceType,jdbcType=VARCHAR},
            #{repairFacId,jdbcType=VARCHAR},
            #{repairFacCode,jdbcType=VARCHAR},
            #{repairFacType,jdbcType=VARCHAR},
            #{factoryQualification,jdbcType=VARCHAR},
            #{repairFacPhone,jdbcType=VARCHAR},
            #{repairFacName,jdbcType=VARCHAR},
            #{vinNo,jdbcType=VARCHAR},
            #{engineNo,jdbcType=VARCHAR},
            #{plateNo,jdbcType=VARCHAR},
            #{enrolDate,jdbcType=VARCHAR},
            #{selfEstiFlag,jdbcType=VARCHAR},
            #{selfApproveFlag,jdbcType=VARCHAR},
            #{insuranceCode,jdbcType=VARCHAR},
            #{insuranceName,jdbcType=VARCHAR},
            #{mixCode,jdbcType=VARCHAR},
            #{vehicleSettingMode,jdbcType=VARCHAR},
            #{modelMatchFlag,jdbcType=VARCHAR},
            #{evalTypeCode,jdbcType=VARCHAR},
            #{accidentCauseCode,jdbcType=VARCHAR},
            #{clmTms,jdbcType=VARCHAR},
            #{allLoseSum,jdbcType=DECIMAL},
            #{allLoseRemainsSum,jdbcType=DECIMAL},
            #{allLoseSalvSum,jdbcType=DECIMAL},
            #{allLoseTotalSum,jdbcType=DECIMAL},
            #{partDiscountPercent,jdbcType=DECIMAL},
            #{engineType,jdbcType=VARCHAR},
            #{fuelType,jdbcType=VARCHAR},
            #{vehicleOrigin,jdbcType=VARCHAR},
            #{vehicleType,jdbcType=VARCHAR},
            #{auditSalvageFee,jdbcType=DECIMAL},
            #{auditRemnantFee,jdbcType=DECIMAL},
            #{auditPartSum,jdbcType=DECIMAL},
            #{auditRepiarSum,jdbcType=DECIMAL},
            #{auditMateSum,jdbcType=DECIMAL},
            #{totalManageSum,jdbcType=DECIMAL},
            #{auditSelfPaySum,jdbcType=DECIMAL},
            #{auditOuterSum,jdbcType=DECIMAL},
            #{auditDerogationSum,jdbcType=DECIMAL},
            #{auditHandlerCode,jdbcType=VARCHAR},
            #{auditRemark,jdbcType=VARCHAR},
            #{totalSum,jdbcType=DECIMAL},
            #{auditAllLoseSum,jdbcType=DECIMAL},
            #{auditAllLoseRemainsSum,jdbcType=DECIMAL},
            #{auditAllLoseSalvSum,jdbcType=DECIMAL},
            #{auditAllLoseTotalSum,jdbcType=DECIMAL},
            #{createTime,jdbcType=TIMESTAMP},
            #{createOperId,jdbcType=BIGINT},
            #{createOperName,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{createOperId,jdbcType=BIGINT},
            #{createOperName,jdbcType=VARCHAR}
        )
    </insert>
    
    <!-- 根据主键取得数据 -->
    <select id="selectById" parameterType="java.util.ArrayList" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
         from ${mtcSchema}.mtc_loss_info 
         where 
         id = #{id} 
         
    </select>
    
    <!-- 根据主键删除数据 -->
    <delete id="deleteById" parameterType="java.util.ArrayList">
        delete from ${mtcSchema}.mtc_loss_info 
        where 
        id = #{id,jdbcType=BIGINT} 
        
    </delete>

	<!-- 更新数据 -->
    <update id="updateByIdSelective" parameterType="com.extracme.evcard.mtc.model.MtcLossInfo">
        update ${mtcSchema}.mtc_loss_info
        <set>
                        <if test="entityMap.vehCertainCode != null">
                                veh_certain_code = #{entityMap.vehCertainCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehCertainName != null">
                                veh_certain_name = #{entityMap.vehCertainName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehGroupCode != null">
                                veh_group_code = #{entityMap.vehGroupCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.groupName != null">
                                group_name = #{entityMap.groupName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehBrandCode != null">
                                veh_brand_code = #{entityMap.vehBrandCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.brandName != null">
                                brand_name = #{entityMap.brandName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.selfConfigFlag != null">
                                self_config_flag = #{entityMap.selfConfigFlag,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.salvageFee != null">
                                salvage_fee = #{entityMap.salvageFee,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.remnantFee != null">
                                remnant_fee = #{entityMap.remnantFee,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.manageFee != null">
                                manage_fee = #{entityMap.manageFee,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.evalPartSum != null">
                                eval_part_sum = #{entityMap.evalPartSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.evalRepairSum != null">
                                eval_repair_sum = #{entityMap.evalRepairSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.evalMateSum != null">
                                eval_mate_sum = #{entityMap.evalMateSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.selfPaySum != null">
                                self_pay_sum = #{entityMap.selfPaySum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.outerSum != null">
                                outer_sum = #{entityMap.outerSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.derogationSum != null">
                                derogation_sum = #{entityMap.derogationSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.sumLossAmount != null">
                                sum_loss_amount = #{entityMap.sumLossAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.handlerCode != null">
                                handler_code = #{entityMap.handlerCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.remark != null">
                                remark = #{entityMap.remark,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.priceType != null">
                                price_type = #{entityMap.priceType,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairFacId != null">
                                repair_fac_id = #{entityMap.repairFacId,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairFacCode != null">
                                repair_fac_code = #{entityMap.repairFacCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairFacType != null">
                                repair_fac_type = #{entityMap.repairFacType,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.factoryQualification != null">
                                factory_qualification = #{entityMap.factoryQualification,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairFacPhone != null">
                                repair_fac_phone = #{entityMap.repairFacPhone,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairFacName != null">
                                repair_fac_name = #{entityMap.repairFacName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vinNo != null">
                                vin_no = #{entityMap.vinNo,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.engineNo != null">
                                engine_no = #{entityMap.engineNo,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.plateNo != null">
                                plate_no = #{entityMap.plateNo,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.enrolDate != null">
                                enrol_date = #{entityMap.enrolDate,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.selfEstiFlag != null">
                                self_esti_flag = #{entityMap.selfEstiFlag,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.selfApproveFlag != null">
                                self_approve_flag = #{entityMap.selfApproveFlag,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.insuranceCode != null">
                                insurance_code = #{entityMap.insuranceCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.insuranceName != null">
                                insurance_name = #{entityMap.insuranceName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.mixCode != null">
                                mix_code = #{entityMap.mixCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehicleSettingMode != null">
                                vehicle_setting_mode = #{entityMap.vehicleSettingMode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.modelMatchFlag != null">
                                model_match_flag = #{entityMap.modelMatchFlag,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.evalTypeCode != null">
                                eval_type_code = #{entityMap.evalTypeCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.accidentCauseCode != null">
                                accident_cause_code = #{entityMap.accidentCauseCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.clmTms != null">
                                clm_tms = #{entityMap.clmTms,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.allLoseSum != null">
                                all_lose_sum = #{entityMap.allLoseSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.allLoseRemainsSum != null">
                                all_lose_remains_sum = #{entityMap.allLoseRemainsSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.allLoseSalvSum != null">
                                all_lose_salv_sum = #{entityMap.allLoseSalvSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.allLoseTotalSum != null">
                                all_lose_total_sum = #{entityMap.allLoseTotalSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.partDiscountPercent != null">
                                part_discount_percent = #{entityMap.partDiscountPercent,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.engineType != null">
                                engine_type = #{entityMap.engineType,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.fuelType != null">
                                fuel_type = #{entityMap.fuelType,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehicleOrigin != null">
                                vehicle_origin = #{entityMap.vehicleOrigin,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehicleType != null">
                                vehicle_type = #{entityMap.vehicleType,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.auditSalvageFee != null">
                                audit_salvage_fee = #{entityMap.auditSalvageFee,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditRemnantFee != null">
                                audit_remnant_fee = #{entityMap.auditRemnantFee,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditPartSum != null">
                                audit_part_sum = #{entityMap.auditPartSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditRepiarSum != null">
                                audit_repiar_sum = #{entityMap.auditRepiarSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditMateSum != null">
                                audit_mate_sum = #{entityMap.auditMateSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.totalManageSum != null">
                                total_manage_sum = #{entityMap.totalManageSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditSelfPaySum != null">
                                audit_self_pay_sum = #{entityMap.auditSelfPaySum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditOuterSum != null">
                                audit_outer_sum = #{entityMap.auditOuterSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditDerogationSum != null">
                                audit_derogation_sum = #{entityMap.auditDerogationSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditHandlerCode != null">
                                audit_handler_code = #{entityMap.auditHandlerCode,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.auditRemark != null">
                                audit_remark = #{entityMap.auditRemark,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.totalSum != null">
                                total_sum = #{entityMap.totalSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditAllLoseSum != null">
                                audit_all_lose_sum = #{entityMap.auditAllLoseSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditAllLoseRemainsSum != null">
                                audit_all_lose_remains_sum = #{entityMap.auditAllLoseRemainsSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditAllLoseSalvSum != null">
                                audit_all_lose_salv_sum = #{entityMap.auditAllLoseSalvSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.auditAllLoseTotalSum != null">
                                audit_all_lose_total_sum = #{entityMap.auditAllLoseTotalSum,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.status != null">
                                status = #{entityMap.status,jdbcType=INTEGER},
                        </if>
                        <if test="entityMap.miscDesc != null">
                                misc_Desc = #{entityMap.miscDesc,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.createOperId != null">
                                create_oper_id = #{entityMap.createOperId,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.createOperName != null">
                                create_oper_name = #{entityMap.createOperName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.updateTime != null">
                                update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.updateOperId != null">
                                update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.updateOperName != null">
                                update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR}
                        </if>
        </set>
        where
            task_no = #{entityMap.taskNo,jdbcType=VARCHAR} and status = 1
    </update>
    
    <!-- 批量插入 -->
     <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${mtcSchema}.mtc_loss_info (
               task_no,
               veh_certain_code,
               veh_certain_name,
               veh_group_code,
               group_name,
               veh_brand_code,
               brand_name,
               self_config_flag,
               salvage_fee,
               remnant_fee,
               manage_fee,
               eval_part_sum,
               eval_repair_sum,
               eval_mate_sum,
               self_pay_sum,
               outer_sum,
               derogation_sum,
               sum_loss_amount,
               handler_code,
               remark,
               price_type,
               repair_fac_id,
               repair_fac_code,
               repair_fac_type,
               factory_qualification,
               repair_fac_phone,
               repair_fac_name,
               vin_no,
               engine_no,
               plate_no,
               enrol_date,
               self_esti_flag,
               self_approve_flag,
               insurance_code,
               insurance_name,
               mix_code,
               vehicle_setting_mode,
               model_match_flag,
               eval_type_code,
               accident_cause_code,
               clm_tms,
               all_lose_sum,
               all_lose_remains_sum,
               all_lose_salv_sum,
               all_lose_total_sum,
               part_discount_percent,
               engine_type,
               fuel_type,
               vehicle_origin,
               vehicle_type,
               audit_salvage_fee,
               audit_remnant_fee,
               audit_part_sum,
               audit_repiar_sum,
               audit_mate_sum,
               total_manage_sum,
               audit_self_pay_sum,
               audit_outer_sum,
               audit_derogation_sum,
               audit_handler_code,
               audit_remark,
               total_sum,
               audit_all_lose_sum,
               audit_all_lose_remains_sum,
               audit_all_lose_salv_sum,
               audit_all_lose_total_sum,
               create_time,
               create_oper_id,
               create_oper_name,
               update_time,
               update_oper_id,
               update_oper_name
         ) VALUES
         <foreach collection="list" item="item" index="index" separator=",">
             (
                  #{item.taskNo,jdbcType=VARCHAR},
                  #{item.vehCertainCode,jdbcType=VARCHAR},
                  #{item.vehCertainName,jdbcType=VARCHAR},
                  #{item.vehGroupCode,jdbcType=VARCHAR},
                  #{item.groupName,jdbcType=VARCHAR},
                  #{item.vehBrandCode,jdbcType=VARCHAR},
                  #{item.brandName,jdbcType=VARCHAR},
                  #{item.selfConfigFlag,jdbcType=VARCHAR},
                  #{item.salvageFee,jdbcType=DECIMAL},
                  #{item.remnantFee,jdbcType=DECIMAL},
                  #{item.manageFee,jdbcType=DECIMAL},
                  #{item.evalPartSum,jdbcType=DECIMAL},
                  #{item.evalRepairSum,jdbcType=DECIMAL},
                  #{item.evalMateSum,jdbcType=DECIMAL},
                  #{item.selfPaySum,jdbcType=DECIMAL},
                  #{item.outerSum,jdbcType=DECIMAL},
                  #{item.derogationSum,jdbcType=DECIMAL},
                  #{item.sumLossAmount,jdbcType=DECIMAL},
                  #{item.handlerCode,jdbcType=VARCHAR},
                  #{item.remark,jdbcType=VARCHAR},
                  #{item.priceType,jdbcType=VARCHAR},
                  #{item.repairFacId,jdbcType=VARCHAR},
                  #{item.repairFacCode,jdbcType=VARCHAR},
                  #{item.repairFacType,jdbcType=VARCHAR},
                  #{item.factoryQualification,jdbcType=VARCHAR},
                  #{item.repairFacPhone,jdbcType=VARCHAR},
                  #{item.repairFacName,jdbcType=VARCHAR},
                  #{item.vinNo,jdbcType=VARCHAR},
                  #{item.engineNo,jdbcType=VARCHAR},
                  #{item.plateNo,jdbcType=VARCHAR},
                  #{item.enrolDate,jdbcType=VARCHAR},
                  #{item.selfEstiFlag,jdbcType=VARCHAR},
                  #{item.selfApproveFlag,jdbcType=VARCHAR},
                  #{item.insuranceCode,jdbcType=VARCHAR},
                  #{item.insuranceName,jdbcType=VARCHAR},
                  #{item.mixCode,jdbcType=VARCHAR},
                  #{item.vehicleSettingMode,jdbcType=VARCHAR},
                  #{item.modelMatchFlag,jdbcType=VARCHAR},
                  #{item.evalTypeCode,jdbcType=VARCHAR},
                  #{item.accidentCauseCode,jdbcType=VARCHAR},
                  #{item.clmTms,jdbcType=VARCHAR},
                  #{item.allLoseSum,jdbcType=DECIMAL},
                  #{item.allLoseRemainsSum,jdbcType=DECIMAL},
                  #{item.allLoseSalvSum,jdbcType=DECIMAL},
                  #{item.allLoseTotalSum,jdbcType=DECIMAL},
                  #{item.partDiscountPercent,jdbcType=DECIMAL},
                  #{item.engineType,jdbcType=VARCHAR},
                  #{item.fuelType,jdbcType=VARCHAR},
                  #{item.vehicleOrigin,jdbcType=VARCHAR},
                  #{item.vehicleType,jdbcType=VARCHAR},
                  #{item.auditSalvageFee,jdbcType=DECIMAL},
                  #{item.auditRemnantFee,jdbcType=DECIMAL},
                  #{item.auditPartSum,jdbcType=DECIMAL},
                  #{item.auditRepiarSum,jdbcType=DECIMAL},
                  #{item.auditMateSum,jdbcType=DECIMAL},
                  #{item.totalManageSum,jdbcType=DECIMAL},
                  #{item.auditSelfPaySum,jdbcType=DECIMAL},
                  #{item.auditOuterSum,jdbcType=DECIMAL},
                  #{item.auditDerogationSum,jdbcType=DECIMAL},
                  #{item.auditHandlerCode,jdbcType=VARCHAR},
                  #{item.auditRemark,jdbcType=VARCHAR},
                  #{item.totalSum,jdbcType=DECIMAL},
                  #{item.auditAllLoseSum,jdbcType=DECIMAL},
                  #{item.auditAllLoseRemainsSum,jdbcType=DECIMAL},
                  #{item.auditAllLoseSalvSum,jdbcType=DECIMAL},
                  #{item.auditAllLoseTotalSum,jdbcType=DECIMAL},
                  #{item.createTime,jdbcType=TIMESTAMP},
                  #{item.createOperId,jdbcType=BIGINT},
                  #{item.createOperName,jdbcType=VARCHAR},
                  #{item.createTime,jdbcType=TIMESTAMP},
                  #{item.createOperId,jdbcType=BIGINT},
                  #{item.createOperName,jdbcType=VARCHAR}
             )
        </foreach>
    </insert>
    
     <!-- 单条逻辑删除 -->
     <update id="logicalSelectById">
        update ${mtcSchema}.mtc_loss_info set status = 0 , update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR} 
        where 
        id = #{id,jdbcType=BIGINT} 
		
        and status = 1
     </update>
     
     <!-- 批量逻辑删除 -->
    <update id="batchLogicalSelectById" parameterType="java.util.List">
        update ${mtcSchema}.mtc_loss_info  set status = 0 , update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR}  
        where 
        id in  
         <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">  
            ${item}
         </foreach>
         
        and status = 1
     </update>

    <select id="queryMtcLossList" parameterType="com.extracme.evcard.mtc.model.MtcLossInfo" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            ${mtcSchema}.mtc_loss_info
        <where>
            <if test="taskNo != null and taskNo != ''">
                and task_no = #{taskNo,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <update id="updateStatus" parameterType="string">
        update
            ${mtcSchema}.mtc_loss_info
        set
            status = 0,
            update_time = sysdate()
        where
            task_no = #{taskNo,jdbcType=VARCHAR} and status = 1
    </update>

    <update id="updateAuditHandlerCode" parameterType="string">
        update
            ${mtcSchema}.mtc_loss_info
        set
        audit_handler_code = '00',
        update_time = sysdate()
        where
            task_no = #{taskNo,jdbcType=VARCHAR} and status = 1
    </update>

    <delete id="deleteByTaskNo" parameterType="string">
        delete from
            ${mtcSchema}.mtc_loss_info
        where
            task_no = #{taskNo,jdbcType=VARCHAR} and status = 1
    </delete>
</mapper>