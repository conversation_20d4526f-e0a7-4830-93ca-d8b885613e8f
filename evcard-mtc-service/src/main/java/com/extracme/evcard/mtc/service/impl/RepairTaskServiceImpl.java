package com.extracme.evcard.mtc.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.evcard.mtc.provider.dto.RepairDetailDTO;
import com.evcard.mtc.provider.dto.RepairScheduleDTO;
import com.evcard.mtc.provider.dto.RepairTaskViewDTO;
import com.evcard.mtc.provider.service.IMtcRepairTaskService;
import com.extracme.evcard.bdp.dto.*;
import com.extracme.evcard.bdp.dto.innerrent.BaseResultBean;
import com.extracme.evcard.bdp.service.IBdpMtcTaskInfoService;
import com.extracme.evcard.bdp.service.IBdpVehicleService;
import com.extracme.evcard.bdp.service.ILongRentReplaceVehicleService;
import com.extracme.evcard.mtc.bo.*;
import com.extracme.evcard.mtc.common.DateUtil;
import com.extracme.evcard.mtc.common.*;
import com.extracme.evcard.mtc.configuration.BaiYangConfiguration;
import com.extracme.evcard.mtc.dao.*;
import com.extracme.evcard.mtc.dto.VehicleHistoryInfoDTO;
import com.extracme.evcard.mtc.dto.*;
import com.extracme.evcard.mtc.dto.order.AbstractOrderInfoDTO;
import com.extracme.evcard.mtc.dto.order.OrderInfoDTO;
import com.extracme.evcard.mtc.enums.ByApproveEnum;
import com.extracme.evcard.mtc.enums.CurrentTacheStatusEnum;
import com.extracme.evcard.mtc.enums.RepairTypeEnum;
import com.extracme.evcard.mtc.listener.event.BaseRepairEvent;
import com.extracme.evcard.mtc.listener.subject.VehicleJoinSubject;
import com.extracme.evcard.mtc.listener.subject.VehicleMaintenanceCheckSubject;
import com.extracme.evcard.mtc.model.*;
import com.extracme.evcard.mtc.model.md.CurrentUser;
import com.extracme.evcard.mtc.model.md.EditFeeRecordReq;
import com.extracme.evcard.mtc.rpc.MdRpc;
import com.extracme.evcard.mtc.service.*;
import com.extracme.evcard.mtc.util.ApolloPropertyUtils;
import com.extracme.evcard.mtc.util.ByApproveUtil;
import com.extracme.evcard.mtc.util.HttpClientUtils;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.rent.dto.IrtRentReplaceVehicleTask;
import com.extracme.evcard.rpc.rent.entity.IrtRentReplaceVehicleTaskBo;
import com.extracme.evcard.rpc.rent.service.IIrtRentReplaceVehicleTaskService;
import com.extracme.evcard.sso.dto.SsoUserBaseInfoDto;
import com.extracme.evcard.sso.service.SsoUserService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RepairTaskServiceImpl implements RepairTaskService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApolloPropertyUtils apolloPropertyUtils;

    @Resource
    private RepairTaskMapper repairTaskMapper;

    @Resource
    private MtcOperatorLogMapper mtcOperatorLogMapper;
    @Resource
    private MtcManualLogMapper mtcManualLogMapper;
    @Resource
    VehicleRepairPicMapper vehicleRepairPicMapper;
    @Resource
    private RepairDepotInfoMapper repairDepotInfoMapper;
    @Resource
    private VehicleAdvanceCheckTaskMapper vehicleAdvanceCheckTaskMapper;
    @Resource
    ReplaceItemDetailMapper replaceItemDetailMapper;
    @Resource
    RepairItemDetailMapper repairItemDetailMapper;
    @Resource
    private OrgInfoMapper orgInfoMapper;
    @Resource
    private MtcManualTaskMapper mtcManualTaskMapper;
    @Resource
    private OSSUtil ossUtil;

    @Resource
    private IBdpMtcTaskInfoService bdpMtcTaskInfoService;

    @Resource
    private IBdpVehicleService bdpVehicleService;

    @Autowired
    private IBfcCostService bfcCostService;
    @Resource
    private MtcBudgetManagementMapper mtcBudgetManagementMapper;

    @Autowired
    private VehicleMaintenanceCheckSubject vehicleMaintenanceCheckSubject;

    @Autowired
    private VehicleJoinSubject vehicleJoinSubject;

    @Autowired
    private IMessagepushServ iMessagepushServ;

    @Autowired
    private ILongRentReplaceVehicleService rentReplaceVehicleService;

    @Autowired
    private IIrtRentReplaceVehicleTaskService iIrtRentReplaceVehicleTaskService;

    @Resource
    private IMtcRepairTaskService mtcRepairTaskService;

    @Autowired
    private ManualUtils manualUtils;

    @Resource
    private MdRpc mdRpc;

    @Resource
    private NewAndOldOrderConversionService newAndOldOrderConversionService;

    @Resource
    private IMtcRepairItemCheckInfoService mtcRepairItemCheckInfoService;

    @Resource
    private MtcLossFitInfoMapper mtcLossFitInfoMapper;

    @Resource
    private MtcLossRepairInfoMapper mtcLossRepairInfoMapper;

    @Resource
    private MtcRepairTaskLeavingFactoryMapper mtcRepairTaskLeavingFactoryMapper;

    @Resource
    private InsurancePreReviewService insurancePreReviewService;

    @Resource
    private RepairTaskService repairTaskService;

    @Resource
    private SsoUserService ssoUserService;

    @Resource
    private ReplaceVehicleUtils replaceVehicleUtils;

    @Autowired
    private BaiYangConfiguration baiYangConfiguration;

    @Resource
    private MtcRepairAdjustApproveMapper mtcRepairAdjustApproveMapper;

    @Autowired
    private IRepairTaskAdjustApproveService repairTaskAdjustApproveService;

    @Override
    @Transactional
    public DefaultServiceRespDTO addRepairTaskInfo(RepairTaskBO repairTaskBO, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        RepairTaskModelBO repairTaskModel = new RepairTaskModelBO();
        BeanCopyUtils.copyProperties(repairTaskBO, repairTaskModel);
        log.debug("RepairTaskServiceImpl 任务编号:" + repairTaskBO.getDispatchTaskSeq());
        log.debug("RepairTaskServiceImpl 维修类型输入:" + repairTaskBO.getRepairTypeId());
        log.debug("RepairTaskServiceImpl 维修级别输入:" + repairTaskBO.getRepairGrade());
        log.debug("RepairTaskServiceImpl 车牌号:" + repairTaskBO.getVehicleNo());
        log.debug("RepairTaskServiceImpl 车型ID :" + repairTaskBO.getVehicleModelSeq());
        log.debug("RepairTaskServiceImpl 车型名称:" + repairTaskBO.getVehicleModelInfo());
        log.debug("RepairTaskServiceImpl 车辆所属组织机构ID:" + repairTaskBO.getOrgId());
        log.debug("RepairTaskServiceImpl 车辆所属组织机构名称:" + repairTaskBO.getOrgName());
        log.debug("RepairTaskServiceImpl 车架号 :" + repairTaskBO.getVin());
        log.debug("RepairTaskServiceImpl 修理厂ID:" + repairTaskBO.getRepairDepotId());
        log.debug("RepairTaskServiceImpl 修理厂名称:" + repairTaskBO.getRepairDepotName());
        log.debug("RepairTaskServiceImpl 保险公司所属:" + repairTaskBO.getInsuranceCompanyName());
        log.debug("RepairTaskServiceImpl 总里程数:" + repairTaskBO.getTotalMileage());
        log.debug("RepairTaskServiceImpl 任务创建日期:" + repairTaskBO.getTaskCreateTime());
        log.debug("RepairTaskServiceImpl 驾驶人姓名:" + repairTaskBO.getDriverName());
        // 条件check
        if (StringUtils.isBlank(repairTaskBO.getDispatchTaskSeq())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务编号不能为空");
            return vo;
        }

        RepairTask repairTask = repairTaskMapper.selectByTaskNo(repairTaskBO.getDispatchTaskSeq());
        if (repairTask != null) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务编号已存在");
            return vo;
        }


        if (StringUtils.isBlank(repairTaskBO.getRepairTypeId())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("维修类型输入(1:事故维修 2:自费维修 3:车辆保养 4:轮胎任务 6:常规保养 7:终端维修 9:短租包修)，其他违法");
            return vo;
        } else {
            if (!repairTaskBO.getRepairTypeId().equals("1") && !repairTaskBO.getRepairTypeId().equals("2")
                    && !repairTaskBO.getRepairTypeId().equals("3") && !repairTaskBO.getRepairTypeId().equals("4")
                    && !repairTaskBO.getRepairTypeId().equals("6") && !repairTaskBO.getRepairTypeId().equals("7")
                    && !repairTaskBO.getRepairTypeId().equals("9")) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("维修类型输入(1:事故维修 2:自费维修 3:车辆保养 4:轮胎任务 6:常规保养 7:终端维修 9:短租包修)，其他违法");
                return vo;
            } else if (repairTaskBO.getRepairTypeId().equals("4")) {
                vo = MessageUtil.checkMustInput("轮胎数量", repairTaskBO.getTireNumber());
                if (vo.getCode() != 0) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("轮胎数量(轮胎任务的时候必填)");
                    return vo;
                }

                vo = MessageUtil.checkInteger("轮胎数量", repairTaskBO.getTireNumber());
                if (vo.getCode() != 0) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("轮胎数量必须是整数");
                    return vo;
                } else {
                    repairTaskModel.setTireNumber(Long.valueOf(repairTaskBO.getTireNumber()));
                }
            }
        }

        if (StringUtils.isBlank(repairTaskBO.getRepairGrade())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("维修级别输入（A,B,C 三种字母之一，其他非法）");
            return vo;
        } else {
            if (!"A".equals(repairTaskBO.getRepairGrade())
                    && !"B".equals(repairTaskBO.getRepairGrade())
                    && !"C".equals(repairTaskBO.getRepairGrade())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("维修级别输入（A,B,C 三种字母之一，其他非法）");
                return vo;
            }
        }

        BigDecimal repairTypeId = new BigDecimal(repairTaskBO.getRepairTypeId());
        repairTaskModel.setRepairTypeId(repairTypeId);
        switch (repairTaskBO.getRepairTypeId()) {
            case "1":
                if (repairTaskBO.getOrigin() != 2) {
                    if (StringUtils.isBlank(repairTaskBO.getInsuranceCompanyName())) {
                        vo.setCode(Contants.RETURN_ERROR_CODE);
                        vo.setMessage("车辆无保险信息，无法事故维修");
                        return vo;
                    }
                    if (StringUtils.isBlank(repairTaskBO.getAccidentReportNumber())) {
                        vo.setCode(Contants.RETURN_ERROR_CODE);
                        vo.setMessage("事故维修必须输入报案号");
                        return vo;
                    }
                }
                repairTaskModel.setRepairTypeName("事故维修 ");
                break;
            case "2":
                if (StringUtils.isBlank(repairTaskBO.getInsuranceCompanyName())) {
                    repairTaskModel.setInsuranceCompanyName("");
                }
                repairTaskModel.setRepairTypeName("自费维修 ");
                break;
            case "3":
                if (StringUtils.isBlank(repairTaskBO.getInsuranceCompanyName())) {
                    repairTaskModel.setInsuranceCompanyName("");
                }
                repairTaskModel.setRepairTypeName("车辆保养 ");
                break;
            case "6":
                if (StringUtils.isBlank(repairTaskBO.getInsuranceCompanyName())) {
                    repairTaskModel.setInsuranceCompanyName("");
                }
                repairTaskModel.setRepairTypeName("常规保养 ");
                break;
            case "7":
                if (StringUtils.isBlank(repairTaskBO.getInsuranceCompanyName())) {
                    repairTaskModel.setInsuranceCompanyName("");
                }
                repairTaskModel.setRepairTypeName("终端维修 ");
                break;
            case "9":
                if (StringUtils.isBlank(repairTaskBO.getInsuranceCompanyName())) {
                    repairTaskModel.setInsuranceCompanyName("");
                }
                repairTaskModel.setRepairTypeName("短租包修 ");
                break;
            default:
                if (StringUtils.isBlank(repairTaskBO.getInsuranceCompanyName())) {
                    repairTaskModel.setInsuranceCompanyName("");
                }
                repairTaskModel.setRepairTypeName("更换轮胎 ");
                break;
        }
        // 车牌号
        if (StringUtils.isBlank(repairTaskBO.getVehicleNo())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车牌号 不能为空");
            return vo;
        }
        // 车型
        if (StringUtils.isBlank(repairTaskBO.getVehicleModelSeq())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车型ID 不能为空");
            return vo;
        } else {
            try {
                repairTaskModel.setVehicleModelSeq(Long.valueOf(repairTaskBO.getVehicleModelSeq()));
            } catch (NumberFormatException e) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("车型ID必须输入整数");
                return vo;
            }
        }
        Integer sum1 = repairDepotInfoMapper.queryVehicleModelSeq(Long.valueOf(repairTaskBO.getVehicleModelSeq()));
        if (sum1 < 1) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车型ID不存在");
            return vo;
        }
        // 车型名称
        if (StringUtils.isBlank(repairTaskBO.getVehicleModelInfo())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车型名称不能为空");
        }
        // 车辆所属组织机构ID
        if (StringUtils.isBlank(repairTaskBO.getOrgId())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车辆所属组织机构ID 不能为空");
            return vo;
        } else {
            Integer sum = repairDepotInfoMapper.queryOrgId(repairTaskBO.getOrgId());
            if (sum < 1) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("车辆所属组织机构ID不存在");
                return vo;
            }
        }
        // 车辆所属组织机构名称
        if (StringUtils.isBlank(repairTaskBO.getOrgName())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车辆所属组织机构名称 不能为空");
            return vo;
        }
        // 车架号
        if (StringUtils.isBlank(repairTaskBO.getVin())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车架号 不能为空");
            return vo;
        }
        // 查询实际运营标签
        else {
            BdpVehicleBaseInfoDTO bdpVehicleBaseInfoDTO = bdpVehicleService.queryVehicleBaseInfoByVin(repairTaskBO.getVin());
            repairTaskModel.setFactOperateTag(-1);
            if (bdpVehicleBaseInfoDTO != null) {
                repairTaskModel.setFactOperateTag(bdpVehicleBaseInfoDTO.getFactOperateTag());
                if (repairTaskBO.getRenttype() == null) {
                    repairTaskModel.setRenttype(bdpVehicleBaseInfoDTO.getRenttype().intValue());
                }
            }
        }
        /**
         * 判断车辆 交付状态 车辆资产状态
         * 交付状态 0 未交付 1 已交付
         * 车辆资产状态 0  在建工程  1  固定资产
         * 2  固定资产（待报废）3  报废  4  固定资产(待处置)
         * 5  固定资产(已处置) 6以租代售 7库存商品
         * 8 已处置（未过户）
         */
        BdpVehicleBaseInfoDTO vehicleBaseInfo = bdpVehicleService.queryVehicleBaseInfoByVin(repairTaskBO.getVin());
        if (vehicleBaseInfo == null) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车架号不存在，不可以创建维修任务！");
            return vo;
        }
        String assetsStatus = vehicleBaseInfo.getAssetsStatus();
        if (assetsStatus == null) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车辆资产状态不存在，不可以创建维修任务！");
            return vo;
        }
        Integer deliveryStatus = vehicleBaseInfo.getDeliveryStatus();
        if (deliveryStatus == null) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车辆交付状态不存在，不可以创建维修任务！");
            return vo;
        }
        if (assetsStatus.equals("5") || assetsStatus.equals("3")) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("处置车辆不可以创建维修任务！");
            return vo;
        } else if (assetsStatus.equals("8") && deliveryStatus.equals(1)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("处置车辆不可以创建维修任务！");
            return vo;
        } else if (assetsStatus.equals("2") && deliveryStatus.equals(1)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("处置车辆不可以创建维修任务！");
            return vo;
        }

        // 修理厂相关
        if (StringUtils.isBlank(repairTaskBO.getRepairDepotId())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修理厂ID不能为空");
            return vo;
        }
        if (StringUtils.isBlank(repairTaskBO.getRepairDepotName())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修理厂名称不能为空");
            return vo;
        }
        repairTaskModel.setRepairDepotId(repairTaskBO.getRepairDepotId());
        RepairDepotInfo repairDepotInfo = repairDepotInfoMapper.queryDepotInfo(repairTaskBO.getRepairDepotId());
        if (null == repairDepotInfo) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修理厂信息不存在");
            return vo;
        }
        // 判断修理厂是否启用
        if (repairDepotInfo.getStatus() != 1) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("该维修厂已被禁用无法接受新的维修任务");
            return vo;
        }
        if (StringUtils.isBlank(repairDepotInfo.getRepairDepotOrgId())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("输入的修理厂ID查询不到修理厂的主要合作分公司");
            return vo;
        }
        repairTaskModel.setRepairDepotOrgId(repairDepotInfo.getRepairDepotOrgId());
        // 修理厂sap供应商编号
        if (StringUtils.isNotBlank(repairDepotInfo.getRepairDepotSapCode())) {
            repairTaskModel.setRepairDepotSapCode(repairDepotInfo.getRepairDepotSapCode());
        } else {
            repairTaskModel.setRepairDepotSapCode("");
        }
        repairTaskModel.setRepairDepotType(repairDepotInfo.getRepairDepotType());
        repairTaskModel.setTaxRate(repairDepotInfo.getTaxRate());
        repairTaskModel.setIsProxyOperable(repairDepotInfo.getIsProxyOperable());

        // 总里程数
        if (StringUtils.isNotBlank(repairTaskBO.getTotalMileage())) {
            BigDecimal totalMileage = new BigDecimal(repairTaskBO.getTotalMileage());
            repairTaskModel.setTotalMileage(totalMileage);
        } else {
            repairTaskModel.setTotalMileage(BigDecimal.ZERO);
        }

        // 任务创建时间
        if (StringUtils.isBlank(repairTaskBO.getTaskCreateTime())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务创建时间不能为空");
            return vo;
        } else {
            try {
                repairTaskModel.setTaskCreateTime(Timestamp.valueOf(repairTaskBO.getTaskCreateTime()));
            } catch (Exception e) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("任务创建时间的日期输入不合法");
                return vo;
            }
        }
        // 关联订单
        if (StringUtils.isBlank(repairTaskBO.getAssociatedOrder())) {
            repairTaskModel.setAssociatedOrder(StringUtils.EMPTY);
            repairTaskModel.setAuthId(StringUtils.EMPTY);
        } else {
            repairTaskModel.setAuthId(StringUtils.EMPTY);
            AbstractOrderInfoDTO orderInfoDto = newAndOldOrderConversionService.getOrderInfoById(repairTaskBO.getAssociatedOrder(), repairTaskBO.getVin());
            if (null != orderInfoDto) {
                BeanCopyUtils.copyProperties(orderInfoDto, repairTaskModel);
                if (StringUtils.isNotBlank(orderInfoDto.getAuthId())) {
                    repairTaskModel.setAuthId(orderInfoDto.getAuthId());
                } else {
                    repairTaskModel.setAuthId(StringUtils.EMPTY);
                }
                repairTaskModel.setRelateType(repairTaskBO.getRelateType());
                repairTaskModel.setOrderRemark(repairTaskBO.getOrderRemark());
            }
        }
        // 是否购买不计免赔(0:否 1:是)
        if (StringUtils.isNotBlank(repairTaskBO.getNoDeductiblesFlag())) {
            if (!repairTaskBO.getNoDeductiblesFlag().equals("0") && !repairTaskBO.getNoDeductiblesFlag().equals("1")) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("免赔输入(0:否 1:是),其他非法");
                return vo;
            }
            BigDecimal noDeductiblesFlag = new BigDecimal(repairTaskBO.getNoDeductiblesFlag());
            repairTaskModel.setNoDeductiblesFlag(noDeductiblesFlag);
        } else {
            repairTaskModel.setNoDeductiblesFlag(new BigDecimal(-1));
        }
        // 驾驶员姓名
        if (StringUtils.isBlank(repairTaskBO.getDriverName())) {
            repairTaskModel.setDriverName(StringUtils.EMPTY);
        }
        // 驾驶员手机
        if (StringUtils.isBlank(repairTaskBO.getDriverTel())) {
            repairTaskModel.setDriverTel(StringUtils.EMPTY);
        }

        // 巡检姓名
        if (StringUtils.isBlank(repairTaskBO.getRoutingInspectionName())) {
            repairTaskModel.setRoutingInspectionName(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(repairTaskBO.getRoutingInspectionTel())) {
            repairTaskModel.setRoutingInspectionTel(StringUtils.EMPTY);
        }

        // 受损部分
        if (StringUtils.isBlank(repairTaskBO.getDamagedPartDescribe())) {
            repairTaskModel.setDamagedPartDescribe(StringUtils.EMPTY);
        }
        // 事故描述
        if (StringUtils.isBlank(repairTaskBO.getAccidentDescribe())) {
            repairTaskModel.setAccidentDescribe(StringUtils.EMPTY);
        }
        if (StringUtils.isNotBlank(repairTaskBO.getTrailerFlag())) {
            if (!"0".equals(repairTaskBO.getTrailerFlag()) && !"1".equals(repairTaskBO.getTrailerFlag())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("输入是否拖车(0:否 1:是),其他非法");
                return vo;
            }
            BigDecimal trailerFlag = new BigDecimal(repairTaskBO.getTrailerFlag());
            repairTaskModel.setTrailerFlag(trailerFlag);
        } else {
            repairTaskModel.setTrailerFlag(BigDecimal.ZERO);
        }
        // 事故报案号
        if (StringUtils.isBlank(repairTaskBO.getAccidentReportNumber())) {
            repairTaskModel.setAccidentReportNumber(StringUtils.EMPTY);
        }
        // 当前环节
        Timestamp createTime = Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        repairTaskModel.setCurrentTache(10L);
        repairTaskModel.setCreateTime(createTime);
        repairTaskModel.setCreateOperId((long) -1);
        repairTaskModel.setCreateOperName("维修厂");
        repairTaskModel.setUpdateTime(createTime);
        repairTaskModel.setUpdateOperId((long) -1);
        repairTaskModel.setUpdateOperName("维修厂");

        // 获取所属公司车型
        // mtc_repair_task 表中的 org_id 之前存的是运营公司, vehicle表中org_id 对应的是所属公司
        // 这次需求新增所属公司字段存在operate_id字段中
        BdpVehicleBaseInfoDTO bdpVehicleBaseInfoDTO = bdpVehicleService.queryVehicleBaseInfoByVin(repairTaskBO.getVin());
        //VehicleOperateDTO vehicleOperate = rentReplaceVehicleService.getVehicleOperateDTOByVin(repairTaskBO.getVin());
        if (bdpVehicleBaseInfoDTO != null) {
            String operationOrgId = bdpVehicleBaseInfoDTO.getOrgId();
            repairTaskModel.setOperateOrgId(operationOrgId);
            repairTaskModel.setOperateOrgName(orgInfoMapper.getOrgName(operationOrgId));
        }

        // 验证事故编号
        if (StringUtils.isNotBlank(repairTaskModel.getAccidentNo())) {
            DefaultServiceRespDTO validateRespDTO = insurancePreReviewService.validateAccidentNo(repairTaskModel, repairTaskModel.getAccidentNo());
            if (validateRespDTO.getCode() != 0) {
                return validateRespDTO;
            }
        }

        // 根据任务编号 删除 改派完成任务
        int delCount = repairTaskMapper.deleteByTaskNo(repairTaskBO.getDispatchTaskSeq());
        if (delCount > 0) {
            // 如果删除成功 删除子表中 该任务的所有关联信息 1.图片 2 换件项目明细 3 修理项目明细
            // 1.图片
            vehicleRepairPicMapper.deletePicByTaskNoAgain(repairTaskBO.getDispatchTaskSeq());
            // 2 换件项目明细
            replaceItemDetailMapper.deleteBytaskNo(repairTaskBO.getDispatchTaskSeq());
            // 3 修理项目明细
            repairItemDetailMapper.deleteBytaskNo(repairTaskBO.getDispatchTaskSeq());
        }
        /*RepairTaskLeavingFactoryDTO repairTaskLeavingFactoryDTO = getRepairTaskLeavingFactory(repairTaskModel.getDispatchTaskSeq());
        if (null != repairTaskLeavingFactoryDTO) {
            repairTaskModel.setTakeUserName(repairTaskLeavingFactoryDTO.getName());
            repairTaskModel.setTakeUserPhone(repairTaskLeavingFactoryDTO.getPhoneNumber());
            repairTaskModel.setTakeVoucher(repairTaskLeavingFactoryDTO.getDeliveryPictures());
            // repairTaskModel.setSynTakeTime(repairTaskLeavingFactoryDTO.getDeliveryTime());
        }*/

        // 整理增加车辆历史信息
        String useTime;
        if (null != repairTaskModel.getSendRepairTime()) {
            useTime = repairTaskModel.getSendRepairTime();
        } else {
            useTime = DateUtil.getFormatDate(repairTaskModel.getTaskCreateTime(), DateUtil.DATE_TYPE2);
        }
        VehicleHistoryInfoDTO vehicleHistoryInfoDTO;
        // 如果 送修时间是今天 则取车辆当前实时信息
        if (cn.hutool.core.date.DateUtil.isSameDay(cn.hutool.core.date.DateUtil.parse(useTime, DateUtil.DATE_TYPE5), new Date())) {
            vehicleHistoryInfoDTO = bfcCostService.getVehicleBaseInfo(repairTaskModel.getVin());
        }else {
            vehicleHistoryInfoDTO = bfcCostService.getVehicleHistoryByTime(repairTaskModel.getVin(), useTime);
        }
        if (null != vehicleHistoryInfoDTO) {
            repairTaskModel.setPropertyStatus(vehicleHistoryInfoDTO.getPropertyStatus());
            repairTaskModel.setProductLine(vehicleHistoryInfoDTO.getProductLine());
            repairTaskModel.setSubProductLine(vehicleHistoryInfoDTO.getSubProductLine());
            repairTaskModel.setFactOperateTag(vehicleHistoryInfoDTO.getFactOperateTag());
        }
        // 新增车机总里程数
        repairTaskModel.setMileage(repairTaskBO.getMileage());

        int count = repairTaskMapper.saveByRepairTaskModel(repairTaskModel);
        log.info("门店调用 车架号 :" + repairTaskModel.getVin());
        mdRpc.saveMdVehicleOccupyRecord(repairTaskModel.getVin());
        if (count < 1) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("保存失败");
            return vo;
        }

        List<String> drivingLicensePics = repairTaskBO.getDrivingLicensePics();
        if (CollectionUtils.isNotEmpty(drivingLicensePics)) {
            for (String string : drivingLicensePics) {
                if (string.length() > 200) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("行驶证图片长度不能超过200位");
                    return vo;
                }
                VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                vehicleRepairPic.setTaskNo(repairTaskBO.getDispatchTaskSeq());
                vehicleRepairPic.setPicType(BigDecimal.valueOf(10));
                vehicleRepairPic.setPicUrl(string);
                Integer repair_pic = saveVehicle_repair_pic(vehicleRepairPic);
                if (repair_pic < 1) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保存失败");
                    return vo;
                }
            }
        }
        List<String> policyPics = repairTaskBO.getPolicyPics();
        if (CollectionUtils.isNotEmpty(policyPics)) {
            for (String string : policyPics) {
                if (string.length() > 200) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保单图片url长度不能超过200位");
                    return vo;
                }
                VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                vehicleRepairPic.setTaskNo(repairTaskBO.getDispatchTaskSeq());
                vehicleRepairPic.setPicType(BigDecimal.valueOf(11));
                vehicleRepairPic.setPicUrl(string);
                Integer repair_pic = saveVehicle_repair_pic(vehicleRepairPic);
                if (repair_pic < 1) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保存失败");
                    return vo;
                }
            }
        }
        List<String> accidentPics = repairTaskBO.getAccidentPics();
        if (CollectionUtils.isNotEmpty(accidentPics)) {
            for (String string : accidentPics) {
                if (string.length() > 200) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("事故图片url长度不能超过200位");
                    return vo;
                }
                VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                vehicleRepairPic.setTaskNo(repairTaskBO.getDispatchTaskSeq());
                vehicleRepairPic.setPicType(BigDecimal.valueOf(12));
                vehicleRepairPic.setPicUrl(string);
                Integer repair_pic = saveVehicle_repair_pic(vehicleRepairPic);
                if (repair_pic < 1) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保存失败");
                    return vo;
                }
            }
        }
        List<String> standardCarPics = repairTaskBO.getStandardCarPics();
        if (CollectionUtils.isNotEmpty(standardCarPics)) {
            for (String string : standardCarPics) {
                if (string.length() > 200) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("车损图片(标的车)url不长度不能超过200位");
                    return vo;
                }
                VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                vehicleRepairPic.setTaskNo(repairTaskBO.getDispatchTaskSeq());
                vehicleRepairPic.setPicType(BigDecimal.valueOf(13));
                vehicleRepairPic.setPicUrl(string);
                Integer repair_pic = saveVehicle_repair_pic(vehicleRepairPic);
                if (repair_pic < 1) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保存失败");
                    return vo;
                }
            }
        }
        List<String> thirdPartyPics = repairTaskBO.getThirdPartyPics();
        if (CollectionUtils.isNotEmpty(thirdPartyPics)) {
            for (String string : thirdPartyPics) {
                if (string.length() > 200) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("车损图片(三者车)url长度不能超过200位");
                    return vo;
                }
                VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                vehicleRepairPic.setTaskNo(repairTaskBO.getDispatchTaskSeq());
                vehicleRepairPic.setPicType(BigDecimal.valueOf(14));
                vehicleRepairPic.setPicUrl(string);
                Integer repair_pic = saveVehicle_repair_pic(vehicleRepairPic);
                if (repair_pic < 1) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保存失败");
                    return vo;
                }
            }
        }
        List<String> claimMaterialPics = repairTaskBO.getClaimMaterialPics();
        if (CollectionUtils.isNotEmpty(claimMaterialPics)) {
            for (String string : claimMaterialPics) {
                if (string.length() > 200) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("理赔材料图片url长度不能超过200位");
                    return vo;
                }
                VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                vehicleRepairPic.setTaskNo(repairTaskBO.getDispatchTaskSeq());
                vehicleRepairPic.setPicType(BigDecimal.valueOf(15));
                vehicleRepairPic.setPicUrl(string);
                Integer repair_pic = saveVehicle_repair_pic(vehicleRepairPic);
                if (repair_pic < 1) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保存失败");
                    return vo;
                }
            }
        }
        List<String> otherPics = repairTaskBO.getOtherPics();
        if (CollectionUtils.isNotEmpty(otherPics)) {
            for (String string : otherPics) {
                if (string.length() > 200) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("其他图片url长度不合理");
                    return vo;
                }
                VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                vehicleRepairPic.setTaskNo(repairTaskBO.getDispatchTaskSeq());
                vehicleRepairPic.setPicType(BigDecimal.valueOf(16));
                vehicleRepairPic.setPicUrl(string);
                Integer repair_pic = saveVehicle_repair_pic(vehicleRepairPic);
                if (repair_pic < 1) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("保存失败");
                    return vo;
                }
            }
        }

        // 操作日志
        String content = "新增维修任务";
        Integer integer = this.saveSelectiveLog(repairTaskModel, content);

//        //添加维保手动计提日志
//        if (ArrayUtils.contains(Contants.MANUAL_LIST, repairTypeId.toString())){
//            ComModel comModel = new ComModel();
//            comModel.setCreateOperId(Long.valueOf(-1));
//            comModel.setCreateOperName("维修厂");
//            comModel.setUpdateOperId(Long.valueOf(-1));
//            comModel.setUpdateOperName("维修厂");
//            manualUtils.saveManualLog(mtcManualTaskDTO.getId(), "NEW", content, comModel);
//        }

        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcProcessLog.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcProcessLog.setCreateOperId((long) -1);
        mtcProcessLog.setUpdateOperId((long) -1);
        mtcProcessLog.setCreateOperName(repairTaskBO.getRepairDepotName());
        mtcProcessLog.setUpdateOperName(repairTaskBO.getRepairDepotName());
        mtcProcessLog.setRemark("车辆交接");
        mtcProcessLog.setRecoderId(repairTaskModel.getId() + StringUtils.EMPTY);
        mtcProcessLog.setStatus(0);
        mtcProcessLog.setOpeContent("未处理");
        mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcProcessLog.setCurrentTache((long) Contants.CURRENT_TACHE_VEHICLE_JOIN);
        if (!"4".equals(repairTaskBO.getRepairTypeId())) {
            // 添加流程log
            mtcOperatorLogMapper.saveSelective(mtcProcessLog);
            // 记录任务状态持续时间
            vehicleJoinSubject.recordPeriod(new BaseRepairEvent(createTime, repairTaskBO.getDispatchTaskSeq()));
        }

        if (integer < 1) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("保存失败");
            return vo;
        }
        //发送车辆 交接待处理
        vehicleJoinSubject.notifyTaskAcceptFromIds(new BaseRepairEvent(new Date(), repairTaskBO.getDispatchTaskSeq()));
        return vo;
    }

    private Integer saveVehicle_repair_pic(VehicleRepairPic vehicleRepairPic) {
        vehicleRepairPic.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        vehicleRepairPic.setCreateOperId(Long.valueOf(-1));
        vehicleRepairPic.setCreateOperName("维修厂");
        vehicleRepairPic.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        vehicleRepairPic.setUpdateOperId(Long.valueOf(-1));
        vehicleRepairPic.setUpdateOperName("维修厂");
        int count = vehicleRepairPicMapper.save(vehicleRepairPic);
        return count;

    }

    ;

    private Integer saveSelectiveLog(RepairTaskModelBO repairTaskModel, String content) {
        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
        mtcOperatorLog.setTableName("mtc_repair_task");
        mtcOperatorLog.setRecoderId(repairTaskModel.getId() + StringUtils.EMPTY);
        mtcOperatorLog.setOpeContent(content);
        mtcOperatorLog.setRemark(StringUtils.EMPTY);
        mtcOperatorLog.setStatus(1);
        mtcOperatorLog.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcOperatorLog.setCreateOperId(Long.valueOf(-1));
        mtcOperatorLog.setCreateOperName("维修厂");
        mtcOperatorLog.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcOperatorLog.setUpdateOperId(Long.valueOf(-1));
        mtcOperatorLog.setUpdateOperName("维修厂");
        Integer count = mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
        return count;

    }

    private Integer saveSelectiveLog1(Long id, String remark, String tableName, String content, ComModel comModel) {
        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
        mtcOperatorLog.setTableName(tableName);
        mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
        mtcOperatorLog.setOpeContent(content);
        mtcOperatorLog.setRemark(remark);
        mtcOperatorLog.setStatus(1);
        mtcOperatorLog.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcOperatorLog.setCreateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setCreateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcOperatorLog.setUpdateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setUpdateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VEHICLE_CHECK));
        Integer count = mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
        return count;

    }

    /**
     * 新增维保计提日志
     *
     * @param id
     * @param remark
     * @param content
     * @param comModel
     * @return
     */
    private Integer saveManualLog(Long id, String remark, String content, ComModel comModel) {
        MtcManualLog mtcOperatorLog = new MtcManualLog();
        mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
        mtcOperatorLog.setOpeContent(content);
        mtcOperatorLog.setRemark(remark);
        mtcOperatorLog.setStatus(1);
        mtcOperatorLog.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcOperatorLog.setCreateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setCreateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mtcOperatorLog.setUpdateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setUpdateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VEHICLE_CHECK));
        Integer count = mtcManualLogMapper.saveSelective(mtcOperatorLog);
        return count;
    }

    /**
     * 首页信息一览
     *
     * @param pageNum
     * @param pageSize
     * @param orgId
     * @param repairDepotId
     * @param request
     * @return
     */
    @Override
    public PageInfo<VehicleRepairNumBO> getFirstPageInfo(Integer pageNum, Integer pageSize, String orgId,
                                                         String repairDepotId, HttpServletRequest request) {

        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }

        List<String> repairDepotIds = new ArrayList<String>();

        if (StringUtils.isBlank(repairDepotId)) {
            repairDepotIds = repairDepotInfoMapper.getRepairDepotIds(orgId);
            if (CollectionUtils.isEmpty(repairDepotIds)) {
                repairDepotIds.add(StringUtils.EMPTY);
            }
        } else {
            String[] ids = repairDepotId.split(",");
            for (String rdid : ids) {
                repairDepotIds.add(rdid);
            }
        }

        log.debug("RepairTaskServiceImpl 当前页码名称检索条件:" + pageNum);
        log.debug("RepairTaskServiceImpl 每页显示的条数检索条件:" + pageSize);
        log.debug("RepairTaskServiceImpl 车辆运营单位名称检索条件:" + orgId);
        log.debug("RepairTaskServiceImpl 修理厂名称检索条件:" + repairDepotIds.toString());
        PageHelper.startPage(pageNum, pageSize, false);
        // 获取系统当前时间
        Timestamp now = new Timestamp(System.currentTimeMillis());
        SimpleDateFormat dateFormat = new SimpleDateFormat(ComUtil.DATE_TYPE2);
        String currentTime = dateFormat.format(now);
        List<VehicleRepairNumBO> list = repairTaskMapper.getFirstPageInfo(orgId, repairDepotIds, currentTime);
        PageInfo<VehicleRepairNumBO> pageInfo = new PageInfo<>(list);
        Integer total = repairTaskMapper.getFirstPageInfoNum(repairDepotIds);
        pageInfo.setTotal(total);
        return pageInfo;

    }

    /**
     * 导出首页信息
     *
     * @param orgId
     * @param repairDepotId
     * @param request
     * @return
     */
    @Override
    public DefaultServiceRespDTO exportFirstPageInfo(String orgId, String repairDepotId, HttpServletRequest request,
                                                     HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }

        List<String> repairDepotIds = new ArrayList<String>();
        if (StringUtils.isBlank(repairDepotId)) {
            repairDepotIds = repairDepotInfoMapper.getRepairDepotIds(orgId);
            if (CollectionUtils.isEmpty(repairDepotIds)) {
                repairDepotIds.add(StringUtils.EMPTY);
            }
        } else {
            String[] ids = repairDepotId.split(",");
            for (String rdid : ids) {
                repairDepotIds.add(rdid);
            }
        }
        log.debug("RepairTaskServiceImpl 车辆运营单位名称检索条件:" + orgId);
        log.debug("RepairTaskServiceImpl 修理厂名称检索条件:" + repairDepotIds.toString());
        // 声明一个工作薄
        Workbook workbook = new SXSSFWorkbook(100);

        // 生成一个表格
        Sheet sheet = workbook.createSheet();
        sheet.setColumnWidth(0, 10000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 4000);
        sheet.setColumnWidth(6, 4000);

        String[] headers = apolloPropertyUtils.getString("export.FirstPageInfo.title")
                .split(Contants.COMMA_SIGN_SPLIT_NAME);

        int columnNum = headers.length;
        // 产生表格标题行
        Row row = sheet.createRow(0);
        for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
            Cell cell = row.createCell(columnIndex);
            cell.setCellValue(headers[columnIndex]);
        }
        // 获取系统当前时间
        Timestamp now = new Timestamp(System.currentTimeMillis());
        SimpleDateFormat dateFormat = new SimpleDateFormat(ComUtil.DATE_TYPE2);
        String currentTime = dateFormat.format(now);
        List<VehicleRepairNumBO> list = repairTaskMapper.getFirstPageInfo(orgId, repairDepotIds, currentTime);
        if (CollectionUtils.isNotEmpty(list)) {
            for (int rowIndex = 0; rowIndex < list.size(); rowIndex++) {
                VehicleRepairNumBO vehicleRepairNumBO = list.get(rowIndex);
                row = sheet.createRow(rowIndex + 1);
                // 第一列：修理厂名称
                Cell cell0 = row.createCell(0);
                // 第二列：当前在修车辆总数
                Cell cell1 = row.createCell(1);
                // 第三列：今日新增车辆总数
                Cell cell2 = row.createCell(2);
                // 第四列：今日出厂车辆总数
                Cell cell3 = row.createCell(3);
                // 第五列：在修A类数
                Cell cell4 = row.createCell(4);
                // 第六列：在修B类数
                Cell cell5 = row.createCell(5);
                // 第七列：在修C类数
                Cell cell6 = row.createCell(6);

                /* 往Excel表的该行列里面赋值 */
                cell0.setCellValue(vehicleRepairNumBO.getRepairDepotName());
                cell1.setCellValue(vehicleRepairNumBO.getCurrentRepairNum());
                cell2.setCellValue(vehicleRepairNumBO.getTodayAddNum());
                cell3.setCellValue(vehicleRepairNumBO.getTodayOutNum());
                cell4.setCellValue(vehicleRepairNumBO.getaRepairNum());
                cell5.setCellValue(vehicleRepairNumBO.getbRepairNum());
                cell6.setCellValue(vehicleRepairNumBO.getcRepairNum());

            }
        }

        // String exportDirector =
        // apolloPropertyUtils.getString(Contants.EXPROT_DIRECTORY);
        SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateStr = dateformat.format(System.currentTimeMillis());
        String execelName = Contants.FIRSTPAGE_INFO_FUNCTION_NAME + dateStr + Contants.XLSX;
        // File file = new File("d:\\" + execelName);
        response.setHeader("Content-Type", "application/vnd.ms-excel");
        response.setHeader("content-disposition", "attachment;filename=" + execelName);

        OutputStream out = null;

        try {
            out = response.getOutputStream();
        } catch (IOException ioe) {
        }
        // OutputStream out = null;
        try {
            // out = new FileOutputStream(file);
            workbook.write(out);
            out.flush();// 缓存清空输出
        } catch (FileNotFoundException e1) {
            log.error(ComUtil.getExceptionMsg(e1));
        } catch (IOException e) {
            log.error(ComUtil.getExceptionMsg(e));
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }
                workbook = null;
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }
                out = null;
            }

        }
        return vo;

    }

    /**
     * 首页看板三个数据统计
     *
     * @param request
     * @return
     */
    @Override
    public VehicleRepairStatisticsBO getVehicleRepairStatistics(String orgId, HttpServletRequest request) {
        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }
        VehicleRepairStatisticsBO vehicleRepairStatisticsBO = new VehicleRepairStatisticsBO();

        // 获取系统当前时间
        Timestamp now = new Timestamp(System.currentTimeMillis());
        SimpleDateFormat dateFormat = new SimpleDateFormat(ComUtil.DATE_TYPE2);
        String currentTime = dateFormat.format(now);

        // 当前在修总数量
        vehicleRepairStatisticsBO.setCurrentRepairTotalNum(repairTaskMapper.getCurrentRepairTotalNum(orgId));

        // 今日进修理厂数量
        vehicleRepairStatisticsBO.setTodayInFactoryNum(repairTaskMapper.getTodayInFactoryNum(orgId, currentTime));

        // 今日出修理厂数量
        vehicleRepairStatisticsBO.setTodayOutFactoryNum(repairTaskMapper.getTodayOutFactoryNum(orgId, currentTime));
        return vehicleRepairStatisticsBO;
    }

    /**
     * 查看修理厂在修车辆明细
     *
     * @param pageNum       当前页码
     * @param pageSize      每页显示条数
     * @param repairDepotId 修理厂ID
     * @param orgId         车辆运营单位ID
     * @param request
     * @return
     */
    @Override
    public RepairDepotInRepairingListBO getRepairDepotInRepairingInfo(Integer pageNum, Integer pageSize, String orgId,
                                                                      String repairDepotId, HttpServletRequest request) {

        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }
        log.debug("RepairTaskServiceImpl 当前页码名称检索条件:" + pageNum);
        log.debug("RepairTaskServiceImpl 每页显示的条数检索条件:" + pageSize);
        log.debug("RepairTaskServiceImpl 车辆运营单位名称检索条件:" + orgId);
        log.debug("RepairTaskServiceImpl 修理厂名称检索条件:" + repairDepotId);
        PageHelper.startPage(pageNum, pageSize, true);
        List<RepairDepotInRepairingBO> list = repairTaskMapper.getRepairDepotInRepairingInfo(orgId, repairDepotId);
        PageInfo<RepairDepotInRepairingBO> pageInfo = new PageInfo<>(list);

        Integer maintainNum = repairTaskMapper.queryMaintainNum(orgId, repairDepotId);
        Integer maintenanceToselfNum = repairTaskMapper.queryMaintenanceToselfNum(orgId, repairDepotId);
        RepairDepotInRepairingListBO listBo = new RepairDepotInRepairingListBO();

        listBo.setList(pageInfo);
        listBo.setMaintainNum(String.valueOf(maintainNum));
        listBo.setMaintenanceToselfNum(String.valueOf(maintenanceToselfNum));

        // Integer total =
        // repairTaskMapper.getRepairDepotInRepairingInfoNum(orgId,
        // repairDepotId);
        // pageInfo.setTotal(total);
        return listBo;

    }

    /**
     * 导出修理厂在修车辆明细
     *
     * @param repairDepotId 修理厂ID
     * @param orgId         车辆运营单位ID
     * @param request
     * @return
     */
    @Override
    public DefaultServiceRespDTO exportRepairDepotInRepairingInfo(String orgId, String repairDepotId,
                                                                  HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }

        log.debug("RepairTaskServiceImpl 车辆运营单位名称检索条件:" + orgId);
        log.debug("RepairTaskServiceImpl 修理厂名称检索条件:" + repairDepotId);

        // 声明一个工作薄
        Workbook workbook = new SXSSFWorkbook(100);

        // 生成一个表格
        Sheet sheet = workbook.createSheet();
        sheet.setColumnWidth(0, 10000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 4000);

        String[] headers = apolloPropertyUtils.getString("export.RepairDepotInRepairingInfo.title")
                .split(Contants.COMMA_SIGN_SPLIT_NAME);

        int columnNum = headers.length;
        // 产生表格标题行
        Row row = sheet.createRow(0);
        for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
            Cell cell = row.createCell(columnIndex);
            cell.setCellValue(headers[columnIndex]);
        }

        List<RepairDepotInRepairingBO> list = repairTaskMapper.getRepairDepotInRepairingInfo(orgId, repairDepotId);
        if (CollectionUtils.isNotEmpty(list)) {
            for (int rowIndex = 0; rowIndex < list.size(); rowIndex++) {
                RepairDepotInRepairingBO repairDepotInRepairingBO = list.get(rowIndex);
                row = sheet.createRow(rowIndex + 1);
                // 第一列：车辆运营单位
                Cell cell0 = row.createCell(0);
                // 第二列：任务编号
                Cell cell1 = row.createCell(1);
                // 第三列：车牌号
                Cell cell2 = row.createCell(2);
                // 第四列：车型
                Cell cell3 = row.createCell(3);
                // 第五列：车架号
                Cell cell4 = row.createCell(4);
                // 第六列：车辆保险所属
                Cell cell5 = row.createCell(5);
                // 第七列：修理类型
                Cell cell6 = row.createCell(6);
                // 第八列：修理级别
                Cell cell7 = row.createCell(7);
                // 第九列：车辆接收时间
                Cell cell8 = row.createCell(8);
                // 第十列：预计修理完成时间
                Cell cell9 = row.createCell(9);
                // 第十一列：业务状态
                Cell cell10 = row.createCell(10);

                /* 往Excel表的该行列里面赋值 */
                cell0.setCellValue(repairDepotInRepairingBO.getOrgName());
                cell1.setCellValue(repairDepotInRepairingBO.getTaskNo());
                cell2.setCellValue(repairDepotInRepairingBO.getVehicleNo());
                cell3.setCellValue(repairDepotInRepairingBO.getVehicleModelName());
                cell4.setCellValue(repairDepotInRepairingBO.getVin());
                cell5.setCellValue(repairDepotInRepairingBO.getInsuranceCompanyName());
                cell6.setCellValue(repairDepotInRepairingBO.getRepairTypeName());
                cell7.setCellValue(repairDepotInRepairingBO.getRepairGrade());
                cell8.setCellValue(repairDepotInRepairingBO.getVehicleReciveTime());
                cell9.setCellValue(repairDepotInRepairingBO.getVehicleFinishTime());
                cell10.setCellValue(ComUtil.getRenttypeStr(repairDepotInRepairingBO.getRenttype()));
            }
        }

        // String exportDirector =
        // apolloPropertyUtils.getString(Contants.EXPROT_DIRECTORY);
        SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateStr = dateformat.format(System.currentTimeMillis());
        String execelName = Contants.INREPAIRING_INFO_FUNCTION_NAME + dateStr + Contants.XLSX;
        // File file = new File("d:\\" + execelName);
        response.setHeader("Content-Type", "application/vnd.ms-excel");
        response.setHeader("content-disposition", "attachment;filename=" + execelName);

        OutputStream out = null;

        try {
            out = response.getOutputStream();
        } catch (IOException ioe) {
        }

        // OutputStream out = null;
        try {
            // out = new FileOutputStream(file);
            workbook.write(out);
            out.flush();// 缓存清空输出
        } catch (FileNotFoundException e1) {
            log.error(ComUtil.getExceptionMsg(e1));
        } catch (IOException e) {
            log.error(ComUtil.getExceptionMsg(e));
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }
                workbook = null;
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }
                out = null;
            }

        }
        return vo;

    }

    /**
     * 查看车辆车辆验收任务进度列表
     *
     * @param vehicleCheckTaskScheduleQueryBO
     * @param request
     * @return
     */
    @Override
    public VehicleCheckTaskScheduleCountBO queryVehicleCheck(
            VehicleCheckTaskScheduleQueryBO vehicleCheckTaskScheduleQueryBO, HttpServletRequest request) {

        if (StringUtils.isEmpty(vehicleCheckTaskScheduleQueryBO.getOrgId())) {
            ComModel comModel = ComUtil.getUserInfo(request);
            vehicleCheckTaskScheduleQueryBO.setOrgId(comModel.getOrgId());
        }

        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getVehicleNo())) {
            vehicleCheckTaskScheduleQueryBO.setVehicleNo(vehicleCheckTaskScheduleQueryBO.getVehicleNo().trim());
        }
        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getTaskNo())) {
            vehicleCheckTaskScheduleQueryBO.setTaskNo(vehicleCheckTaskScheduleQueryBO.getTaskNo().trim());
        }
        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getRepairDepotName())) {
            vehicleCheckTaskScheduleQueryBO
                    .setRepairDepotName(vehicleCheckTaskScheduleQueryBO.getRepairDepotName().trim());
        }
        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getVin())) {
            vehicleCheckTaskScheduleQueryBO.setVin(vehicleCheckTaskScheduleQueryBO.getVin().trim());
        }
        PageHelper.startPage(vehicleCheckTaskScheduleQueryBO.getPageNum(),
                vehicleCheckTaskScheduleQueryBO.getPageSize());
        List<VehicleCheckTaskScheduleBO> list = repairTaskMapper.querySchedule(vehicleCheckTaskScheduleQueryBO);
        // 查询业财发送sap状态
        // 如果在计提任务中就不需要查
        List<String> taskNoList = list.stream()
                .map(VehicleCheckTaskScheduleBO::getTaskNo)
                .filter(this::isExitManualTask)
                .collect(Collectors.toList());
        String info = StringUtils.EMPTY;
        if (taskNoList.size() != 0) {
            try {
                Map<String, List<String>> param = new HashMap<>();
                param.put("taskNoList", taskNoList);
                info = HttpRequest.postMethod(apolloPropertyUtils.getString("bfsVehicleRepairBillsAccrualResultUrl"),
                        param);
                if (StringUtils.isNotBlank(info)) {
                    JSONObject jsonObject = JSONObject.parseObject(info);
                    JSONArray jsonArray = jsonObject.getJSONArray("accountingStatusList");
                    if (CollectionUtils.isNotEmpty(jsonArray)) {
                        Map<String, String> map = new HashMap<>();
                        jsonArray.forEach(item -> {
                            JSONObject v = (JSONObject) item;
                            map.put(v.getString("taskNo"), v.getString("accountingStatus"));
                        });
                        list.forEach(item -> {
                            String accountingStatus = map.get(item.getTaskNo());
                            item.setAccountingStatus(accountingStatus);
                        });
                    }
                }
            } catch (Exception e) {
                log.error("查询sap发送状态异常", e);
            }
        }
        PageInfo<VehicleCheckTaskScheduleBO> pageInfo = new PageInfo<>(list);
        VehicleCheckTaskScheduleCountBO vcbo = new VehicleCheckTaskScheduleCountBO();
        Long applyCount = repairTaskMapper.queryApplyCount(vehicleCheckTaskScheduleQueryBO);
        Long waitCount = repairTaskMapper.queryWaitCount(vehicleCheckTaskScheduleQueryBO);
        Long finishCount = repairTaskMapper.queryFinishCount(vehicleCheckTaskScheduleQueryBO);
        Long closedCount = repairTaskMapper.queryClosedCount(vehicleCheckTaskScheduleQueryBO);
        vcbo.setPageinfo(pageInfo);
        vcbo.setApplyCount(applyCount);
        vcbo.setWaitCount(waitCount);
        vcbo.setFinishCount(finishCount);
        vcbo.setClosedCount(closedCount);
        return vcbo;
    }

    @Override
    public DefaultServiceRespDTO exportVehicleCheck(VehicleCheckTaskScheduleQueryBO vehicleCheckTaskScheduleQueryBO, HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        try {
            // TODO 李成祥
            // 声明一个工作薄
            XSSFWorkbook workbook = new XSSFWorkbook();
            // 生成一个表格
            XSSFSheet sheet = workbook.createSheet();
            sheet.setColumnWidth(0, 6000);
            sheet.setColumnWidth(1, 6000);
            sheet.setColumnWidth(19, 6000);
            sheet.setColumnWidth(20, 6000);
            String[] headers = apolloPropertyUtils.getString("export.vehicleCheck.title").split(Contants.COMMA_SIGN_SPLIT_NAME);

            int columnNum = headers.length;
            // 产生表格标题行
            XSSFRow row = sheet.createRow(0);
            for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
                XSSFCell cell = row.createCell(columnIndex);
                cell.setCellValue(headers[columnIndex]);
            }
            if (StringUtils.isEmpty(vehicleCheckTaskScheduleQueryBO.getOrgId())) {
                ComModel comModel = ComUtil.getUserInfo(request);
                vehicleCheckTaskScheduleQueryBO.setOrgId(comModel.getOrgId());
            }
            if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getVehicleNo())) {
                vehicleCheckTaskScheduleQueryBO.setVehicleNo(vehicleCheckTaskScheduleQueryBO.getVehicleNo().trim());
            }
            if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getTaskNo())) {
                vehicleCheckTaskScheduleQueryBO.setTaskNo(vehicleCheckTaskScheduleQueryBO.getTaskNo().trim());
            }
            if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getRepairDepotName())) {
                vehicleCheckTaskScheduleQueryBO
                        .setRepairDepotName(vehicleCheckTaskScheduleQueryBO.getRepairDepotName().trim());
            }
            if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getVin())) {
                vehicleCheckTaskScheduleQueryBO.setVin(vehicleCheckTaskScheduleQueryBO.getVin().trim());
            }
            int exportSize = 5000;
            vehicleCheckTaskScheduleQueryBO.setExportSize(exportSize);
            List<VehicleCheckTaskScheduleBO> list = repairTaskMapper.queryScheduleExport(vehicleCheckTaskScheduleQueryBO);
            while (CollectionUtils.isNotEmpty(list)) {
                for (int rowIndex = 0; rowIndex < list.size(); rowIndex++) {
                    VehicleCheckTaskScheduleBO temp = list.get(rowIndex);
                    row = sheet.createRow(rowIndex + 1);
                    XSSFCell cell0 = row.createCell(0);
                    XSSFCell cell1 = row.createCell(1);
                    XSSFCell cell2 = row.createCell(2);
                    XSSFCell cell3 = row.createCell(3);
                    XSSFCell cell4 = row.createCell(4);
                    XSSFCell cell5 = row.createCell(5);
                    XSSFCell cell6 = row.createCell(6);
                    XSSFCell cell7 = row.createCell(7);
                    XSSFCell cell8 = row.createCell(8);
                    XSSFCell cell9 = row.createCell(9);
                    XSSFCell cell10 = row.createCell(10);
                    XSSFCell cell11 = row.createCell(11);
                    XSSFCell cell12 = row.createCell(12);
                    XSSFCell cell13 = row.createCell(13);
                    XSSFCell cell14 = row.createCell(14);
                    XSSFCell cell15 = row.createCell(15);
                    XSSFCell cell16 = row.createCell(16);
                    XSSFCell cell17 = row.createCell(17);
                    XSSFCell cell18 = row.createCell(18);
                    XSSFCell cell19 = row.createCell(19);
                    XSSFCell cell20 = row.createCell(20);
                    XSSFCell cell21 = row.createCell(21);
                    XSSFCell cell22 = row.createCell(22);
                    XSSFCell cell23 = row.createCell(23);
                    XSSFCell cell24 = row.createCell(24);

                    cell0.setCellValue(temp.getTaskNo() == null ? StringUtils.EMPTY : temp.getTaskNo());
                    cell1.setCellValue(temp.getVehicleNo() == null ? StringUtils.EMPTY : temp.getVehicleNo());
                    cell2.setCellValue(ComUtil.getRenttypeStr(temp.getRenttype()));
                    cell3.setCellValue(ComUtil.getFaceOperateTagStr(temp.getFactOperateTag()));
                    cell4.setCellValue(temp.getVehicleModelInfo() == null ? StringUtils.EMPTY : temp.getVehicleModelInfo());
                    cell5.setCellValue(temp.getVin() == null ? StringUtils.EMPTY : temp.getVin());
                    cell6.setCellValue(temp.getRepairTypeName() == null ? StringUtils.EMPTY : temp.getRepairTypeName());
                    cell7.setCellValue(temp.getRepairDepotName() == null ? StringUtils.EMPTY : temp.getRepairDepotName());
                    cell8.setCellValue(temp.getTotalMoney().toString());
                    // 计算税前总价
                    if (temp.getTotalMoney() != null) {
                        String taxRate = apolloPropertyUtils.getString("vehicleRepairAmountTaxRate");
                        BigDecimal totalMoneyPreTax = temp.getTotalMoney().divide(new BigDecimal(String.valueOf(1 + Double.parseDouble(taxRate))), 2, BigDecimal.ROUND_HALF_UP);
                        cell9.setCellValue(totalMoneyPreTax.toString());
                    } else {
                        cell9.setCellValue(0);
                    }
                    cell10.setCellValue(temp.getRepairDepotSapCode() == null ? StringUtils.EMPTY : temp.getRepairDepotSapCode());
                    String sapSendStatusDesc = "";
//                    if (temp.getSapSendStatus() == 0) {
//                        sapSendStatusDesc = "未发送";
//                    } else if (temp.getSapSendStatus() == 1) {
//                        sapSendStatusDesc = "已发送";
//                    } else if (temp.getSapSendStatus() == 2) {
//                        sapSendStatusDesc = "发送失败";
//                    } else if (temp.getSapSendStatus() == 3 || temp.getSapSendStatus() == 4) {
//                        sapSendStatusDesc = "需重新发送";
//                    } else if (temp.getSapSendStatus() == 5) {
//                        sapSendStatusDesc = "发送中";
//                    }
                    cell11.setCellValue(sapSendStatusDesc);
                    cell12.setCellValue(ComUtil.dateToString(temp.getVehicleReciveTime()));
                    cell13.setCellValue(ComUtil.dateToString(temp.getTaskInflowTime()));
                    cell14.setCellValue(temp.getResurveyFlag().intValue() == 0 ? "否" : "是");
                    cell15.setCellValue(temp.getTimeOut().equals("0") ? "超时" : "未超时");
                    cell16.setCellValue(temp.getVehicleCheckTime() == null ? StringUtils.EMPTY : ComUtil.dateToString(temp.getVehicleCheckTime()));
                    cell17.setCellValue(temp.getInsuranceCompanyName());
                    cell18.setCellValue(temp.getRepairGrade());
                    cell19.setCellValue(temp.getOrgName());
                    cell20.setCellValue("否");
                    cell21.setCellValue(temp.getDeclareNo());
                    String declareSettlementStatus = "";
                    // 结算状态 1=已生成 2=待结算 3=结算中 4=已结算 5=已作废
                    if (null != temp.getDeclareSettlementStatus()) {
                        if (temp.getDeclareSettlementStatus() == 1) {
                            declareSettlementStatus = "已生成";
                        } else if (temp.getDeclareSettlementStatus() == 2) {
                            declareSettlementStatus = "待结算";
                        } else if (temp.getDeclareSettlementStatus() == 3) {
                            declareSettlementStatus = "结算中";
                        } else if (temp.getDeclareSettlementStatus() == 4) {
                            declareSettlementStatus = "已结算";
                        } else if (temp.getDeclareSettlementStatus() == 5) {
                            declareSettlementStatus = "已作废";
                        }
                    }
                    cell22.setCellValue(declareSettlementStatus);
                    cell23.setCellValue(temp.getSettlementNo());
                    String settlementStatus = "";
                    if (null != temp.getSettlementStatus()) {
                        if (temp.getSettlementStatus() == 1) {
                            settlementStatus = "未提交";
                        } else if (temp.getSettlementStatus() == 2) {
                            settlementStatus = "审核中";
                        } else if (temp.getSettlementStatus() == 3) {
                            settlementStatus = "审批通过";
                        } else if (temp.getSettlementStatus() == 4) {
                            settlementStatus = "审批拒绝";
                        } else if (temp.getSettlementStatus() == 5) {
                            settlementStatus = "已作废";
                        } else if (temp.getSettlementStatus() == 6) {
                            settlementStatus = "已关闭";
                        }
                    }
                    cell24.setCellValue(settlementStatus);
                    int reviewToSelFeeFlagValue = temp.getReviewToSelFeeFlag();
                    String repairTypeIdValue = String.valueOf(temp.getRepairTypeId());
                    if (reviewToSelFeeFlagValue == 0 && "1".equals(repairTypeIdValue)) {
                        BigDecimal maxVehicleInsuranceTotalAmount = new BigDecimal(temp.getRepairReviewTotalAmount()).multiply(BigDecimal.valueOf(1.3));
                        if (maxVehicleInsuranceTotalAmount.compareTo(new BigDecimal(temp.getVehicleInsuranceTotalAmount())) < 0) {
                            cell20.setCellValue("是");
                        }
                    }
                }
                VehicleCheckTaskScheduleBO last = list.get(list.size() - 1);
                Date lastUpdateTime = last.getUpdateTime();
                long lastId = last.getId();
                vehicleCheckTaskScheduleQueryBO.setLastId(lastId);
                vehicleCheckTaskScheduleQueryBO.setLastUpdateTime(lastUpdateTime);
                if (list.size() == exportSize) {
                    list = repairTaskMapper.queryScheduleExport(vehicleCheckTaskScheduleQueryBO);
                } else {
                    list = null;
                }
            }

            SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateStr = dateformat.format(System.currentTimeMillis());
            String execelName = "vehicleCheck" + dateStr + Contants.XLSX;
            // File file = new File(exportDirector + execelName);
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + execelName);
            OutputStream out = response.getOutputStream();
            try {
                // out = new FileOutputStream(file);
                workbook.write(out);
                out.flush();// 缓存清空输出
            } catch (FileNotFoundException e1) {
                log.error(e1.getMessage(), e1);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                if (workbook != null) {
                    try {
                        workbook.close();
                    } catch (IOException e) {
                        log.error(e.getMessage(), e);
                    }
                    workbook = null;
                }
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        log.error(e.getMessage(), e);
                    }
                    out = null;
                }
            }
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
            return vo;
        }
        return vo;
    }

    /**
     * 查看车辆车辆验收任务进度列表-手机端
     *
     * @param vehicleCheckTaskScheduleQueryBO
     * @param request
     * @return
     */
    @Override
    public VehicleCheckTaskScheduleCountBO queryVehicleCheckInfo(
            VehicleCheckTaskScheduleQueryBO vehicleCheckTaskScheduleQueryBO, HttpServletRequest request) {

        if (StringUtils.isEmpty(vehicleCheckTaskScheduleQueryBO.getOrgId())) {
            ComModel comModel = ComUtil.getUserInfo(request);
            vehicleCheckTaskScheduleQueryBO.setOrgId(comModel.getOrgId());
        }
        PageHelper.startPage(vehicleCheckTaskScheduleQueryBO.getPageNum(),
                vehicleCheckTaskScheduleQueryBO.getPageSize());
        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getVehicleNo())) {
            vehicleCheckTaskScheduleQueryBO.setVehicleNo(vehicleCheckTaskScheduleQueryBO.getVehicleNo().trim());
        }
        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getTaskNo())) {
            vehicleCheckTaskScheduleQueryBO.setTaskNo(vehicleCheckTaskScheduleQueryBO.getTaskNo().trim());
        }
        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getRepairDepotName())) {
            vehicleCheckTaskScheduleQueryBO
                    .setRepairDepotName(vehicleCheckTaskScheduleQueryBO.getRepairDepotName().trim());
        }
        if (StringUtils.isNotEmpty(vehicleCheckTaskScheduleQueryBO.getVin())) {
            vehicleCheckTaskScheduleQueryBO.setVin(vehicleCheckTaskScheduleQueryBO.getVin().trim());
        }
        List<VehicleCheckTaskScheduleBO> list = repairTaskMapper.queryScheduleInfo(vehicleCheckTaskScheduleQueryBO);
        PageInfo<VehicleCheckTaskScheduleBO> pageInfo = new PageInfo<>(list);
        VehicleCheckTaskScheduleCountBO vcbo = new VehicleCheckTaskScheduleCountBO();
        Long applyCount = repairTaskMapper.queryApplyCount(vehicleCheckTaskScheduleQueryBO);
        Long waitCount = repairTaskMapper.queryWaitCount(vehicleCheckTaskScheduleQueryBO);
        // Long finishCount =
        // repairTaskMapper.queryFinishCount(vehicleCheckTaskScheduleQueryBO);
        vcbo.setPageinfo(pageInfo);
        vcbo.setApplyCount(applyCount);
        vcbo.setWaitCount(waitCount);
        // vcbo.setFinishCount(finishCount);
        return vcbo;

    }

    /**
     * 验收通过
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public DefaultServiceRespDTO updateAcceptance(CheckAcceptancePassBO checkAcceptancePassBO,
                                                  HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(checkAcceptancePassBO.getTaskNo());
        RepairTask repairTaskRet = repairTaskMapper.selectByTaskNoV(checkAcceptancePassBO.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于车辆验收环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于车辆验收环节
        if (Contants.CURRENT_TACHE_VEHICLE_CHECK != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于车辆验收环节，请重新刷新一览页面");
            return vo;
        }

        // 检查车辆验收环节是否是待验收
        if (Contants.VEHICLE_CHECK_ACCEPTANCE != taskSchedule.getVehicleCheckTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于车辆验收环节的待验收状态，请重新刷新一览页面");
            return vo;
        }

        Date now = new Date();
        String currentTime = ComUtil.getSystemDate(now, ComUtil.DATE_TYPE1);

        ComModel comModel = ComUtil.getUserInfo(request);
        // 保养任务判断是否转维修
        Integer repairTypeId = repairTaskMapper.queryRepairTypeId(checkAcceptancePassBO.getTaskNo());
       /* if (repairTypeId != null && repairTypeId == 3 && checkAcceptancePassBO.getRepairFlag() == 1) {
            checkAcceptancePassBO.setMaintainToRepairFlag("-1");
            checkAcceptancePassBO.setCurrentTache(String.valueOf(Contants.CURRENT_TACHE_MAINTENA_TO_REPAIR));
        }*/
        checkAcceptancePassBO.setCheckResultFlag("0");
        checkAcceptancePassBO.setUpdateTime(currentTime);
        checkAcceptancePassBO.setUpdateUserId(comModel.getCreateOperId().toString());
        checkAcceptancePassBO.setUpdateUserName(comModel.getCreateOperName());
        checkAcceptancePassBO.setVehicleCheckTaskSchedule(String.valueOf(Contants.VEHICLE_CHECK_COMPLETED));
        checkAcceptancePassBO.setVehicleCheckTime(currentTime);
        // 客户直付金额 -> 影响用户承担金额
        if (ObjectUtil.isNotEmpty(checkAcceptancePassBO.getCustAmount())) {
            if (repairTaskRet.getCustAmount().compareTo(checkAcceptancePassBO.getCustAmount()) != 0 || !repairTaskRet.getCustPaysDirect().equals(checkAcceptancePassBO.getCustPaysDirect())) {
                // 单独修改客户直付金额逻辑（用户承担=客户直付金额）
                log.info("任务【{}】单独修改客户直付金额", repairTaskRet.getTaskNo());
                checkAcceptancePassBO.setUserAssumedAmount(checkAcceptancePassBO.getCustAmount());
                checkAcceptancePassBO.setNotUserAssumedAmount(BigDecimal.ZERO);
            }
        }
        // 重新计算自费金额
        RepairAmountDTO repairAmountDTO = new RepairAmountDTO(
                repairTaskRet.getTaskNo(),
                repairTaskRet.getVehicleInsuranceTotalAmount(),
                null == checkAcceptancePassBO.getUserAssumedAmount() ? repairTaskRet.getUserAssumedAmount() : checkAcceptancePassBO.getUserAssumedAmount(),
                repairTaskRet.getEstimatedClaimAmount());
        BigDecimal selfFundedAmount = repairTaskService.getSelfFundedAmount(repairAmountDTO, manualUtils.getAccidentDamage(repairTaskRet.getAccidentNo(), comModel.getToken()));
        checkAcceptancePassBO.setSelfFundedAmount(selfFundedAmount);
        // 维修任务垫付更新
        int count = repairTaskMapper.updateByTaskNo(checkAcceptancePassBO);
        if (count <= 0) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.NO_CAR_MESSAGE);
            return vo;
        }

        // 业财对接-同步自费金额账单
        bfcCostService.syncSelfFundedAmountBill(repairTaskRet.getTaskNo());

        List<VehicleRepairPic> allVehicleRepairPics = vehicleRepairPicMapper.getAllPicListByTaskNo(checkAcceptancePassBO.getTaskNo());
        List<VehicleRepairPic> insertPicList;
        List<VehicleRepairPic> deletePicList;
        try {
            // 客户直付凭证
            insertPicList = new ArrayList<>(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, checkAcceptancePassBO.getCustPicture(), BigDecimal.valueOf(22)),
                    BigDecimal.valueOf(22),
                    checkAcceptancePassBO.getTaskNo(),
                    comModel));
            deletePicList = new ArrayList<>(RepairTaskUtils.getDeletePics(allVehicleRepairPics, checkAcceptancePassBO.getCustPicture(), BigDecimal.valueOf(22)));
            // 验收图片
            insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, checkAcceptancePassBO.getRepairPicture(), BigDecimal.valueOf(2)),
                    BigDecimal.valueOf(2),
                    checkAcceptancePassBO.getTaskNo(),
                    comModel));
            deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, checkAcceptancePassBO.getRepairPicture(), BigDecimal.valueOf(2)));
            // 验收视频
            insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, checkAcceptancePassBO.getCheckVideo(), Contants.CHECKVIDEO),
                    Contants.CHECKVIDEO,
                    checkAcceptancePassBO.getTaskNo(),
                    comModel));
            deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, checkAcceptancePassBO.getCheckVideo(), Contants.CHECKVIDEO));

            if (CollectionUtils.isNotEmpty(deletePicList)) {
                // 批量删除
                vehicleRepairPicMapper.delMaterialPic(deletePicList.stream().map(VehicleRepairPic::getId).collect(Collectors.toList()));
            }

        } catch (MtcBusinessRuntimeException e) {
            log.error("维修任务保存，包装图片异常！");
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }

        String insertRepairPicLog = "";
        if (CollectionUtils.isNotEmpty(insertPicList)) {
            // 批量插入
            vehicleRepairPicMapper.batchInsert(insertPicList);
            if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(2)))) {
                insertRepairPicLog += "新增上传验收图片；";
            }
            if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(Contants.CHECKVIDEO))) {
                insertRepairPicLog += "新增上传验收视频；";
            }
        }

        int countPic = vehicleRepairPicMapper.queryPicCount(checkAcceptancePassBO.getTaskNo());
        if (countPic < 0) {
            throw new MtcBusinessRuntimeException(Contants.NO_PIC_MESSAGE, "-1");
        }

        RepairTask repairTask = repairTaskMapper.selectByTaskNo(checkAcceptancePassBO.getTaskNo());
        MtcManualTaskDTO mtcManualTaskDTO = manualUtils.getMtcManualTaskByTaskNo(repairTask.getTaskNo());
        String opeContent = Contants.VEHICLE_ACCEPTANCE_OK;
        // 维修垫付日志inject
        Integer custPaysDirect = checkAcceptancePassBO.getCustPaysDirect();
        if (ObjectUtil.isNotEmpty(custPaysDirect)) {
            if (custPaysDirect > 0) {
                if (custPaysDirect == 1) {
                    opeContent = opeContent + " 是否客户直付:是";
                } else if (custPaysDirect == 2) {
                    opeContent = opeContent + " 是否客户直付:否";
                }
            }
            if (ObjectUtil.isNotEmpty(checkAcceptancePassBO.getCustAmount())) {
                opeContent = opeContent + ", 金额" + NumberUtil.decimalFormat("0.00", checkAcceptancePassBO.getCustAmount()) + "；";
            }
        }
        if (StringUtils.isNotBlank(insertRepairPicLog)) {
            opeContent += insertRepairPicLog;
        }
        saveSelectiveLog1(repairTask.getId(), Contants.VEHICLE_ACCEPTANCE_MESSAGE,
                Contants.TABLENAME_MTC_REPAIR_TASK, opeContent, comModel);

        //验收通过记录计提日志
        if (ArrayUtils.contains(Contants.UPD_MANUAL_LIST, repairTypeId.toString())) {
            StringBuilder content = new StringBuilder();
            content.append("验收完成，").append("任务编号：").append(repairTask.getTaskNo()).append("，");
            this.doManualLogContent(repairTaskRet, checkAcceptancePassBO, content);

            manualUtils.saveManualLog(mtcManualTaskDTO.getId(), String.valueOf(Contants.CURRENT_TACHE_CHECK),
                    content.toString(), comModel);
        }

        if (ArrayUtils.contains(Contants.NO_MANUAL_LIST, repairTypeId.toString())) {
            manualUtils.saveManualLog(mtcManualTaskDTO.getId(), String.valueOf(Contants.CURRENT_TACHE_CHECK),
                    "验收通过，任务编号：" + repairTask.getTaskNo(), comModel);
        }

        LogUtil.saveMtcOperatorLog(mtcOperatorLogMapper, repairTask.getId(), "待处理", "材料收集", comModel,
                new Timestamp(System.currentTimeMillis()), 70L);

        // 验收成功，将该任务占据的预算更新为已使用预算
        // 更新占据预算
        MtcBudgetManagement budgetManagement = mtcBudgetManagementMapper
                .selectByOrgIdAndYear(repairTask.getOrgId(), ComUtil.getCurrentYear());
        if (budgetManagement != null) {
            if (repairTask.getRepairInsuranceTotalAmount() != null
                    && repairTask.getRepairInsuranceTotalAmount().doubleValue() > 0) {
                // 减去占据预算
                budgetManagement.setOccupyBudget(budgetManagement.getOccupyBudget()
                        .subtract(repairTask.getRepairInsuranceTotalAmount()));
                if (budgetManagement.getOccupyBudget().doubleValue() < 0) {
                    budgetManagement.setOccupyBudget(new BigDecimal(0.00));
                }
                // 加上已使用预算
                budgetManagement.setUsedBudget(
                        budgetManagement.getUsedBudget().add(repairTask.getRepairInsuranceTotalAmount()));
                // 计算剩余预算
                budgetManagement.setSurplusBudget(
                        budgetManagement.getAvailableBudget().subtract(budgetManagement.getUsedBudget()));
                if (budgetManagement.getSurplusBudget().doubleValue() < 0) {
                    budgetManagement.setSurplusBudget(new BigDecimal(0.00));
                }
                budgetManagement.setUpdateOperId(comModel.getUpdateOperId());
                budgetManagement.setUpdateOperName(comModel.getUpdateOperName());
                budgetManagement.setUpdateTime(new Date());
                mtcBudgetManagementMapper.updateByPrimaryKeySelective(budgetManagement);
            }
        }

        // 如果是保养待维修任务，通知调度关联的保养任务已完成，否则走其他任务流程
        if (checkAcceptancePassBO.getTaskNo().startsWith("BYZX") || (repairTypeId == 3 && checkAcceptancePassBO.getRepairFlag() == 0)) {
            String associatedTaskNo = "";
            if (checkAcceptancePassBO.getTaskNo().startsWith("BYZX")) {
                associatedTaskNo = repairTaskMapper.queryAssociatedTaskNo(checkAcceptancePassBO.getTaskNo());
            } else if (repairTypeId == 3 && checkAcceptancePassBO.getRepairFlag() == 0) {
                associatedTaskNo = checkAcceptancePassBO.getTaskNo();
            }
            if (StringUtils.isNotBlank(associatedTaskNo)) {
                // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
                int idsUseMode = 1;
                try {
                    idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
                } catch (Exception e) {
                    idsUseMode = 1;
                }
                if (idsUseMode == 1) {
                    MtcMoveUpGettingVehicleBO mtcMoveUpGettingVehicle = new MtcMoveUpGettingVehicleBO();
                    // 获得维修厂省市区
                    mtcMoveUpGettingVehicle.setDispatchTaskSeq(associatedTaskNo);
                    // 获得维修厂省市区
                    AtcPricingAdoptBO repairTaskArea = repairDepotInfoMapper
                            .getDepotArea(repairTaskMapper.repairDepotId(associatedTaskNo));
                    if (repairTaskArea != null) {
                        mtcMoveUpGettingVehicle.setProvinceName(repairTaskArea.getProvinceName());
                        mtcMoveUpGettingVehicle.setCityName(repairTaskArea.getCityName());
                        mtcMoveUpGettingVehicle.setAreaName(repairTaskArea.getAreaName());
                    }
                    try {
                        HttpRequest.postMethod(
                                apolloPropertyUtils.getString("evcard.ids.dns")
                                        + "repair-factory-new/mtcMoveUpGettingVehicle",
                                mtcMoveUpGettingVehicle);
                    } catch (Exception ex) {
                        log.info("调度系统异常:" + ex.toString());
                        throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                    }
                }
            }
        } else if (repairTask.getOrigin() == 0) {
            if (repairTypeId != 3) {
                // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
                int idsUseMode = 1;
                try {
                    idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
                } catch (Exception e) {
                    idsUseMode = 1;
                }

                if (idsUseMode == 1) {
                    String info;
                    // 调用调度接口
                    try {
                        info = HttpRequest.putMethod(apolloPropertyUtils.getString("evcard.ids.dns")
                                        + "repair-factory-new/mtcCompleteRepareTask/" + checkAcceptancePassBO.getTaskNo(),
                                null);
                    } catch (Exception ex) {
                        log.info("调度系统异常:" + ex.toString());
                        throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                    }

                    if (StringUtils.isBlank(info)) {
                        throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                    } else {
                        log.info(info);
                        vo = JSON.parseObject(info, DefaultServiceRespDTO.class);

                        if (vo.getCode() != 0) {
                            throw new MtcBusinessRuntimeException("调度系统错误信息：" + vo.getMessage(),
                                    String.valueOf(vo.getCode()));
                        }
                    }
                }
            }
        } else if (repairTask.getOrigin() == 1) {
            // 修改车管系统任务状态
            ComUtil.updateBdpTaskInfoStatus(bdpMtcTaskInfoService, checkAcceptancePassBO.getTaskNo(), 3,
                    comModel, vo);
        }

        //通知车辆验收通过事件
        vehicleMaintenanceCheckSubject.notifyCheckApprove(new BaseRepairEvent(now, checkAcceptancePassBO.getTaskNo()));
        // 记录任务状态持续时间
        vehicleMaintenanceCheckSubject.recordPeriod(new BaseRepairEvent(now, checkAcceptancePassBO.getTaskNo()));

        //TODO 通知长租系统 车辆验证 车损确认为自认损耗通知长租
        String checkResultFlag = checkAcceptancePassBO.getCheckResultFlag();
        if ("0".equals(checkResultFlag)) {
            Integer carDamageType = checkAcceptancePassBO.getCarDamageType();
            if (carDamageType != null && carDamageType == 2) {
                try {
                    Map<String, Object> param = new HashMap<>(8);
                    param.put("vehiclePlate", repairTask.getVehicleNo());
                    param.put("actualVin", repairTask.getVin());
                    param.put("workNo", repairTask.getTaskNo());
                    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//
                    param.put("maintainBeginDate", ComUtil.dateToString(repairTask.getVehicleReciveTime()));
                    param.put("maintainEndDate", currentTime);
                    BaseResultBean resultBean = sendPost("saveVehicleMaintenance", JSON.toJSONString(param));
                    log.error("通知长租系统结果===========》{}", JSON.toJSONString(resultBean));
                } catch (Exception e) {
                    log.error("通知长租系统异常了:{}", e);
                }
            }
            // 设置约定取车时间，并且发短信
            try {
                this.doReplaceVehicleTask(repairTask.getTaskNo(), 1);
            } catch (Exception e) {
                log.info("约定取车时间设置错误");
            }
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                ThreadPoolUtils.EXECUTOR.submit(() -> {
                    if (repairTaskRet.getRepairDepotType() == 2) {
                        // 非合作修理厂 业财对接-同步账单完成
                        bfcCostService.notifyCompleteBill(repairTaskRet.getTaskNo(), comModel.getToken());
                    }

                    // 梧桐维修任务增加日志
                    SsoUserBaseInfoDto ssoUserBaseInfoDto = ssoUserService.getUserByUserName(comModel.getUserName());
                    repairTaskService.addRepairTaskLogForWt(new AddRepairTaskLogForWtDTO(
                            checkAcceptancePassBO.getTaskNo(), "车辆验收通过", "",
                            comModel.getUserName(), ssoUserBaseInfoDto.getdAccount()
                    ));
                    // 调用梧桐-维修任务附加费变更
                    if (repairTask.getRepairTypeId().intValue() == 2) {
                        UpdateRepairTaskAdditionalFeeDTO updateRepairTaskAdditionalFeeDTO = new UpdateRepairTaskAdditionalFeeDTO();
                        updateRepairTaskAdditionalFeeDTO.setTaskNo(repairTask.getTaskNo());
                        updateRepairTaskAdditionalFeeDTO.setOperateType("验收通过");
                        updateRepairTaskAdditionalFeeDTO.setOperateName(comModel.getCreateOperName());
                        updateRepairTaskAdditionalFeeDTO.setOperateAccount(comModel.getUserName());
                        updateRepairTaskAdditionalFee(updateRepairTaskAdditionalFeeDTO);
                    }
                });
            }
        });

        return vo;
    }

    @Override
    public DefaultServiceRespDTO updateAcceptanceFinish(CheckAcceptancePassBO bo, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = this.updateAcceptance(bo, request);
        // 车辆验收通过，修改费用记录
        this.editFeeRecord(bo);

        // t验收通过 修改附加费信息
        this.updAdditionalChargeRecord(bo, request);

        mdRpc.updateMdOccupyRecord(bo);
        // 通知业财系统验收完成
     /*   try {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    Thread thread = new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(2000);
                                HttpRequest.sendGet(apolloPropertyUtils.getString("bfsVehicleRepairBillFinishUrl") + bo.getTaskNo(),
                                        null);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                    thread.start();
                }
            });
        } catch (Exception e) {
            log.error("通知业财系统验收完成异常", e);
        }*/
        return respDTO;
    }

    /**
     * 验收通过修改车管附加费信息
     *
     * @param bo
     */
    public void updAdditionalChargeRecord(CheckAcceptancePassBO bo, HttpServletRequest request) {

        BdpUpdateAddChargeDTO bdpUpdateAddChargeDTO = new BdpUpdateAddChargeDTO();

        log.info("验收通过修改车管附加费信息start:CheckAcceptancePassBO:{}", JSON.toJSONString(bo));
        try {
            RepairTask repairTask = repairTaskMapper.selectByTaskNoV(bo.getTaskNo());
            bdpUpdateAddChargeDTO.setRepairTaskAmount(repairTask.getVehicleInsuranceTotalAmount());
            bdpUpdateAddChargeDTO.setType(1);
            bdpUpdateAddChargeDTO.setRepairNo(bo.getTaskNo());

            log.info("验收通过修改车管附加费信息请求参数:{}", JSON.toJSONString(bdpUpdateAddChargeDTO));
            bdpMtcTaskInfoService.updateAddChargeByTaskNo(bdpUpdateAddChargeDTO, request.getRemoteUser());
        } catch (Exception e) {
            log.error("验收通过修改车管附加费信息异常", e);
        }
    }

    /**
     * 调度维修系统更新费用记录
     *
     * @param bo
     */
    private void editFeeRecord(CheckAcceptancePassBO bo) {
        log.info("调度维修系统更新费用记录start:CheckAcceptancePassBO:" + JSON.toJSONString(bo));
        try {
            RepairTask repairTask = repairTaskMapper.selectByTaskNo(bo.getTaskNo());
            if (org.apache.commons.lang.StringUtils.isEmpty(repairTask.getAssociatedOrder())) {
                return;
            }
            String associatedOrder = repairTask.getAssociatedOrder();
            String substring = associatedOrder.substring(0, 2);
            if (!substring.equals("MC")) {
                return;
            }
            EditFeeRecordReq editFeeRecordReq = new EditFeeRecordReq();
            editFeeRecordReq.setRepairTaskNo(bo.getTaskNo());
            editFeeRecordReq.setCarDamageFee(repairTask.getRepairInsuranceTotalAmount().toString());
            CurrentUser currentUser = new CurrentUser();
            currentUser.setUserId(bo.getUpdateUserId());
            currentUser.setUserName(bo.getUpdateUserName());
            editFeeRecordReq.setCurrentUser(currentUser);
            editFeeRecordReq.setContractId(repairTask.getAssociatedOrder());
            RepairDetailDTO repairDetailDTO = mtcRepairTaskService.queryRepairDetail(bo.getTaskNo());
            editFeeRecordReq.setVin(repairDetailDTO.getVin());
            // 追偿状态（ 2：确认追偿 3:不予追偿）
            editFeeRecordReq.setRecoveryStatus(2);
            mdRpc.editFeeRecord(editFeeRecordReq);
            log.info("调度维修系统更新费用记录end:");
        } catch (Exception e) {
            log.error("调度维修系统更新费用记录异常", e);
        }
    }

    /**
     * 验收不通过
     *
     * @param checkAcceptanceRejectBO
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updateAcceptanceReject(CheckAcceptanceRejectBO checkAcceptanceRejectBO,
                                                        HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        ComModel comModel = ComUtil.getUserInfo(request);
        vo.setMessage(Contants.SUCCESS_INFO);

        /*if (checkAcceptanceRejectBO.getCarDamageType() == null || checkAcceptanceRejectBO.getCarDamageType() == -1){
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("请确认车损");
            return vo;
        }*/

        if (StringUtils.isEmpty(checkAcceptanceRejectBO.getTaskNo())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.NO_TASKNO_MESSAGE);
            return vo;
        } else if (StringUtils.isEmpty(checkAcceptanceRejectBO.getRemark())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.NO_REMAEK_MESSAGE);
            return vo;
        } else {
            // 取得任务状态详情
            TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(checkAcceptanceRejectBO.getTaskNo());

            if (null == taskSchedule) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("已经不处于车辆验收环节，请重新刷新一览页面");
                return vo;
            }

            // 检查当前环节是否处于车辆验收环节
            if (Contants.CURRENT_TACHE_VEHICLE_CHECK != taskSchedule.getCurrentTache()) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("已经不处于车辆验收环节，请重新刷新一览页面");
                return vo;
            }

            // 检查车辆验收环节是否是待验收
            if (Contants.VEHICLE_CHECK_ACCEPTANCE != taskSchedule.getVehicleCheckTaskSchedule()) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("已经不处于车辆验收环节的待验收状态，请重新刷新一览页面");
                return vo;
            }

            Timestamp updateTime = new Timestamp(System.currentTimeMillis());
            CheckAcceptanceBO checkAcceptanceBO = new CheckAcceptanceBO();
            checkAcceptanceBO.setCheckResultFlag("1");
            checkAcceptanceBO.setCurrentTache(String.valueOf(Contants.CURRENT_TACHE_VEHICLE_REPAIR));
            checkAcceptanceBO.setRemark(checkAcceptanceRejectBO.getRemark());
            checkAcceptanceBO.setTaskNo(checkAcceptanceRejectBO.getTaskNo());
            checkAcceptanceBO.setUpdateTime(updateTime.toString());
            checkAcceptanceBO.setUpdateUserId(comModel.getCreateOperId().toString());
            checkAcceptanceBO.setUpdateUserName(comModel.getCreateOperName());
            checkAcceptanceBO.setVehicleRepairTaskSchedule(String.valueOf(Contants.VEHICLE_REPAIR_REJECT));
            checkAcceptanceBO.setVehicleCheckTaskSchedule("-1");
            checkAcceptanceBO.setCarDamageType(checkAcceptanceRejectBO.getCarDamageType());

            RepairTask repairTaskRet = repairTaskMapper.selectByTaskNoV(checkAcceptanceRejectBO.getTaskNo());
            CheckAcceptancePassBO passBO = new CheckAcceptancePassBO();
            passBO.setClaimsFlag(checkAcceptanceRejectBO.getClaimsFlag());
            passBO.setEstimatedClaimAmount(checkAcceptanceRejectBO.getEstimatedClaimAmount());
            /*MtcManualTask mtcManualTask = new MtcManualTask();
            // 计提逻辑处理
            if(ArrayUtils.contains(Contants.MANUAL_LIST, repairTaskRet.getRepairTypeId().toString())){
                BeanUtils.copyProperties(checkAcceptanceBO, passBO);
                passBO.setClaimsFlag(checkAcceptanceRejectBO.getClaimsFlag());
                passBO.setEstimatedClaimAmount(checkAcceptanceRejectBO.getEstimatedClaimAmount());
                mtcManualTask = manualUtils.doManualTask(passBO);
            }*/
            int count = repairTaskMapper.updateByTaskNoReject(checkAcceptanceBO);
            if (count > 0) {
                List<VehicleRepairPic> allVehicleRepairPics = vehicleRepairPicMapper.getAllPicListByTaskNo(checkAcceptanceRejectBO.getTaskNo());
                List<VehicleRepairPic> insertPicList;
                List<VehicleRepairPic> deletePicList;
                try {
                    // 验收图片
                    insertPicList = new ArrayList<>(RepairTaskUtils.transferStringToVehicleRepairPic(
                            RepairTaskUtils.getInsertPics(allVehicleRepairPics, checkAcceptanceRejectBO.getRepairPicture(), BigDecimal.valueOf(2)),
                            BigDecimal.valueOf(2),
                            checkAcceptanceRejectBO.getTaskNo(),
                            comModel));
                    deletePicList = new ArrayList<>(RepairTaskUtils.getDeletePics(allVehicleRepairPics, checkAcceptanceRejectBO.getRepairPicture(), BigDecimal.valueOf(2)));
                    // 验收视频
                    insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                            RepairTaskUtils.getInsertPics(allVehicleRepairPics, checkAcceptanceRejectBO.getCheckVideo(), Contants.CHECKVIDEO),
                            Contants.CHECKVIDEO,
                            checkAcceptanceRejectBO.getTaskNo(),
                            comModel));
                    deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, checkAcceptanceRejectBO.getCheckVideo(), Contants.CHECKVIDEO));
                } catch (MtcBusinessRuntimeException e) {
                    log.error("维修任务保存，包装图片异常！");
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage(e.getMessage());
                    return vo;
                }
                if (CollectionUtils.isNotEmpty(deletePicList)) {
                    // 批量删除
                    vehicleRepairPicMapper.delMaterialPic(deletePicList.stream().map(VehicleRepairPic::getId).collect(Collectors.toList()));
                }
                String insertRepairPicLog = "";
                if (CollectionUtils.isNotEmpty(insertPicList)) {
                    // 批量插入
                    vehicleRepairPicMapper.batchInsert(insertPicList);
                    if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(2)))) {
                        insertRepairPicLog += "新增上传验收图片；";
                    }
                    if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(Contants.CHECKVIDEO))) {
                        insertRepairPicLog += "新增上传验收视频；";
                    }
                }

                RepairTask repairTask = repairTaskMapper.selectByTaskNo(checkAcceptanceRejectBO.getTaskNo());
                MtcManualTaskDTO mtcManualTaskDTO = manualUtils.getMtcManualTaskByTaskNo(repairTask.getTaskNo());
                String opeContent = Contants.VEHICLE_ACCEPTANCE_NO;
                if (StringUtils.isNotBlank(insertRepairPicLog)) {
                    opeContent = opeContent + "；" + insertRepairPicLog;
                }
                saveSelectiveLog1(repairTask.getId(), Contants.VEHICLE_ACCEPTANCE_MESSAGE,
                        Contants.TABLENAME_MTC_REPAIR_TASK, opeContent, comModel);
                MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
                mtcProcessLog.setUpdateOperId(comModel.getUpdateOperId());
                mtcProcessLog.setUpdateOperName(comModel.getUpdateOperName());
                mtcProcessLog.setUpdateTime(updateTime);
                mtcProcessLog.setCreateOperId(comModel.getUpdateOperId());
                mtcProcessLog.setCreateOperName(comModel.getUpdateOperName());
                mtcProcessLog.setCreateTime(updateTime);
                mtcProcessLog.setRemark("车辆维修");
                mtcProcessLog.setRecoderId(repairTask.getId() + StringUtils.EMPTY);
                mtcProcessLog.setStatus(0);
                mtcProcessLog.setOpeContent("被驳回");
                mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
                mtcProcessLog.setCurrentTache((long) Contants.CURRENT_TACHE_VEHICLE_REPAIR);
                // 添加流程log
                mtcOperatorLogMapper.saveSelective(mtcProcessLog);
                //触发车辆验收不通过 通知
                vehicleMaintenanceCheckSubject.notifyCheckReject(new BaseRepairEvent(updateTime, checkAcceptanceRejectBO.getTaskNo()));
                // 记录任务状态持续时间
                vehicleMaintenanceCheckSubject.recordPeriod(new BaseRepairEvent(updateTime, checkAcceptanceRejectBO.getTaskNo()));
                //验收不通过记录计提日志
                if (ArrayUtils.contains(Contants.UPD_MANUAL_LIST, repairTask.getRepairTypeId().toString())) {
                    StringBuilder content = new StringBuilder().append("验收不通过，").append("任务编号：").append(repairTask.getTaskNo()).append("，");
                    this.doManualLogContent(repairTaskRet, passBO, content);
                    manualUtils.saveManualLog(mtcManualTaskDTO.getId(), String.valueOf(Contants.CURRENT_TACHE_CHECK),
                            content.toString(), comModel);
                }

                return vo;
            } else {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage(Contants.CHECK_REJECT_MESSAGE);
                return vo;
            }
        }
    }

    /**
     * 允许提车
     *
     * @param taskNo
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updateAllowCar(String taskNo, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        if (StringUtils.isEmpty(taskNo)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务编号不能为空");
            return vo;
        } else {

            // 取得任务状态详情
            TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(taskNo);

            if (null == taskSchedule) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("车辆验收环节已经不是提车申请状态，请重新刷新一览页面");
                return vo;
            }

            // 检查车辆验收环节是否是提车申请状态
            if (Contants.VEHICLE_CHECK_PICKUP != taskSchedule.getVehicleCheckTaskSchedule()) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("车辆验收环节已经不是提车申请状态，请重新刷新一览页面");
                return vo;
            }

            CheckAcceptancePassBO checkAcceptancePassBO = new CheckAcceptancePassBO();
            ComModel comModel = ComUtil.getUserInfo(request);
            checkAcceptancePassBO.setTaskNo(taskNo);
            if (taskSchedule.getVerificationLossTaskSchedule() == 320) {
                checkAcceptancePassBO.setCurrentTache("70");
                checkAcceptancePassBO.setMaterialCollectionTaskSchedule(700);
            }
            checkAcceptancePassBO.setUpdateTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            checkAcceptancePassBO.setUpdateUserId(comModel.getCreateOperId().toString());
            checkAcceptancePassBO.setUpdateUserName(comModel.getCreateOperName());
            checkAcceptancePassBO.setVehicleCheckTaskSchedule(String.valueOf(Contants.VEHICLE_CHECK_ALERADY));
            checkAcceptancePassBO.setVehicleCheckTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            checkAcceptancePassBO.setCheckResultFlag("0");
            // 2018/6/5 修复提前提车当前环节被更新的bug
            // checkAcceptancePassBO.setCurrentTache(String.valueOf(Contants.CURRENT_TACHE_INSURANCE_QUOTE));
            int count = repairTaskMapper.updateByTaskNo(checkAcceptancePassBO);
            vehicleAdvanceCheckTaskMapper.updateByTaskNo(taskNo, comModel.getCreateOperId().toString(),
                    comModel.getCreateOperName(), ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            if (count > 0) {
                RepairTask repairTask = repairTaskMapper.selectByTaskNo(checkAcceptancePassBO.getTaskNo());
                saveSelectiveLog1(repairTask.getId(), Contants.VEHICLE_ADVANCE, Contants.TABLENAME_MTC_REPAIR_TASK,
                        Contants.PERMISSION_TO_LIFT_OK, comModel);

                // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
                int idsUseMode = 1;
                try {
                    idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
                } catch (Exception e) {
                    idsUseMode = 1;
                }

                if (idsUseMode == 1) {
                    String info;

                    RepairTaskViewBO repairTaskView = repairTaskMapper
                            .getRepairTaskView(Long.valueOf(repairTask.getId()));

                    MtcMoveUpGettingVehicleBO mtcMoveUpGettingVehicle = new MtcMoveUpGettingVehicleBO();
                    // 获得维修厂省市区
                    mtcMoveUpGettingVehicle.setDispatchTaskSeq(taskNo);
                    // 获得维修厂省市区
                    AtcPricingAdoptBO repairTaskArea = repairDepotInfoMapper
                            .getDepotArea(repairTaskView.getRepairDepotId());
                    mtcMoveUpGettingVehicle.setProvinceName(repairTaskArea.getProvinceName());
                    mtcMoveUpGettingVehicle.setCityName(repairTaskArea.getCityName());
                    mtcMoveUpGettingVehicle.setAreaName(repairTaskArea.getAreaName());

                    // 调用调度的接口
                    try {
                        info = HttpRequest.postMethod(apolloPropertyUtils.getString("evcard.ids.dns")
                                + "repair-factory-new/mtcMoveUpGettingVehicle", mtcMoveUpGettingVehicle);
                    } catch (Exception ex) {
                        log.info("调度系统异常:" + ex.toString());
                        throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                    }

                    if (StringUtils.isBlank(info)) {
                        throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                    } else {
                        vo = JSON.parseObject(info, DefaultServiceRespDTO.class);

                        if (vo.getCode() != 0) {
                            throw new MtcBusinessRuntimeException(vo.getMessage(), String.valueOf(vo.getCode()));
                        }
                    }
                }

                return vo;
            } else {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("允许提车失败");
                return vo;
            }
        }

    }

    /**
     * 允许提车驳回
     */
    @Override
    @Transactional
    public DefaultServiceRespDTO updateRejectCar(CheckAcceptanceRejectBO checkAcceptanceRejectBO,
                                                 HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(checkAcceptanceRejectBO.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车辆验收环节已经不是提车申请状态，请重新刷新一览页面");
            return vo;
        }

        // 检查车辆验收环节是否是提车申请状态
        if (Contants.VEHICLE_CHECK_PICKUP != taskSchedule.getVehicleCheckTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("车辆验收环节已经不是提车申请状态，请重新刷新一览页面");
            return vo;
        }

        CheckAcceptanceBO checkAcceptanceBO = new CheckAcceptanceBO();
        Timestamp updateTime = new Timestamp(System.currentTimeMillis());
        ComModel comModel = ComUtil.getUserInfo(request);
        // 2018/6/5 修复提前提车当前环节被更新的bug
        // checkAcceptanceBO.setCurrentTache(String.valueOf(Contants.CURRENT_TACHE_INSURANCE_QUOTE));
        checkAcceptanceBO.setTaskNo(checkAcceptanceRejectBO.getTaskNo());
        checkAcceptanceBO.setUpdateUserId(String.valueOf(comModel.getCreateOperId()));
        checkAcceptanceBO.setUpdateUserName(comModel.getCreateOperName());
        checkAcceptanceBO.setUpdateTime(updateTime.toString());
        checkAcceptanceBO.setVehicleRepairTaskSchedule("-1");
        // checkAcceptanceBO.setInsuranceQuoteTaskSchedule(String.valueOf(Contants.INSURANCE_QUOTE_REJECT));
        checkAcceptanceBO.setVehicleCheckTaskSchedule("-1");
        checkAcceptanceBO.setRemark(checkAcceptanceRejectBO.getRemark());
        int count = repairTaskMapper.updateByTaskNoReject(checkAcceptanceBO);
        if (count > 0) {
            int count1 = vehicleAdvanceCheckTaskMapper.updateReject(checkAcceptanceRejectBO.getRemark(),
                    checkAcceptanceRejectBO.getTaskNo(), comModel.getCreateOperId().toString(),
                    comModel.getCreateOperName(), updateTime.toString());
            if (count1 > 0) {
                vehicleAdvanceCheckTaskMapper.deleteByTask(checkAcceptanceRejectBO.getTaskNo());
                RepairTask repairTask = repairTaskMapper.selectByTaskNo(checkAcceptanceRejectBO.getTaskNo());
                saveSelectiveLog1(repairTask.getId(), Contants.VEHICLE_ADVANCE, Contants.TABLENAME_MTC_REPAIR_TASK,
                        Contants.PERMISSION_TO_LIFT_NO, comModel);
                return vo;
            } else {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("提前提车驳回失败");
                return vo;
            }
        } else {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("提前提车驳回失败");
            return vo;
        }
    }

    @Override
    public DefaultServiceRespDTO exportfirstAllInfo(String orgId, String repairDepotId, HttpServletRequest request,
                                                    HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }

        // 限制导出单位
        String notExportedCompany = "";
        try {
            notExportedCompany = apolloPropertyUtils.getString("notExportedCompany");
        } catch (Exception e) {
            notExportedCompany = "";
        }

        if (!"".equals(notExportedCompany.trim())) {

            String[] companyList = notExportedCompany.split(Contants.COMMA_SIGN_SPLIT_NAME);

            if (Arrays.asList(companyList).contains(orgId)) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("环球总部和事业部无法导出");
                return vo;

            }

        }

        // 声明一个工作薄
        Workbook workbook = new SXSSFWorkbook(100);
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER); // 居中
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 垂直
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);// 左边框
        cellStyle.setBorderTop(CellStyle.BORDER_THIN);// 上边框
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);// 右边框
        // 获取系统当前时间
        Timestamp now = new Timestamp(System.currentTimeMillis());
        SimpleDateFormat dateFormat = new SimpleDateFormat(ComUtil.DATE_TYPE2);
        String currentTime = dateFormat.format(now);
        // 根据orgId查询子公司信息
        List<OrgInfoBO> orgList = orgInfoMapper.getAllSubOrgId(orgId);
        for (OrgInfoBO orgInfoBO : orgList) {
            List<String> subOrgIds = orgInfoMapper.getSubOrgIdIsExist(orgInfoBO.getOrgId());
            if (CollectionUtils.isNotEmpty(subOrgIds)) {
                continue;
            } else {
                List<String> repairDepotIds = new ArrayList<String>();
                if (StringUtils.isBlank(repairDepotId)) {
                    repairDepotIds = repairDepotInfoMapper.getRepairDepotIds(orgInfoBO.getOrgId());
                    if (CollectionUtils.isEmpty(repairDepotIds)) {
                        repairDepotIds.add(StringUtils.EMPTY);
                    }
                } else {
                    String[] ids = repairDepotId.split(",");
                    for (String rdid : ids) {
                        repairDepotIds.add(rdid);
                    }
                }
                log.debug("RepairTaskServiceImpl 车辆运营单位名称检索条件:" + orgInfoBO.getOrgId());
                log.debug("RepairTaskServiceImpl 修理厂名称检索条件:" + repairDepotIds.toString());
                // 每个子公司生成一个表格，子公司没有在修车辆就不创建sheet
                Integer num = repairTaskMapper.getRepairDepotInRepairingInfoNum(orgInfoBO.getOrgId(), null);
                if (num == 0) {

                    log.debug("走非零" + num);
                    continue;
                } else {

                    log.debug("走非零" + num);
                }

                Sheet sheet = workbook.createSheet(orgInfoBO.getOrgName());
                sheet.setColumnWidth(0, 10000);
                sheet.setColumnWidth(1, 6000);
                sheet.setColumnWidth(2, 5000);
                sheet.setColumnWidth(3, 5000);
                sheet.setColumnWidth(4, 5000);
                sheet.setColumnWidth(5, 4000);
                sheet.setColumnWidth(6, 4000);
                sheet.setColumnWidth(7, 4000);
                sheet.setColumnWidth(8, 6000);
                sheet.setColumnWidth(9, 5000);
                sheet.setColumnWidth(10, 7000);
                sheet.setColumnWidth(11, 5000);
                sheet.setColumnWidth(12, 4000);
                sheet.setColumnWidth(13, 7000);
                sheet.setColumnWidth(14, 8000);
                sheet.setColumnWidth(15, 6000);
                sheet.setColumnWidth(16, 5000);
                sheet.setColumnWidth(17, 6000);
                sheet.setColumnWidth(18, 5000);
                sheet.setColumnWidth(19, 5000);
                sheet.setColumnWidth(20, 8000);
                sheet.setColumnWidth(21, 4000);

                String[] headers = apolloPropertyUtils.getString("export.FirstPageAllInfo.title")
                        .split(Contants.COMMA_SIGN_SPLIT_NAME);

                int columnNum = headers.length;
                // 产生表格标题行
                Row row = sheet.createRow(0);
                for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
                    Cell cell = row.createCell(columnIndex);
                    cell.setCellValue(headers[columnIndex]);
                    cell.setCellStyle(cellStyle);
                }

                List<VehicleRepairNumBO> list = repairTaskMapper.getFirstPageInfo(orgInfoBO.getOrgId(), repairDepotIds,
                        currentTime);
                if (CollectionUtils.isNotEmpty(list)) {
                    int nextRowIndex = 0;
                    for (int index = 0; index < list.size(); index++) {
                        List<RepairDepotInRepairingBO> detaillist = repairTaskMapper
                                .getFirstPageExportInfo(orgInfoBO.getOrgId(), list.get(index).getRepairDepotId());
                        if (CollectionUtils.isNotEmpty(detaillist)) {
                            for (int i = 0; i < detaillist.size(); i++) {
                                row = sheet.createRow(nextRowIndex + i + 1);
                                // 分子公司
                                Cell cell0 = row.createCell(0);
                                // 修理厂名称
                                Cell cell1 = row.createCell(1);
                                // 当前在修车辆总数
                                Cell cell2 = row.createCell(2);
                                // 今日新增车辆数
                                Cell cell3 = row.createCell(3);
                                // 今日出厂车辆数
                                Cell cell4 = row.createCell(4);
                                // 在修A类数
                                Cell cell5 = row.createCell(5);
                                // 在修B类数
                                Cell cell6 = row.createCell(6);
                                // 在修C类数
                                Cell cell7 = row.createCell(7);
                                // 车牌号
                                Cell cell8 = row.createCell(8);
                                // 车型
                                Cell cell9 = row.createCell(9);
                                // 车架号
                                Cell cell10 = row.createCell(10);
                                // 修理类型
                                Cell cell11 = row.createCell(11);
                                // 修理级别
                                Cell cell12 = row.createCell(12);
                                // 车辆保险所属
                                Cell cell13 = row.createCell(13);
                                // 车辆接收时间
                                Cell cell14 = row.createCell(14);
                                // 换件项目
                                Cell cell15 = row.createCell(15);
                                // 换件项目（元）
                                Cell cell16 = row.createCell(16);
                                // 修理项目
                                Cell cell17 = row.createCell(17);
                                // 修理总价（元）
                                Cell cell18 = row.createCell(18);
                                // 汇总金额（元）
                                Cell cell19 = row.createCell(19);
                                // 任务编号
                                Cell cell20 = row.createCell(20);
                                // 业务状态
                                Cell cell21 = row.createCell(21);
                                cell0.setCellValue(detaillist.get(i).getOrgName());
                                cell0.setCellStyle(cellStyle);
                                cell1.setCellValue(list.get(index).getRepairDepotName());
                                cell1.setCellStyle(cellStyle);
                                cell2.setCellValue(list.get(index).getCurrentRepairNum());
                                cell2.setCellStyle(cellStyle);
                                cell3.setCellValue(list.get(index).getTodayAddNum());
                                cell3.setCellStyle(cellStyle);
                                cell4.setCellValue(list.get(index).getTodayOutNum());
                                cell4.setCellStyle(cellStyle);
                                cell5.setCellValue(list.get(index).getaRepairNum());
                                cell5.setCellStyle(cellStyle);
                                cell6.setCellValue(list.get(index).getbRepairNum());
                                cell6.setCellStyle(cellStyle);
                                cell7.setCellValue(list.get(index).getcRepairNum());
                                cell7.setCellStyle(cellStyle);
                                cell8.setCellValue(detaillist.get(i).getVehicleNo());
                                cell8.setCellStyle(cellStyle);
                                cell9.setCellValue(detaillist.get(i).getVehicleModelName());
                                cell9.setCellStyle(cellStyle);
                                cell10.setCellValue(detaillist.get(i).getVin());
                                cell10.setCellStyle(cellStyle);
                                cell11.setCellValue(detaillist.get(i).getRepairTypeName());
                                cell11.setCellStyle(cellStyle);
                                cell12.setCellValue(detaillist.get(i).getRepairGrade());
                                cell12.setCellStyle(cellStyle);
                                cell13.setCellValue(detaillist.get(i).getInsuranceCompanyName());
                                cell13.setCellStyle(cellStyle);
                                cell14.setCellValue(detaillist.get(i).getVehicleReciveTime());
                                cell14.setCellStyle(cellStyle);
                                cell15.setCellValue(detaillist.get(i).getPartName());
                                cell15.setCellStyle(cellStyle);
                                cell16.setCellValue(detaillist.get(i).getPartTotalMoney());
                                cell16.setCellStyle(cellStyle);
                                cell17.setCellValue(detaillist.get(i).getRepairName());
                                cell17.setCellStyle(cellStyle);
                                cell18.setCellValue(detaillist.get(i).getRepairTotalMoney());
                                cell18.setCellStyle(cellStyle);
                                cell19.setCellValue(detaillist.get(i).getTotalMoney());
                                cell19.setCellStyle(cellStyle);
                                cell20.setCellValue(detaillist.get(i).getTaskNo());
                                cell20.setCellStyle(cellStyle);
                                cell21.setCellValue(ComUtil.getRenttypeStr(detaillist.get(i).getRenttype()));
                                cell21.setCellStyle(cellStyle);
                            }
                        }
                        nextRowIndex += detaillist.size();
                    }
                }
            }

        }

        StringBuilder fileUrl = new StringBuilder();
        fileUrl.append(apolloPropertyUtils.getString("imagePath") + "mtc_file_excel" + Contants.LEFT_DIAGONAL);
        fileUrl.append(ComUtil.getSystemDate(ComUtil.DATE_TYPE3) + "." + Contants.XLSX);

        ByteArrayOutputStream outTemp = new ByteArrayOutputStream();
        ByteArrayInputStream inputStream = null;

        try {
            workbook.write(outTemp);
            inputStream = new ByteArrayInputStream(outTemp.toByteArray());

            ossUtil.uploadFile(inputStream, fileUrl.toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {

                workbook.close();
                outTemp.flush();
                outTemp.close();

                if (inputStream != null) {
                    inputStream.close();
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        Workbook newWorkbook = null;

        try {
            newWorkbook = getWorkbook(apolloPropertyUtils.getString("imageHttpPath") + fileUrl.toString());
        } catch (Exception e3) {
            e3.printStackTrace();
        } finally {
            // 删除临时文件
            ossUtil.delete(fileUrl.toString());
        }

        // String exportDirector =
        // apolloPropertyUtils.getString(Contants.EXPROT_DIRECTORY);
        SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateStr = dateformat.format(System.currentTimeMillis());
        String execelName = Contants.FIRSTPAGE_INFO_FUNCTION_NAME + dateStr + Contants.XLSX;
        // File file = new File("d:\\" + execelName);
        response.setHeader("Content-Type", "application/vnd.ms-excel");
        response.setHeader("content-disposition", "attachment;filename=" + execelName);
        OutputStream out = null;
        try {
            out = response.getOutputStream();
        } catch (IOException e2) {
            e2.printStackTrace();
        }
        // OutputStream out = null;
        try {
            // out = new FileOutputStream(file);
            newWorkbook.write(out);
            out.flush();// 缓存清空输出
        } catch (FileNotFoundException e1) {
            log.error(ComUtil.getExceptionMsg(e1));
        } catch (IOException e) {
            log.error(ComUtil.getExceptionMsg(e));
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }
                workbook = null;
            }
            if (newWorkbook != null) {
                try {
                    newWorkbook.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }
                newWorkbook = null;
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }
                out = null;
            }

        }

        return vo;

    }

    // POI单元格垂直居中，相同内容的单元格合并
    public Workbook getWorkbook(String filePath) {

        InputStream tempIs = null;
        XSSFWorkbook workbook = null;

        try {
            URL url = new URL(filePath);
            URLConnection conn = url.openConnection();
            conn.setConnectTimeout(3000);
            conn.setReadTimeout(3 * 60 * 1000);
            tempIs = conn.getInputStream();

            workbook = new XSSFWorkbook(tempIs);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {

            try {

                if (tempIs != null) {
                    tempIs.close();
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER); // 居中
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 垂直
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);// 左边框
        cellStyle.setBorderTop(CellStyle.BORDER_THIN);// 上边框
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);// 右边框
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            int totalRow = sheet.getPhysicalNumberOfRows();
            log.debug("总行数：" + totalRow + StringUtils.EMPTY);

            if (totalRow == 2) {
                continue;
            }
            for (int cellIndex = 7; cellIndex >= 0; cellIndex--) {
                int currnetRow = 1; // 开始查找的行
                for (int p = 1; p < totalRow; p++) {// totalRow 总行数
                    Cell currentCell = sheet.getRow(p).getCell(cellIndex);
                    String current = getStringCellValue(currentCell);
                    Cell nextCell = null;
                    String next = "";
                    if (p < totalRow + 1) {
                        Row nowRow = sheet.getRow(p + 1);
                        if (nowRow != null) {
                            nextCell = nowRow.getCell(cellIndex);
                            next = getStringCellValue(nextCell);
                        } else {
                            next = "";
                        }
                    } else {
                        next = "";
                    }
                    if (cellIndex <= 1) {
                        if (current.equals(next)) {// 对比是否相同
                            currentCell.setCellValue("");
                            continue;
                        } else {

                            if (currnetRow == p) {
                                Cell nowCell = sheet.getRow(currnetRow).getCell(cellIndex);
                                nowCell.setCellValue(current);
                                nowCell.setCellStyle(cellStyle);
                                currnetRow = p + 1;
                            } else {
                                sheet.addMergedRegion(new CellRangeAddress(currnetRow, p, cellIndex, cellIndex));// 合并单元格
                                Cell nowCell = sheet.getRow(currnetRow).getCell(cellIndex);
                                nowCell.setCellValue(current);
                                nowCell.setCellStyle(cellStyle);
                                currnetRow = p + 1;
                            }

                        }
                    } else {
                        // 第一列的值
                        Cell firstCell = sheet.getRow(p).getCell(1);
                        String firstCellValue = getStringCellValue(firstCell);
                        Cell secondCell = null;
                        String secondCellValue = "";
                        if (p < totalRow + 1) {
                            Row nowRow = sheet.getRow(p + 1);
                            if (nowRow != null) {
                                secondCell = nowRow.getCell(1);
                                secondCellValue = getStringCellValue(secondCell);
                            } else {
                                secondCellValue = "";
                            }
                        } else {
                            secondCellValue = "";
                        }
                        if (current.equals(next) && firstCellValue.equals(secondCellValue)) {// 对比是否相同
                            currentCell.setCellValue("");
                            continue;
                        } else {

                            if (currnetRow == p) {
                                Cell nowCell = sheet.getRow(currnetRow).getCell(cellIndex);
                                nowCell.setCellValue(current);
                                nowCell.setCellStyle(cellStyle);
                                currnetRow = p + 1;
                            } else {
                                sheet.addMergedRegion(new CellRangeAddress(currnetRow, p, cellIndex, cellIndex));// 合并单元格
                                Cell nowCell = sheet.getRow(currnetRow).getCell(cellIndex);
                                nowCell.setCellValue(current);
                                nowCell.setCellStyle(cellStyle);
                                currnetRow = p + 1;
                            }

                        }
                    }
                }

            }
        }

        return workbook;
    }

    private String getStringCellValue(Cell cell) {
        String strCell = "";
        if (cell != null) {
            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    strCell = cell.getStringCellValue();
                    break;
                case Cell.CELL_TYPE_NUMERIC:
                    strCell = String.valueOf(cell.getNumericCellValue());
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    strCell = String.valueOf(cell.getBooleanCellValue());
                    break;
                case Cell.CELL_TYPE_BLANK:
                    strCell = "";
                    break;
                default:
                    strCell = "";
                    break;
            }
            if (strCell.equals("") || strCell == null) {
                return "";
            }
        }
        return strCell;
    }

    @Override
    public ReportFirst reportFirstAllPageInfo(String orgId, HttpServletRequest request, HttpServletResponse response) {
        ReportFirst reportFirst = new ReportFirst();
        List<ReportByWeek> list = repairTaskMapper.queryReportFirst(orgId);
        Integer countA = repairTaskMapper.queryCountA(orgId);
        Integer countB = repairTaskMapper.queryCountB(orgId);
        Integer countC = repairTaskMapper.queryCountC(orgId);
        Integer countD = repairTaskMapper.queryCountD(orgId);
        reportFirst = repairTaskMapper.queryGradeMaintenance(orgId);
        countB = countB + countC;
        reportFirst.setList(list);
        reportFirst.setCountA(String.valueOf(countA));
        reportFirst.setCountB(String.valueOf(countB));
        reportFirst.setCountD(String.valueOf(countD));
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 设置日期格式
        String nowTime = df.format(new Date());// new Date()为获取当前系统时间
        reportFirst.setNowTime(nowTime);
        return reportFirst;
    }

    @Override
    public DefaultServiceRespDTO updateSapCode(long id) {
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(id);
        if (repairTaskNew != null) {
            // 查询修理厂编号
            String repairDepotSapCode = repairDepotInfoMapper.queryDepotSapCode(repairTaskNew.getRepairDepotId());
            if (!repairTaskNew.getRepairDepotSapCode().equals(repairDepotSapCode)) {
                if (repairTaskMapper.updateRepairDepotSapCode(id, repairDepotSapCode) > 0) {
                    return new DefaultServiceRespDTO(0, "校准成功");
                }
            } else {
                return new DefaultServiceRespDTO(-1, "供应商编码无变化");
            }
        }
        return new DefaultServiceRespDTO(-1, "校准供应商编码失败");
    }

    @Override
    public DefaultServiceRespDTO updateReSendSap(long id, int sapSendStatus) {
        try {
            repairTaskMapper.updateReSendSap(id, sapSendStatus, new Date());
            return new DefaultServiceRespDTO(0, "已重新发送");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return new DefaultServiceRespDTO(-1, "发送失败");
    }

    @Override
    public DefaultServiceRespDTO vehicleRepairBillFinishModify(String taskNo) {
        try {
            MtcManualTaskDTO mtcManualTaskDTO = manualUtils.getMtcManualTaskByTaskNo(taskNo);
            if (null != mtcManualTaskDTO) {
                HttpRequest.sendGet(apolloPropertyUtils.getString("bfsVehicleRepairBillFinishModifyUrl") + mtcManualTaskDTO.getManualNo(), null);
            } else {
                return new DefaultServiceRespDTO(-1, "未找到维计提，不可发送！");
            }
        } catch (Exception e) {
            log.error("通知业财系统重新发送sap异常", e);
        }
        return DefaultServiceRespDTO.SUCCESS;
    }

    @Override
    public DefaultServiceRespDTO accidentDamageChangePush(AccidentDamageDTO accidentDamageDTO, ComModel comModel) {

        /*try {
            MtcManualTaskDTO mtcManualTaskDTO = mtcManualTaskMapper.selectByManualNo(accidentDamageDTO.getAccidentNo());
            if (null != mtcManualTaskDTO) {
                manualUtils.saveManualLog(mtcManualTaskDTO.getId(), StringUtils.EMPTY, accidentDamageDTO.toSting(), comModel);
                manualUtils.doManualTaskChangeAmount(null, accidentDamageDTO.getAccidentNo(), accidentDamageDTO, comModel);
                manualUtils.doUpdateRepairTaskAmount(accidentDamageDTO);
            }
        } catch (Exception e) {
            log.error("事故理赔金额变更推送异常");
            e.printStackTrace();
            return new DefaultServiceRespDTO(-1, "事故理赔金额变更推送异常");
        }*/

        // 修改事故任务下所有维修任务 自费金额根据是否本车全损处置 变化
        List<RepairTask> repairTaskList = repairTaskMapper.queryRepairTaskListByAccidentNo(accidentDamageDTO.getAccidentNo());
        if (CollectionUtils.isNotEmpty(repairTaskList)) {
            for (RepairTask repairTask : repairTaskList) {
                if (repairTask.getCurrentTache() < 30L || repairTask.getCurrentTache() == 110L) {
                    continue;
                }
                if (CurrentTacheStatusEnum.CLOSE_CURRENT_STATUS.contains(repairTask.getCurrentStatus())) {
                    continue;
                }
                // 1. 更新自费金额
                repairTaskService.updateSelfFundedAmount(repairTask.getTaskNo(), accidentDamageDTO);
                // 2. 业财对接-理赔金额账单
                bfcCostService.syncSelfFundedAmountBill(repairTask.getTaskNo());
            }
        }

        return DefaultServiceRespDTO.SUCCESS;
    }

    @Override
    public RepairTaskToBfcBO queryRepairTaskListByAccidentNo(String accidentNo) {
        RepairTaskToBfcBO result = new RepairTaskToBfcBO();
        if (StringUtils.isBlank(accidentNo)) {
            return result;
        }

        List<RepairTaskToBfcListBO> repairTaskToBfcListBOList = new LinkedList<>();
        // 根据事故编号查询维修任务列表
        List<RepairTask> repairTaskList = repairTaskMapper.queryRepairTaskListByAccidentNo(accidentNo);
        if (CollectionUtils.isNotEmpty(repairTaskList)) {
            for (RepairTask repairTask : repairTaskList) {
                RepairTaskToBfcListBO bo = new RepairTaskToBfcListBO();
                BeanCopyUtils.copyProperties(repairTask, bo);
                bo.setRepairTypeId(repairTask.getRepairTypeId().intValue());
                bo.setRepairTypeName(RepairTypeEnum.getEnumDesc(bo.getRepairTypeId()));
                Long currentTacheStatus = null;
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_VEHICLE_JOIN)) {
                    currentTacheStatus = repairTask.getVehicleTransferTaskSchedule();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_INSURANCE_QUOTE)) {
                    currentTacheStatus = repairTask.getInsuranceQuoteTaskSchedule();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS)) {
                    currentTacheStatus = repairTask.getVerificationLossTaskSchedule();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_REASSIGNMENT_APPLY)) {
                    currentTacheStatus = repairTask.getReassignmentTaskSchedule();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_VEHICLE_REPAIR)) {
                    currentTacheStatus = repairTask.getVehicleRepairTaskSchedule();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_VEHICLE_CHECK)) {
                    currentTacheStatus = repairTask.getVehicleCheckTaskSchedule();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_MATERIAL_COLLECTION)) {
                    currentTacheStatus = repairTask.getMaterialCollectionTaskSchedule().longValue();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_LOSS_REGISTRATION)) {
                    currentTacheStatus = repairTask.getLossRegistrationTaskSchedule().longValue();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_SETTLEMENT_MANAGEMENT)) {
                    currentTacheStatus = repairTask.getSettlementTaskSchedule().longValue();
                }
                if (bo.getCurrentTache().equals((long) Contants.CURRENT_TACHE_INSURANCE_PRE_REVIEW)) {
                    currentTacheStatus = repairTask.getInsurancePreReviewTaskSchedule();
                }

                bo.setIsClosed(CurrentTacheStatusEnum.CLOSE_CURRENT_STATUS.contains(repairTask.getCurrentStatus()));
                bo.setSelfFundedAmount(repairTask.getSelfFundedAmount());

                bo.setCurrentTacheStatus(CurrentTacheStatusEnum.getEnumDesc(currentTacheStatus));
                // 计算 修理厂报价：其他合计
                if (null != bo.getRepairInsuranceTotalAmount()
                        && null != bo.getRepairReplaceTotalAmount()
                        && null != bo.getRepairRepairTotalAmount()) {
                    BigDecimal repairOtherTotalAmount = bo.getRepairInsuranceTotalAmount()
                            .subtract(bo.getRepairReplaceTotalAmount())
                            .subtract(bo.getRepairRepairTotalAmount());
                    bo.setRepairOtherTotalAmount(repairOtherTotalAmount);
                }
                // 计算 车管核价：其他合计
                if (null != bo.getVehicleInsuranceTotalAmount()
                        && null != bo.getVehicleReplaceTotalAmount()
                        && null != bo.getVehicleRepairTotalAmount()) {
                    BigDecimal vehicleOtherTotalAmount = bo.getVehicleInsuranceTotalAmount()
                            .subtract(bo.getVehicleReplaceTotalAmount())
                            .subtract(bo.getVehicleRepairTotalAmount());
                    bo.setVehicleOtherTotalAmount(vehicleOtherTotalAmount);
                }

                // 查询任务关联自有配件库
                bo.setRepairItemCheckInfoList(mtcRepairItemCheckInfoService.queryCheckListByTaskNo(repairTask.getTaskNo()));
                // 换件项目明细
                bo.setLossFitInfoList(mtcLossFitInfoMapper.queryMtcLossFitItemList(repairTask.getTaskNo()));
                // 工时项目明细
                bo.setLossRepairInfoList(mtcLossRepairInfoMapper.queryMtcLossRepairItemList(repairTask.getTaskNo()));

                if (repairTask.getTaskInflowTime() != null) {
                    bo.setTaskInflowTime(DateUtil.getFormatDate(repairTask.getTaskInflowTime(), "yyyy-MM-dd HH:mm:ss"));
                }
                if (repairTask.getSendRepairTime() != null) {
                    bo.setSendRepairTime(DateUtil.getFormatDate(repairTask.getSendRepairTime(), "yyyy-MM-dd HH:mm:ss"));
                }
                if (repairTask.getVehicleCheckTime() != null) {
                    bo.setVehicleCheckTime(DateUtil.getFormatDate(repairTask.getVehicleCheckTime(), "yyyy-MM-dd HH:mm:ss"));
                }

                repairTaskToBfcListBOList.add(bo);
            }
        }
        result.setRepairTaskList(repairTaskToBfcListBOList);

        try {
            // 核价总金额
            BigDecimal allVehicleInsuranceTotalAmount = manualUtils.getAllVehicleInsuranceTotalAmountByManualNo(accidentNo);
            result.setAllVehicleInsuranceTotalAmount(allVehicleInsuranceTotalAmount);
        } catch (Exception e) {
            log.error("核价总金额计算异常！");
            e.printStackTrace();
        }

        return result;
    }

    @Override
    public RepairTaskNoToBfcDTO ifExistRepairTask(String accidentNo, Integer reviewToSelFeeFlag) {
        RepairTaskNoToBfcDTO result = new RepairTaskNoToBfcDTO();
        // 根据事故编号查询维修任务列表
        if (StringUtils.isNotBlank(accidentNo)) {
            List<RepairTask> repairTaskList = repairTaskMapper.queryRepairTaskListByAccidentNo(accidentNo);
            List<String> taskNoList = repairTaskList.stream().filter(repairTask -> repairTask.getReviewToSelFeeFlag().equals(reviewToSelFeeFlag)).map(RepairTask::getTaskNo).collect(Collectors.toList());
            result.setTaskNoList(taskNoList);
            result.setIfExist(taskNoList.size() > 0);
        }
        return result;
    }

    public BaseResultBean sendPost(String url, String parameter) {
        String compareDatetime = "2022-04-01 00:00:00";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime compareLocalDateTime = LocalDateTime.parse(compareDatetime, dateTimeFormatter);
        LocalDateTime localDateTime = LocalDateTime.now();
        if (localDateTime.isAfter(compareLocalDateTime)) {
            return new BaseResultBean(-1, "2022-04-01 00:00:00以后不通知长租系统");
        }
        BaseResultBean resultBean = new BaseResultBean(-1, "通知长租系统失败");
        String xiangDaoNotifyUrl = apolloPropertyUtils.getString("xiangdaonotifyUrl");
        String notifyUrl = xiangDaoNotifyUrl + url;
        InputStream is = null;
        OutputStream dos = null;
        HttpURLConnection http_conn = null;
        try {
            log.error("调用地址===========》{}", notifyUrl);
            log.error("传入参数===========》{}", parameter);
            URL urlObj = new URL(notifyUrl);
            http_conn = (HttpURLConnection) urlObj.openConnection();
            http_conn.setDoOutput(true);
            http_conn.setDoInput(true);
            http_conn.setUseCaches(false);
            http_conn.setRequestProperty("Content-Type", "application/json");
            http_conn.setConnectTimeout(3000);
            http_conn.setReadTimeout(6000);

            dos = http_conn.getOutputStream();
            IOUtils.write(parameter.getBytes("UTF-8"), dos);
            dos.flush();

            String returnData = StringUtils.EMPTY;
            int responseCode = http_conn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode
                    || HttpURLConnection.HTTP_CREATED == responseCode) {
                is = http_conn.getInputStream();
            } else if (HttpURLConnection.HTTP_BAD_REQUEST == responseCode) {
                is = http_conn.getErrorStream();
            }
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) != -1) {
                returnData += new String(buffer, 0, len, "UTF-8");
            }
            JSONObject response = null;
            if (StringUtils.isNoneBlank(returnData)) {
                response = JSONObject.parseObject(returnData);
            }
            log.error("长租通知返回结果：" + response);
            if (response != null) {
                if ("0".equals(response.getString("retCode"))) {
                    resultBean.setRetCode(0);
                } else {
                    resultBean.setRetCode(-1);
                    resultBean.setRetMsg("【长租系统】 " + response.getString("retMsg"));
                }
            }
        } catch (Exception e) {
            log.error("长租通知异常 :exception===>" + e);
        } finally {
            IOUtils.closeQuietly(dos);
            IOUtils.closeQuietly(is);
            if (http_conn != null) {
                http_conn.disconnect();
            }
        }
        return resultBean;
    }


    /**
     * 替代车任务逻辑处理
     *
     * @param taskNo
     * @param type   1-需要发送短信
     */
    private void doReplaceVehicleTask(String taskNo, Integer type) {
        // 判断当前维修任务是否有关联的替代车任务
        DefaultWebRespVO defaultWebRespVO = rentReplaceVehicleService.selectReplaceTaskMtcsMtcRelationByMtcNo(taskNo);
        if (defaultWebRespVO != null && "0".equals(defaultWebRespVO.getCode())) {
            List<RentMtcTaskRelationDTO> taskRelationDTOS = (List<RentMtcTaskRelationDTO>) defaultWebRespVO.getData();
            if (taskRelationDTOS != null && taskRelationDTOS.size() != 0) {
                // 查询该维修任务关联的替代车任务是否完成
                String replaceVehicleTaskNo = taskRelationDTOS.get(0).getReplaceVehicleTaskNo();
                IrtRentReplaceVehicleTaskBo taskBoReturn = null;
                try {
                    taskBoReturn = iIrtRentReplaceVehicleTaskService.queryByTaskNo(replaceVehicleTaskNo);
                } catch (BusinessException e) {
                    e.printStackTrace();
                }
                // 如果替代车任务完成 查询所关联的维修任务是否都完成
                if (taskBoReturn != null) {
                    List<String> mtcTaskNos = taskRelationDTOS.stream()
                            .map(RentMtcTaskRelationDTO::getMtcTaskNo)
                            .collect(Collectors.toList());
                    // 未完成标志
                    int endTask = 0;
                    int doTask = 0;
                    int cancelTask = 0;
                    // 任务状态(任务状态(0:已送修、1:维修中、2:待改派、3:待提车、4:已完成、5:已取消))
                    List<String> endTaskList = new ArrayList<>();
                    for (String mtcTaskNo : mtcTaskNos) {
                        //BdpMtcTaskInfoDTO bdpMtcTaskInfoDTO = bdpMtcTaskInfoService.selectTaskInfoByTaskNo(mtcTaskNo);

                        RepairData repairData = queryRepairInfoByMtcTaskNo(mtcTaskNo);
                        if (repairData != null) {
                            if (repairData.getTaskStatus() == 4 || repairData.getTaskStatus() == 3) {
                                endTask++;
                                endTaskList.add(mtcTaskNo);
                            } else if (repairData.getTaskStatus() == 5) {
                                cancelTask++;
                            }
                        }
                        /*if (bdpMtcTaskInfoDTO != null){
                            if (bdpMtcTaskInfoDTO.getTaskStatus() == 3){
                                doTask++;
                                endTaskList.add(mtcTaskNo);
                            }else if (bdpMtcTaskInfoDTO.getTaskStatus() == 4){
                                endTask ++;
                                endTaskList.add(mtcTaskNo);
                            }else if (bdpMtcTaskInfoDTO.getTaskStatus() == 5){
                                cancelTask ++;
                            }
                        }*/
                    }
                    // 如果都完成或取消
                    if ((doTask + endTask + cancelTask) == mtcTaskNos.size() && cancelTask != mtcTaskNos.size()) {
                        // 设置约定取车时间
                        List<RepairTaskViewDTO> repairTaskViewBOS = new ArrayList<>();
                        for (String mtcTaskNo : endTaskList) {
                            RepairTaskViewDTO repairTaskViewDTO = mtcRepairTaskService.selectMtcDetailTaskInfo(mtcTaskNo);
                            repairTaskViewBOS.add(repairTaskViewDTO);
                        }
                        log.info("维修任务：{}", JSON.toJSONString(repairTaskViewBOS));
                        // 最大维修任务完成时间
                        String vehicleCheckTime = repairTaskViewBOS.stream()
                                .filter(taskView -> taskView.getVehicleCheckTime() != null)
                                .max(Comparator.comparing(RepairTaskViewDTO::getVehicleCheckTime))
                                .get().getVehicleCheckTime();


                        // 获取运营公司ID
                        String vin = taskBoReturn.getVin();
                        VehicleOperateDTO vehicleOperate = rentReplaceVehicleService.getVehicleOperateDTOByVin(vin);
                        String orgId = taskBoReturn.getOrgId();
                        if (vehicleOperate != null) {
                            orgId = vehicleOperate.getOperationOrgId();
                        }
                        // 获取缓冲时间
                        BdpManageWorkDTO subBufferByOrgId = rentReplaceVehicleService.getSubBufferByOrgId(orgId);
                        String subBufferTime = "0";
                        if (subBufferByOrgId == null) {
                            subBufferTime = "4";
                        } else {
                            subBufferTime = subBufferByOrgId.getBufferTime();
                        }
                        IrtRentReplaceVehicleTask replaceVehicleTask = new IrtRentReplaceVehicleTask();
                        replaceVehicleTask.setTaskNo(replaceVehicleTaskNo);
                        replaceVehicleTask.setExpectTakeTime(null);
                        Date expectTime = null;
                        if (vehicleCheckTime != null) {
                            //计算缓冲时间跳过非工作时间/节假日
                            expectTime = this.skipHolidayAndWeekend(subBufferByOrgId, vehicleCheckTime);
                            if (expectTime == null) {
                                expectTime = DateUtil.AddHours(DateUtil.getDateFromStr(vehicleCheckTime, DateUtil.DATE_TYPE6), Integer.valueOf(subBufferTime));
                            }
                            replaceVehicleTask.setExpectTakeTime(expectTime);
                        }
                        iIrtRentReplaceVehicleTaskService.updateExpectTimeByTaskNo(replaceVehicleTask);

                        // 验收成功通知发送短信
                        // 验收通过发送短信
                        if (type == 1) {
                            this.sendMsg(taskBoReturn, DateUtil.getFormatDate(expectTime, DateUtil.DATE_TYPE6), subBufferByOrgId);
                        }
                    }
                }
            }
        } else {
            log.error("判断当前维修任务是否有关联的替代车任务失败--》{}", taskNo);
        }
    }

    private RepairData queryRepairInfoByMtcTaskNo(String mtcTaskNo) {
        RepairData repairData = new RepairData();
        // 梧桐创建的维修任务
        if (mtcTaskNo.startsWith("SG") || mtcTaskNo.startsWith("ZF") || mtcTaskNo.startsWith("BY") || mtcTaskNo.startsWith("ZD")) {
            JSONObject response = replaceVehicleUtils.queryWtRepairInfo(mtcTaskNo);
            JSONObject jsonObject = (JSONObject) response.get("data");
            repairData = JSONObject.toJavaObject(jsonObject, RepairData.class);
            if (repairData == null) {
                return repairData;
            }
            repairData.setTaskStatus(1);
            // 如果维修任务已关闭
            if (repairData.getRepairTaskStatus().equals(11)) {
                repairData.setTaskStatus(5);
                return repairData;
            }
            // 判断维修任务是否验收通过
            RepairScheduleDTO repairScheduleDTO = mtcRepairTaskService.queryRepairSchedule(mtcTaskNo);
            if (repairScheduleDTO.getVehicleCheckTaskSchedule() == 610) {
                repairData.setTaskStatus(4);
                return repairData;
            }
        } else {
            BdpMtcTaskInfoDTO bdpMtcTaskInfoDTO = bdpMtcTaskInfoService.selectTaskInfoByTaskNo(mtcTaskNo);
            repairData.setTaskStatus(bdpMtcTaskInfoDTO.getTaskStatus());
            repairData.setTaskNo(mtcTaskNo);
        }
        return repairData;
    }

    /**
     * 验收通过发送短信
     *
     * @param taskBoReturn
     * @param expectTime
     */
    private void sendMsg(IrtRentReplaceVehicleTaskBo taskBoReturn, String expectTime, BdpManageWorkDTO subBuffer) {

        // 验收成功通知发送短信
        String rentUserPhone = taskBoReturn.getRentUserPhone();
        String rentUserName = taskBoReturn.getRentUserName();
        String salePhone = taskBoReturn.getSalePhone();
        String saleName = taskBoReturn.getSaleName();

   /*     StringBuilder rentUserSb = new StringBuilder();
        rentUserSb.append("您的长租车辆 车牌：")
                .append(taskBoReturn.getVehicleNo())
                .append("，已维修完毕。请于")
                .append(expectTime)
                .append("前取车，如有问题请联系工作人员：")
                .append(subBuffer.getContactPhone());
        BaseResponse rentUserResponse = iMessagepushServ.syncSendvoiceVerify(rentUserPhone, rentUserSb.toString(), "evcard-mtc");
        if (rentUserResponse != null && "0".equals(rentUserResponse.getCode())) {
            log.info("租客：{} 短信发送成功 手机号码为:{} 短信内容:{}", rentUserName, rentUserPhone, rentUserSb.toString());
        }*/
        StringBuilder manageSb = new StringBuilder();
        manageSb.append("长租客户：")
                .append(rentUserName)
                .append(" 手机号：")
                .append(rentUserPhone)
                .append(" 车牌：")
                .append(taskBoReturn.getVehicleNo())
                .append("，已维修完毕。请联系用户于")
                .append(expectTime)
                .append("前取车");
        BaseResponse manageRes = iMessagepushServ.syncSendvoiceVerify(subBuffer.getContactPhone(), manageSb.toString(), "evcard-mtc");
        if (manageRes != null && "0".equals(manageRes.getCode())) {
            log.info("车管：{} 短信发送成功 手机号码为:{} 短信内容:{}", subBuffer.getContactName(), subBuffer.getContactPhone(), manageSb.toString());
        }
       /* BaseResponse sellRes = iMessagepushServ.syncSendvoiceVerify(taskBoReturn.getSalePhone(), manageSb.toString(), "evcard-mtc");
        if (sellRes != null && "0".equals(sellRes.getCode())) {
            log.info("销售：{} 短信发送成功 手机号码为:{} 短信内容:{}", saleName, salePhone, manageSb.toString());
        }*/

    }

    /**
     * 计算缓冲时间跳过非工作时间/节假日
     *
     * @param subBufferInfo
     * @param vehicleCheckTime
     * @return
     */
    private Date skipHolidayAndWeekend(BdpManageWorkDTO subBufferInfo, String vehicleCheckTime) {

        String bufferTime = subBufferInfo.getBufferTime();
        String workTime = subBufferInfo.getWorkTime();
        if (workTime == null) {
            workTime = "09:00-17:00";
        }
        String[] split = workTime.split("-");
        String startTime = split[0];
        String endTime = split[1];
        //Date date = new Date();
        Date date = DateUtil.getDateFromStr(vehicleCheckTime, DateUtil.DATE_TYPE5);
        // 今天上班时间
        String todayWorkTime = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE5) + " " + startTime + ":00";
        String tomorrowWorkTime = DateUtil.getFormatDate(DateUtil.AddDay(date, 1), DateUtil.DATE_TYPE5) + " " + startTime + ":00";
        String todayUpTime = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE5) + " " + endTime + ":00";
        log.info("公司信息:{}", JSON.toJSONString(subBufferInfo));
        log.info("今天上班时间:{}-->今天下班时间:{}-->明天上班时间:{}", todayWorkTime, todayUpTime, tomorrowWorkTime);
        // 验收时间+缓冲时间
        Date checkTime = DateUtil.AddHours(DateUtil.getDateFromStr(vehicleCheckTime, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime));
        String expectTime = DateUtil.getFormatDate(checkTime, DateUtil.DATE_TYPE2);
        log.info("验收时间:{}", vehicleCheckTime);
        log.info("验收时间+缓冲时间:{}", expectTime);
        log.info("约定取车时间为：{}", expectTime);
        Date todayWorkDateTime = DateUtil.getDateFromStr(todayWorkTime, DateUtil.DATE_TYPE2);
        Date todayUpDateTime = DateUtil.getDateFromStr(todayUpTime, DateUtil.DATE_TYPE2);
        Date tomorrowWorkDateTime = DateUtil.getDateFromStr(tomorrowWorkTime, DateUtil.DATE_TYPE2);
        // 如果验收当天是周末或者节假日
        //getWorkDay(date)
//        String formatDate = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE2);
//        if (isHoliday(formatDate) || isWeekEnd(formatDate)){
//            String workDay = DateUtil.getFormatDate(getWorkDay(date), DateUtil.DATE_TYPE5) + " " + startTime + ":00";
//            expectTime = DateUtil.getFormatDate(DateUtil.AddHours(DateUtil.getDateFromStr(workDay, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime)), DateUtil.DATE_TYPE2);
//        }else if (vehicleCheckTime.compareTo(todayWorkTime) < 0){
//            // 验收时间早于上班时间 --》约定时间== 今天上班时间+缓冲时间
//            expectTime = DateUtil.getFormatDate(DateUtil.AddHours(DateUtil.getDateFromStr(todayWorkTime, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime)), DateUtil.DATE_TYPE2);
//        }else if (vehicleCheckTime.compareTo(todayUpTime) > 0){
//            // 验收时间晚于下班时间 --》约定时间== 明天上班时间+缓冲时间
//            expectTime = DateUtil.getFormatDate(DateUtil.AddHours(DateUtil.getDateFromStr(tomorrowWorkTime, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime)), DateUtil.DATE_TYPE2);
//        }else if (expectTime.compareTo(todayUpTime) > 0){
//            // 验收时间大于今天下班时间
//            //int differHour = DateUtil.getDifferHour(todayUpTime, expectTime);
//            int differMin = DateUtil.getDiffMin(todayUpTime, expectTime);
//            expectTime = DateUtil.getFormatDate(DateUtil.AddMin(DateUtil.getDateFromStr(tomorrowWorkTime, DateUtil.DATE_TYPE2), differMin), DateUtil.DATE_TYPE2);
//        }else if (expectTime.compareTo(todayUpTime) < 0){
//            // 验收时间小于今天下班时
//        }

        if (checkTime.compareTo(todayWorkDateTime) < 0) {
            // 验收时间早于上班时间 --》约定时间== 今天上班时间+缓冲时间
            checkTime = DateUtil.AddHours(todayWorkDateTime, Integer.valueOf(bufferTime));
        } else if (checkTime.compareTo(todayUpDateTime) > 0) {
            // 验收时间晚于下班时间 --》 计算时间差
            int minute = DateUtil.minuteBetween(todayUpDateTime, checkTime);
            if (minute < Integer.valueOf(bufferTime) * 60) {
                checkTime = DateUtil.AddMin(tomorrowWorkDateTime, minute);
            } else {
                checkTime = DateUtil.AddHours(tomorrowWorkDateTime, Integer.valueOf(bufferTime));
            }
        }

//        if (isHoliday(expectTime) || isWeekEnd(expectTime)){
//            Date workDay = getWorkDay(DateUtil.getDateFromStr(expectTime, DateUtil.DATE_TYPE2));
//            expectTime = DateUtil.getFormatDate(workDay, DateUtil.DATE_TYPE2);
//        }
        log.info("实际返回取车时间为：{}", DateUtil.getFormatDate(checkTime, DateUtil.DATE_TYPE2));
        return checkTime;

    }


    /**
     * 获取工作日时间
     *
     * @param date
     * @return
     */
    public Date getWorkDay(Date date) {

        for (int i = 0; i < 10; i++) {
            String formatDate = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE7);
            String formatDate1 = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE2);
            boolean holiday = this.isHoliday(formatDate);
            boolean weekEnd = this.isWeekEnd(formatDate1);
            if (holiday || weekEnd) {
                date = DateUtil.AddDay(date, 1);
            } else {
                break;
            }
        }
        return date;
    }

    /**
     * 是否是周末
     *
     * @param date
     * @return
     */
    private boolean isWeekEnd(String date) {
        boolean isWeekend = DateUtilsHmy.isWeeks(DateUtilsHmy.str2Date(date, DateUtilsHmy.yyyyMMddFormat));
        return isWeekend;
    }

    private boolean isHoliday(String date) {

        long l = rentReplaceVehicleService.queryHolidayCount(date);
        if (l > 0) {
            return true;
        }
        return false;
    }

    private void doManualLogContent(RepairTask repairTaskRet, CheckAcceptancePassBO checkAcceptancePassBO, StringBuilder content) {
        if (repairTaskRet.getClaimsFlag().equals(1) && checkAcceptancePassBO.getClaimsFlag().equals(1)) {
            if (repairTaskRet.getEstimatedClaimAmount().compareTo(checkAcceptancePassBO.getEstimatedClaimAmount()) == 0) {
                content.append(" 预估理赔金额：").append(repairTaskRet.getEstimatedClaimAmount()).append(" 元");
            } else {
                content.append(" 预估理赔金额由：").append(repairTaskRet.getEstimatedClaimAmount())
                        .append(" 元 改为：")
                        .append(checkAcceptancePassBO.getEstimatedClaimAmount()).append(" 元");
            }
        }
    }

    /**
     * 判断是否有计提任务
     *
     * @param taskNo
     * @return
     */
    public boolean isExitManualTask(String taskNo) {
        MtcManualTaskDTO mtcManualTaskDTO = mtcManualTaskMapper.selectByManualNo(taskNo);
        if (mtcManualTaskDTO != null) {
            Date date = DateUtil.getDateFromStr("2022-09-05", DateUtil.DATE_TYPE5);
            Date verificationLossCheckTime = DateUtil.getDateFromStr(mtcManualTaskDTO.getVerificationLossCheckTime(), DateUtil.DATE_TYPE2);
            if (null != date && null != verificationLossCheckTime) {
                return date.before(verificationLossCheckTime);
            }
        }
        return false;
    }

    @Override
    public void genReplaceVehicleExpectTakeTime(List<String> mtcTaskNoList) {
        log.info("批量更新约定取车时间维修任务数量:{}", mtcTaskNoList.size());
        for (String taskNo : mtcTaskNoList) {
            this.doReplaceVehicleTask(taskNo, 2);
        }
    }

    @Override
    public DefaultServiceRespDTO updateRepairTotalMileage(String taskNo, BigDecimal totalMileage, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        RepairTask repairTask = repairTaskMapper.selectByTaskNoV(taskNo);
        if (null != repairTask && repairTask.getCurrentTache() != 30L) {
            DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务已不处于核损核价环节，无法修改！");
            return vo;
        }
        if (null != repairTask && !repairTask.getTotalMileage().equals(totalMileage)) {
            BdpVehicleLabelDTO bdpVehicleLabelDTO = bdpVehicleService.queryVehicleMileageInfoByVin(repairTask.getVin());
            if (null != bdpVehicleLabelDTO && bdpVehicleLabelDTO.getVehicleMileage() < totalMileage.doubleValue()) {
                // 更新维修任务总里程数
                repairTaskMapper.updateTotalMileageByTaskNo(taskNo, totalMileage);
                // 维修任务增加日志
                MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
                mtcOperatorLog.setTableName("mtc_repair_task");
                mtcOperatorLog.setRecoderId(repairTask.getId() + StringUtils.EMPTY);
                mtcOperatorLog.setOpeContent("修改总里程数：由" + repairTask.getTotalMileage().toString() + "改为" + totalMileage.toString());
                mtcOperatorLog.setRemark("核损核价");
                mtcOperatorLog.setCurrentTache(30L);
                mtcOperatorLog.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
                mtcOperatorLog.setCreateOperId(comModel.getCreateOperId());
                mtcOperatorLog.setCreateOperName(comModel.getCreateOperName());
                mtcOperatorLog.setUpdateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
                mtcOperatorLog.setUpdateOperId(comModel.getUpdateOperId());
                mtcOperatorLog.setUpdateOperName(comModel.getUpdateOperName());
                mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
                // 车管系统总里程数更新
                bdpVehicleService.updateVehicleMileage(repairTask.getVin(), totalMileage.intValue(), comModel.getUserName());
                return DefaultServiceRespDTO.SUCCESS;
            }
            DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修改后不可小于车辆在车管系统当前的总里程数！");
            return vo;
        } else {
            DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("维修任务不存在或总里程未变！");
            return vo;
        }
    }

    @Override
    public void updateRepairBudge(BfcRepairBudgeDTO bfcRepairBudgeDTO) {
        if (CollectionUtils.isNotEmpty(bfcRepairBudgeDTO.getTaskNoList())) {
            for (String taskNo : bfcRepairBudgeDTO.getTaskNoList()) {
                RepairTask updateRepairTask = new RepairTask();
                BeanCopyUtils.copyProperties(bfcRepairBudgeDTO, updateRepairTask);
                if (null != bfcRepairBudgeDTO.getSettlementStatus()) {
                    if (bfcRepairBudgeDTO.getSettlementStatus() == 5 || bfcRepairBudgeDTO.getSettlementStatus() == 6) {
                        // 清空结算单号及状态
                        updateRepairTask.setSettlementNo("");
                        updateRepairTask.setSettlementStatus(0);
                    }
                }
                updateRepairTask.setTaskNo(taskNo);
                repairTaskMapper.updateRepairTaskByTaskNo(updateRepairTask);
            }
        }
    }

    @Override
    public List<VehicleRepairPicDTO> getVehicleRepairPicList(String taskNo) {
        List<VehicleRepairPicDTO> result = new LinkedList<>();

        List<VehicleRepairPic> vehicleRepairPicList = vehicleRepairPicMapper.getAllPicListByTaskNo(taskNo);
        for (VehicleRepairPic vehicleRepairPic : vehicleRepairPicList) {
            VehicleRepairPicDTO dto = new VehicleRepairPicDTO();
            BeanCopyUtils.copyProperties(vehicleRepairPic, dto);
            dto.setPicType(vehicleRepairPic.getPicType().intValue());
            result.add(dto);
        }
        return result;
    }

    @Override
    public RepairTaskLeavingFactoryDTO getRepairTaskLeavingFactory(String taskNo) {
        MtcRepairTaskLeavingFactoryExample example = new MtcRepairTaskLeavingFactoryExample();
        example.createCriteria().andTaskNoEqualTo(taskNo);
        example.setOrderByClause("id desc");
        MtcRepairTaskLeavingFactory mtcRepairTaskLeavingFactory = mtcRepairTaskLeavingFactoryMapper.selectByTaskNo(taskNo);
        if (mtcRepairTaskLeavingFactory == null){
            return null;
        }
        RepairTaskLeavingFactoryDTO repairTaskLeavingFactoryDTO = new RepairTaskLeavingFactoryDTO();
        BeanCopyUtils.copyProperties(mtcRepairTaskLeavingFactory, repairTaskLeavingFactoryDTO);
        List<String> deliveryPictureList = new LinkedList<>();
        String str = ComUtil.getString("imageHttpPath") + ComUtil.getString("env");
        for (String picture : mtcRepairTaskLeavingFactory.getDeliveryPictures().split(",")) {
            deliveryPictureList.add(str + picture);
        }
        repairTaskLeavingFactoryDTO.setDeliveryPictureList(deliveryPictureList);
        return repairTaskLeavingFactoryDTO;
    }

    @Override
    public List<RepairTaskLeavingFactoryDTO> getRepairTaskLeavingFactoryList(String taskNo) {
        MtcRepairTaskLeavingFactoryExample example = new MtcRepairTaskLeavingFactoryExample();
        example.createCriteria().andTaskNoEqualTo(taskNo);
        example.setOrderByClause("id desc");
        List<MtcRepairTaskLeavingFactory> mtcRepairTaskLeavingFactories = mtcRepairTaskLeavingFactoryMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(mtcRepairTaskLeavingFactories)){
            return null;
        }
        List<RepairTaskLeavingFactoryDTO> leavingFactoryDTOList = Lists.newArrayList();
        for (MtcRepairTaskLeavingFactory mtcRepairTaskLeavingFactory : mtcRepairTaskLeavingFactories) {
            RepairTaskLeavingFactoryDTO repairTaskLeavingFactoryDTO = new RepairTaskLeavingFactoryDTO();
            BeanCopyUtils.copyProperties(mtcRepairTaskLeavingFactory, repairTaskLeavingFactoryDTO);
            List<String> deliveryPictureList = new LinkedList<>();
            String str = ComUtil.getString("imageHttpPath") + ComUtil.getString("env");
            for (String picture : mtcRepairTaskLeavingFactory.getDeliveryPictures().split(",")) {
                deliveryPictureList.add(str + picture);
            }
            repairTaskLeavingFactoryDTO.setDeliveryPictureList(deliveryPictureList);
            leavingFactoryDTOList.add(repairTaskLeavingFactoryDTO);
        }
        return leavingFactoryDTOList;
    }

    @Override
    public void addRepairTaskLogForWt(AddRepairTaskLogForWtDTO addRepairTaskLogForWtDTO) {
        String queryAccidentInfoByVinUrl = apolloPropertyUtils.getString("vlms.addRepairTaskLog.url");

        try {
            log.info("SERVICE_URL：" + queryAccidentInfoByVinUrl + "请求：" + JSON.toJSONString(addRepairTaskLogForWtDTO));
            JSONObject httpResult = HttpClientUtils.sendRestHttp_post(queryAccidentInfoByVinUrl, "POST", JSON.toJSONString(addRepairTaskLogForWtDTO), new HashMap<>());
            if (null != httpResult) {
                log.info("请求结果：" + httpResult.toJSONString());
            }
        } catch (Exception e) {
            log.error("添加梧桐日志异常：请求参数：" + JSON.toJSONString(addRepairTaskLogForWtDTO), e);
            throw new com.extracme.evcard.mtc.exceptions.BusinessException("添加梧桐日志异常" + e.getMessage());
        }
    }

    @Override
    public void rejectRepairTask(RejectReasonBO rejectReasonBO) {
        String queryAccidentInfoByVinUrl = apolloPropertyUtils.getString("vlms.rejectRepairTask.url");

        try {
            log.info("SERVICE_URL：" + queryAccidentInfoByVinUrl + "请求：" + JSON.toJSONString(rejectReasonBO));
            JSONObject httpResult = HttpClientUtils.sendRestHttp_post(queryAccidentInfoByVinUrl, "POST", JSON.toJSONString(rejectReasonBO), new HashMap<>());
            if (null != httpResult) {
                log.info("请求结果：" + httpResult.toJSONString());
            }
        } catch (Exception e) {
            log.error("通知梧桐-维修任务驳回：请求参数：" + JSON.toJSONString(rejectReasonBO), e);
            throw new com.extracme.evcard.mtc.exceptions.BusinessException("通知梧桐-维修任务驳回异常：" + e.getMessage());
        }
    }

    @Override
    public void updateRepairTaskAdditionalFee(UpdateRepairTaskAdditionalFeeDTO updateRepairTaskAdditionalFeeDTO) {
        String updateRepairTaskAdditionalFeeUrl = apolloPropertyUtils.getString("vlms.updateRepairTaskAdditionalFee.url");

        try {
            log.info("SERVICE_URL：" + updateRepairTaskAdditionalFeeUrl + "请求：" + JSON.toJSONString(updateRepairTaskAdditionalFeeDTO));
            JSONObject httpResult = HttpClientUtils.sendRestHttp_post(updateRepairTaskAdditionalFeeUrl, "POST", JSON.toJSONString(updateRepairTaskAdditionalFeeDTO), new HashMap<>());
            if (null != httpResult) {
                log.info("请求结果：" + httpResult.toJSONString());
            }
        } catch (Exception e) {
            log.error("通知梧桐-维修任务附加费更新：请求参数：" + JSON.toJSONString(updateRepairTaskAdditionalFeeDTO), e);
            throw new com.extracme.evcard.mtc.exceptions.BusinessException("通知梧桐-维修任务附加费更新：" + e.getMessage());
        }
    }

    @Override
    public List<RepairTaskViewBO> queryRepairTaskByVin(String vin) {
        List<RepairTaskViewBO> result = new LinkedList<>();

        List<RepairTask> repairTaskList = repairTaskMapper.queryRepairTaskListByVin(vin);
        if (CollectionUtils.isNotEmpty(repairTaskList)) {
            for (RepairTask repairTask : repairTaskList) {
                RepairTaskViewBO bo = new RepairTaskViewBO();
                BeanCopyUtils.copyProperties(repairTask, bo);
                bo.setCurrentStatusDesc(CurrentTacheStatusEnum.getEnumDesc(repairTask.getCurrentStatus()));
                bo.setVehicleTransferTaskSchedule(repairTask.getVehicleTransferTaskSchedule().toString());
                bo.setInsuranceQuoteTaskSchedule(repairTask.getInsuranceQuoteTaskSchedule().toString());
                bo.setVerificationLossTaskSchedule(repairTask.getVerificationLossTaskSchedule().toString());
                bo.setReassignmentTaskSchedule(repairTask.getReassignmentTaskSchedule().toString());
                bo.setVehicleRepairTaskSchedule(repairTask.getVehicleRepairTaskSchedule().toString());
                bo.setVehicleCheckTaskSchedule(repairTask.getVehicleCheckTaskSchedule().toString());
                bo.setNuclearLossReversionFlag(repairTask.getNuclearLossReversionFlag().toString());
                bo.setMaterialCollectionTaskSchedule(repairTask.getMaterialCollectionTaskSchedule().toString());
                bo.setLossRegistrationTaskSchedule(repairTask.getLossRegistrationTaskSchedule().toString());
                bo.setInsurancePreReviewTaskSchedule(repairTask.getInsurancePreReviewTaskSchedule().toString());
                bo.setSettlementTaskSchedule(repairTask.getSettlementTaskSchedule().toString());
                result.add(bo);
            }
        }

        return result;
    }

    @Override
    public DefaultServiceRespDTO checkDeleteRepairPic(CheckDeleteRepairPicDTO checkDeleteRepairPicDTO, HttpServletRequest request) {
        DefaultServiceRespDTO result = new DefaultServiceRespDTO(-1);

        if (StringUtils.isBlank(checkDeleteRepairPicDTO.getTaskNo()) || StringUtils.isBlank(checkDeleteRepairPicDTO.getPicUrl())) {
            result.setMessage("入参不满足要求！");
        }

        String header = ComUtil.getString("mybatis.configuration.variables.evcard-aliyun-prefix");
        if (checkDeleteRepairPicDTO.getPicUrl().startsWith(header)) {
            String picUrl = checkDeleteRepairPicDTO.getPicUrl().substring(header.length());
            String picUrlPlus = "/" + picUrl;

            VehicleRepairPic vehicleRepairPic = vehicleRepairPicMapper.selectByPicUrl(checkDeleteRepairPicDTO.getTaskNo(), picUrl, picUrlPlus);
            if (null == vehicleRepairPic) {
                return DefaultServiceRespDTO.SUCCESS;
            }
            ComModel comModel = ComUtil.getUserInfo(request);
            if (Objects.equals(vehicleRepairPic.getCreateOperId(), comModel.getCreateOperId())) {
                return DefaultServiceRespDTO.SUCCESS;
            }
        }

        return result;
    }

    @Override
    public Integer checkTaskByBusinessEventNo(String businessEventNo) {
        if (StringUtils.isBlank(businessEventNo)) {
            throw new com.extracme.evcard.mtc.exceptions.BusinessException("事件号异常！");
        }

        List<RepairTask> repairTaskList = new ArrayList<>();
        RepairTask repairTask = repairTaskMapper.selectByTaskNoAll(businessEventNo);
        if (null != repairTask) {
            // 按照任维修任务维度
            repairTaskList.add(repairTask);
        } else {
            // 按照事故任务维度
            repairTaskList = repairTaskMapper.queryRepairTaskListByAccidentNo(businessEventNo);
            repairTaskList = repairTaskList.stream()
                    // 未转自费
                    .filter(task -> task.getReviewToSelFeeFlag() == 0).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(repairTaskList)) {
            return 2;
        }

        // 未关闭 & 存在账单的任务
        List<RepairTask> notEndTaskList = repairTaskList.stream()
                // 任务未关闭
                .filter(task -> !CurrentTacheStatusEnum.END_CURRENT_STATUS.contains(task.getCurrentStatus()))
                .collect(Collectors.toList());
        if (notEndTaskList.size() == 0) {
            return 1;
        }

        return 2;
    }

    @Override
    public Integer checkRepairAdjustApproveByTaskNo(String taskNo) {
        List<MtcRepairAdjustApproveDTO> taxAdjustApproveDTOS = mtcRepairAdjustApproveMapper.selectByTaskNo(taskNo, 2);
        boolean hasTaxValue = taxAdjustApproveDTOS.stream().anyMatch(dto -> dto.getApproveStatus() == 1 || dto.getApproveStatus() == 10);
        if (hasTaxValue) {
            throw new com.extracme.evcard.mtc.exceptions.BusinessException("存在审批中或审批完成的税率调整单！无法创建金额调整单！");
        }

        List<MtcRepairAdjustApproveDTO> mtcRepairAdjustApproveDTOS = mtcRepairAdjustApproveMapper.selectByTaskNo(taskNo, 1);

        // 存在审批中的维修调整审批
        boolean hasValue = mtcRepairAdjustApproveDTOS.stream().anyMatch(dto -> dto.getApproveStatus() == 1);
        return hasValue ? 2 : 1;
    }

    @Override
    public List<CheckTaskCompleteDTO> checkTaskByBusinessEventNoList(List<String> businessEventNoList) {
        List<CheckTaskCompleteDTO> result = new LinkedList<>();

        for (String businessEventNo : businessEventNoList) {
            try {
                CheckTaskCompleteDTO dto = new CheckTaskCompleteDTO();
                dto.setBusinessEventNo(businessEventNo);
                dto.setCompleteStatus(checkTaskByBusinessEventNo(businessEventNo));
                result.add(dto);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    @Override
    public List<CheckTaskCompleteDTO> checkTaskByRepairTaskNoList(List<String> repairTaskNoList) {
        List<CheckTaskCompleteDTO> result = new LinkedList<>();

        for (String taskNo : repairTaskNoList) {
            try {
                CheckTaskCompleteDTO dto = new CheckTaskCompleteDTO();
                dto.setRepairTaskNo(taskNo);
                dto.setCompleteStatus(checkRepairAdjustApproveByTaskNo(taskNo));
                result.add(dto);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    @Override
    public BudgetInfoDTO getBudgetInfoByTaskNo(String taskNo, HttpServletRequest request) {
        BudgetInfoDTO result = new BudgetInfoDTO();

        // 兼容没有token
        String token = StringUtils.EMPTY;
        if (request != null){
            token = ComUtil.getUserInfo(request).getToken();
        }

        RepairTask repairTask = repairTaskMapper.selectByTaskNoAll(taskNo);
        if (null == repairTask) {
            throw new com.extracme.evcard.mtc.exceptions.BusinessException("任务编号不存在！");
        }

        String useTime;
        if (null != repairTask.getSendRepairTime()) {
            useTime = DateUtil.getFormatDate(repairTask.getSendRepairTime(), DateUtil.DATE_TYPE2);
        } else {
            useTime = DateUtil.getFormatDate(repairTask.getTaskInflowTime(), DateUtil.DATE_TYPE2);
        }

        /*VehicleHistoryInfoDTO vehicleHistoryInfoDTO = bfcCostService.getVehicleHistoryByTime(repairTask.getVin(), useTime);
        if (null == vehicleHistoryInfoDTO) {
            log.error("获取车辆历史信息失败！");
            return null;
        }*/

        // 使用维修任务中的数据
        VehicleHistoryInfoDTO vehicleHistoryInfoDTO = new VehicleHistoryInfoDTO();
        vehicleHistoryInfoDTO.setProductLine(repairTask.getProductLine());
        vehicleHistoryInfoDTO.setSubProductLine(repairTask.getSubProductLine());

        // 查预算控制中心代码、预算控制中心名称
        String sapCode = orgInfoMapper.getErpOrgId(repairTask.getOrgId());
        BudgetControlCenterDTO budgetControlCenterDTO = bfcCostService.getBudgetControlCenter(sapCode, vehicleHistoryInfoDTO, token);

        ByApproveUtil byApproveUtil = new ByApproveUtil(ByApproveEnum.QUERY_SIMPLE_FM_BUDGET_INFO, "", baiYangConfiguration);
        String budgetAccount = getBudgetAccount(repairTask.getRepairTypeId().intValue(), repairTask.getPropertyStatus(), repairTask.getSubProductLine());
        if (null == budgetAccount) {
            log.error("获取预算科目失败！");
            return null;
        }


        try {
            SimpleDateFormat sdfInput = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfCurrentYear = new SimpleDateFormat("yyyy-MM");

            Date useDate = sdfInput.parse(useTime);
            Calendar calUse = Calendar.getInstance();
            calUse.setTime(useDate);

            Calendar calNow = Calendar.getInstance();
            calNow.setTime(new Date());

            String budgetMonth = sdfCurrentYear.format(useDate);

            String body = byApproveUtil.queryFMBudgetInfo(budgetAccount, budgetControlCenterDTO.getBudgetControlCenterCode(), budgetMonth);
            cn.hutool.json.JSONObject bodyJson = JSONUtil.parseObj(body);
            if ("0".equals(bodyJson.getStr("retCode"))) {
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    result = objectMapper.readValue(bodyJson.getStr("budgetInfo"), BudgetInfoDTO.class);
                    log.info("可用预算：{}", result.getAvailableBalance());
                    result.setBudgetControlCenter(budgetControlCenterDTO);
                    result.setBudgetAccountCode(budgetAccount);
                    result.setBudgetAccountName(apolloPropertyUtils.getString(budgetAccount + "_name"));
                    result.setBudgetMonth(budgetMonth);

                    // 组装预审需要的信息
                    buildReveiwInfo(result, repairTask, useTime, sapCode);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    private void buildReveiwInfo(BudgetInfoDTO result, RepairTask repairTask, String useTime, String sapCode) {

        try {
            String adjustCostCenter = apolloPropertyUtils.getString("adjustCostCenter");
            String adjustCostCenterName = apolloPropertyUtils.getString("adjustCostCenterName");
            // 获取预审报价金额 - 增强空值校验和负数校验
            BigDecimal repairReviewTotalAmount = Optional.ofNullable(repairTask.getRepairReviewTotalAmount()).orElse(BigDecimal.ZERO);
            BigDecimal estimatedClaimAmount = Optional.ofNullable(repairTask.getEstimatedClaimAmount()).orElse(BigDecimal.ZERO);
            BigDecimal userAssumedAmount = Optional.ofNullable(repairTask.getUserAssumedAmount()).orElse(BigDecimal.ZERO);

            BigDecimal adjustAmount = repairReviewTotalAmount.subtract(estimatedClaimAmount).subtract(userAssumedAmount);
            // 负数校验：如果计算结果为负数，则设置为0
            BigDecimal finalAdjustAmount = adjustAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : adjustAmount;

            result.setAdjustAmount(finalAdjustAmount.toString());

            // 所属公司
            result.setBudgetBelongOrgId(repairTask.getOrgId());
            result.setBudgetBelongSapCode(sapCode);
            result.setBudgetBelongOrgName(repairTask.getOrgName());

            // 解析字符串为LocalDateTime对象
            LocalDateTime dateTime = DateUtil.getDateTimeFromStr(useTime, "yyyy-MM-dd HH:mm:ss");
            // 获取年份和月份
            result.setBudgetAdjustYear(dateTime.getYear() + "");
            result.setBudgetAdjustMonth(dateTime.getMonth().getValue() + "");

            // 预算科目
            result.setBudgetAccountAdd(adjustCostCenter);
            result.setBudgetAccountAddName(adjustCostCenterName);

            // 查询成本中心
            /*CostConfigDTO costConfigDTO = bfcCostService.getCostCenterBySapCode(sapCode);
            if (null == costConfigDTO){
                log.error("获取成本中心失败！");
            }else {
                result.setCostCenterAdd(costConfigDTO.getCostCenterCode());
                result.setCostCenterAddName(costConfigDTO.getCostCenterName());

            }*/

            // 根据控制中心和预算查询成本中心
            BudgetCostConfigDTO budgetCostConfigDTO = bfcCostService.queryCostAccountInfo(result.getBudgetControlCenter().getBudgetControlCenterCode(), adjustCostCenter);
            if(budgetCostConfigDTO != null){
                result.setCostCenterAdd(budgetCostConfigDTO.getCostCenterCode());
                result.setCostCenterAddName(budgetCostConfigDTO.getControlCenterName());
            }

        }catch (Exception e){
            log.error("获取预算信息失败！", e);
        }

    }

    /**
     * 单独修改客户直付金额逻辑
     *
     * @param overallAdjustCustDTO
     * @param comModel
     * @return
     */
    @Override
    public DefaultServiceRespDTO adjustCustAmount(OverallAdjustCustDTO overallAdjustCustDTO, ComModel comModel) {
        if (StringUtils.isBlank(overallAdjustCustDTO.getTaskNo())) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "入参不满足要求！");
        }

        RepairTask repairTask = repairTaskMapper.selectByTaskNoV(overallAdjustCustDTO.getTaskNo());
        if (null == repairTask) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "维修任务不存在！");
        }

        if (overallAdjustCustDTO.getOriginCustAmount().compareTo(repairTask.getCustAmount()) == 0 && overallAdjustCustDTO.getOriginCustPaysDirect().equals(repairTask.getCustPaysDirect())) {
            log.info("无需修改客户直付金额！");
            return DefaultServiceRespDTO.SUCCESS;
        }

        // 单独修改客户直付金额逻辑（用户承担=客户直付金额）
        log.info("任务【{}】单独修改客户直付金额", overallAdjustCustDTO.getTaskNo());
        RepairCustInfoDTO repairCustInfoDTO = new RepairCustInfoDTO();
        BeanCopyUtils.copyProperties(repairTask, repairCustInfoDTO);
        repairCustInfoDTO.setUserAssumedAmount(repairCustInfoDTO.getCustAmount());
        repairCustInfoDTO.setId(repairTask.getId());
        repairCustInfoDTO.setNotUserAssumedAmount(BigDecimal.ZERO);
        repairTaskMapper.adjustCustAmount(repairCustInfoDTO);
        // 更新自费金额
        updateSelfFundedAmount(repairTask.getTaskNo(), manualUtils.getAccidentDamage(repairTask.getAccidentNo(), comModel.getToken()));

        return DefaultServiceRespDTO.SUCCESS;
    }

    /**
     * 调整用户承担金额逻辑
     *
     * @param repairCustInfoDTO
     * @param comModel
     * @return
     */
    @Override
    public DefaultServiceRespDTO adjustCustAmount(RepairCustInfoDTO repairCustInfoDTO, ComModel comModel) {
        if (null == repairCustInfoDTO) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "入参不满足要求！");
        }

        RepairTask repairTask = repairTaskMapper.selectByTaskNoV(repairCustInfoDTO.getTaskNo());
        if (null == repairTask) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "维修任务不存在！");
        }
        repairCustInfoDTO.setId(repairTask.getId());

        if (repairCustInfoDTO.getCustAmount().compareTo(repairCustInfoDTO.getUserAssumedAmount()) < 0) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "用户承担金额不可大于用户直付金额！");
        }

        // 是否客户直付 = 否 -> 清空数据
        if (repairCustInfoDTO.getCustPaysDirect() == 2) {
            repairCustInfoDTO.setCustAmount(BigDecimal.ZERO);
            repairCustInfoDTO.setUserAssumedAmount(BigDecimal.ZERO);
            repairCustInfoDTO.setNotUserAssumedAmount(BigDecimal.ZERO);
        }
        repairCustInfoDTO.setNotUserAssumedAmount(repairCustInfoDTO.getCustAmount().subtract(repairCustInfoDTO.getUserAssumedAmount()));
        repairTaskMapper.adjustCustAmount(repairCustInfoDTO);

        // 处理客户直付凭证
        List<VehicleRepairPic> allVehicleRepairPics = vehicleRepairPicMapper.getAllPicListByTaskNo(repairCustInfoDTO.getTaskNo());
        List<VehicleRepairPic> insertPicList;
        List<VehicleRepairPic> deletePicList;
        try {
            // 客户直付凭证
            insertPicList = new ArrayList<>(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, repairCustInfoDTO.getCustPicture(), BigDecimal.valueOf(22)),
                    BigDecimal.valueOf(22),
                    repairCustInfoDTO.getTaskNo(),
                    comModel));
            deletePicList = new ArrayList<>(RepairTaskUtils.getDeletePics(allVehicleRepairPics, repairCustInfoDTO.getCustPicture(), BigDecimal.valueOf(22)));
        } catch (MtcBusinessRuntimeException e) {
            log.error("维修任务保存，包装图片异常！");
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }

        if (CollectionUtils.isNotEmpty(deletePicList)) {
            // 批量删除
            vehicleRepairPicMapper.delMaterialPic(deletePicList.stream().map(VehicleRepairPic::getId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(insertPicList)) {
            // 批量插入
            vehicleRepairPicMapper.batchInsert(insertPicList);
        }

        // 更新自费金额
        updateSelfFundedAmount(repairTask.getTaskNo(), manualUtils.getAccidentDamage(repairTask.getAccidentNo(), comModel.getToken()));

        return DefaultServiceRespDTO.SUCCESS;
    }

    @Override
    public AbstractOrderInfoDTO getOrderInfo(String taskNo) {
        RepairTask repairTask = repairTaskMapper.selectByTaskNoV(taskNo);
        if (null == repairTask) {
            throw new MtcBusinessRuntimeException("维修任务不存在！", "-1");
        }

        AbstractOrderInfoDTO abstractOrderInfoDTO = newAndOldOrderConversionService.getOrderInfoById(repairTask.getAssociatedOrder(), repairTask.getVin());
        if (null == abstractOrderInfoDTO) {
            abstractOrderInfoDTO = new OrderInfoDTO();
        }
        abstractOrderInfoDTO.setRelateType(repairTask.getRelateType());
        abstractOrderInfoDTO.setOrderRemark(repairTask.getOrderRemark());

        return abstractOrderInfoDTO;
    }

    @Override
    public AbstractOrderInfoDTO checkAdjustOrder(String orderSeq, String taskNo) {
        RepairTask repairTask = repairTaskMapper.selectByTaskNoV(taskNo);
        if (null == repairTask) {
            throw new MtcBusinessRuntimeException("维修任务不存在！不可调整订单", "-1");
        }
        if (!repairTask.getAssociatedOrder().equals(orderSeq)) {
            if (repairTask.getRepairTypeId().intValue() == 1) {
                throw new MtcBusinessRuntimeException("事故维修任务，不可调整订单！", "-1");
            }
        }

        return newAndOldOrderConversionService.getOrderInfoById(orderSeq, null);
    }

    @Override
    public BigDecimal getSelfFundedAmount(RepairAmountDTO repairAmountDTO, AccidentDamageDTO accidentDamageDTO) {
        if (null == repairAmountDTO) {
            throw new MtcBusinessRuntimeException("数据错误，更新自费金额失败！", "-1");
        }
        if (null == repairAmountDTO.getVehicleInsuranceTotalAmount() || repairAmountDTO.getVehicleInsuranceTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        if (null != accidentDamageDTO && null != accidentDamageDTO.getVehicleTotalLossDisposalFlag() && accidentDamageDTO.getVehicleTotalLossDisposalFlag() == 1) {
            // 【本车全损处置】=是，则【自费金额】=0元
            log.info("重新计算任务【{}】自费金额为：{}", repairAmountDTO.getTaskNo(), "0");
            return BigDecimal.ZERO;
        } else {
            // 【自费金额】=【维修总金额】-【预估保险理赔金额】-【非用户承担金额】
            BigDecimal selfFundedAmount = repairAmountDTO.getVehicleInsuranceTotalAmount().subtract(repairAmountDTO.getEstimatedClaimAmount()).subtract(repairAmountDTO.getUserAssumedAmount());
            if (selfFundedAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new MtcBusinessRuntimeException("自费金额不能小于0！请确认后重新提交！", "-1");
            }
            log.info("任务【{}】更新自费金额为：{}", repairAmountDTO.getTaskNo(), selfFundedAmount);
            return selfFundedAmount;
        }
    }

    @Override
    public void updateSelfFundedAmount(String taskNo, AccidentDamageDTO accidentDamageDTO) {
        RepairTask repairTask = repairTaskMapper.selectByTaskNoV(taskNo);
        if (null == repairTask) {
            throw new MtcBusinessRuntimeException("任务不存在，更新自费金额失败！", "-1");
        }
        if (repairTask.getCurrentTache() < 30L || repairTask.getCurrentTache() == 110L) {
            return;
        }

        // 重新计算自费金额
        RepairAmountDTO repairAmountDTO = new RepairAmountDTO(
                repairTask.getTaskNo(),
                repairTask.getVehicleInsuranceTotalAmount(),
                repairTask.getUserAssumedAmount(),
                repairTask.getEstimatedClaimAmount());
        BigDecimal selfFundedAmount = getSelfFundedAmount(repairAmountDTO, accidentDamageDTO);
        repairTaskMapper.updateSelfFundedAmount(repairTask.getId(), selfFundedAmount);
        log.info("任务【{}】更新自费金额为：{}", repairTask.getTaskNo(), selfFundedAmount);
    }

    @Override
    public List<MtcRepairAdjustApproveDTO> getAdjustApproveList(String taskNo, Integer approveType) {
        List<MtcRepairAdjustApproveDTO> mtcRepairAdjustApproveDTOS = mtcRepairAdjustApproveMapper.selectByTaskNo(taskNo, approveType);
        List<String> applicationIds = mtcRepairAdjustApproveDTOS.stream().map(MtcRepairAdjustApproveDTO::getApplicationId).collect(Collectors.toList());
        Map<String, String> detailUrlMap = repairTaskAdjustApproveService.getDetailUrlMap(applicationIds);
        if (CollectionUtils.isNotEmpty(mtcRepairAdjustApproveDTOS)) {
            for (MtcRepairAdjustApproveDTO mtcRepairAdjustApproveDTO : mtcRepairAdjustApproveDTOS) {
                mtcRepairAdjustApproveDTO.setDetailUrl(detailUrlMap.get(mtcRepairAdjustApproveDTO.getApplicationId()));
            }
        }
        return mtcRepairAdjustApproveDTOS;
    }

    /**
     * 获取预算科目
     *
     * @param repairTypeId   维修任务类型
     * @param propertyStatus 资产状态
     * @return 预算科目
     */
    private String getBudgetAccount(Integer repairTypeId, Integer propertyStatus, Integer subProductLine) {
        if (propertyStatus != 0
                && propertyStatus != 1
                && propertyStatus != 6
                && propertyStatus != 7) {
            return apolloPropertyUtils.getString("COST_BUDGET_CLEAN_SUBJECT.code");
        }

        if (null != subProductLine && subProductLine == 9) {
            return apolloPropertyUtils.getString("COST_BUDGET_OFFICE_SUBJECT.code");
        }

        if (RepairTypeEnum.getFeeTypeCode(repairTypeId).equals("1")) {
            return apolloPropertyUtils.getString("COST_BUDGET_REPAIR_SUBJECT.code");
        }

        if (RepairTypeEnum.getFeeTypeCode(repairTypeId).equals("2")) {
            return apolloPropertyUtils.getString("COST_BUDGET_MAINTAIN_SUBJECT.code");
        }

        return null;
    }
}
