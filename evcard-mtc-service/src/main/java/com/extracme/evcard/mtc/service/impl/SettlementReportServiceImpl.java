package com.extracme.evcard.mtc.service.impl;

import com.extracme.evcard.mtc.bo.*;
import com.extracme.evcard.mtc.common.ComModel;
import com.extracme.evcard.mtc.common.ComUtil;
import com.extracme.evcard.mtc.common.Contants;
import com.extracme.evcard.mtc.dao.*;
import com.extracme.evcard.mtc.model.MtcFileInfo;
import com.extracme.evcard.mtc.model.MtcLossFitInfo;
import com.extracme.evcard.mtc.service.SettlementReportService;
import com.extracme.evcard.mtc.util.ApolloPropertyUtils;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 项目名称：evcard-mtc-service 类名称：SettlementReportServiceImpl 类描述：统计报表业务层实现类
 * 创建人：sunb-孙彬 创建时间：2017年12月13日上午9:49:18 修改备注 @version1.0
 */
@Service
public class SettlementReportServiceImpl implements SettlementReportService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApolloPropertyUtils apolloPropertyUtils;

    @Resource
    private RepairTaskMapper repairTaskMapper;

    @Resource
    private RepairDepotInfoMapper repairDepotInfoMapper;

    @Autowired
    private MtcLossFitInfoMapper mtcLossFitInfoMapper;

    @Autowired
    private MtcLossRepairInfoMapper mtcLossRepairInfoMapper;

    @Resource
    private MtcFileInfoMapper mtcFileInfoMapper;

    @Resource
    private MtcFileInfoServiceImpl mtcFileInfoServiceImpl;

    @Value("${download.url}")
    String downloadUrl;

    @Value("${requestFile.dir}")
    String requestFileUrl;

    @Override
    public PageInfo<SettlementReportBO> getSettlementReportInfo(Integer pageNum, Integer pageSize, String orgId,
                                                                Integer repairTypeId, String taskNo, String vehicleNo, String vehicleModelSeq, String taskInflowStartTime,
                                                                String taskInflowEndTime, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                                String vehicleRepairStartTime, String vehicleRepairEndTime, String vehicleCheckStartTime,
                                                                String vehicleCheckEndTime, String verificationLossCheckStartTime, String verificationLossCheckEndTime,
                                                                String repairDepotId, Integer renttype, List<Integer> renttypeList,
                                                                Integer noDeductiblesFlag, Integer factOperateTag,
                                                                String accidentNo, Integer reviewToSelFeeFlag, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        ComModel comModel = ComUtil.getUserInfo(request);
        SettlementReportInfoQueryBO settlementReportInfoQueryBO = getSettlementReportInfoQueryBO(orgId, taskNo,
                repairTypeId, renttype, vehicleNo, vehicleModelSeq, taskInflowStartTime, taskInflowEndTime,
                vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime,
                vehicleCheckStartTime, vehicleCheckEndTime,verificationLossCheckStartTime, verificationLossCheckEndTime, 
                repairDepotId, noDeductiblesFlag, null, factOperateTag, accidentNo, reviewToSelFeeFlag, null, comModel);
        
        PageHelper.startPage(pageNum, pageSize);
        List<SettlementReportBO> settlementReportBOList = repairTaskMapper
                .getSettlementReportInfo(settlementReportInfoQueryBO);
        if (CollectionUtils.isEmpty(settlementReportBOList)) {
            return new PageInfo<>(settlementReportBOList);
        }

        // 查询旧版本换件信息
        List<ViewReplaceItemDetailBO> viewReplaceItemDetailBOList = repairTaskMapper
                .queryReplaceItemDetailList(settlementReportBOList);
        Map<String, StringBuilder> oldReplaceItemMap = new HashMap<>(16);
        for (ViewReplaceItemDetailBO viewReplaceItemDetailBO : viewReplaceItemDetailBOList) {
            StringBuilder stringBuilder = oldReplaceItemMap.get(viewReplaceItemDetailBO.getTaskNo());
            if (stringBuilder == null) {
                stringBuilder = new StringBuilder(viewReplaceItemDetailBO.getPartName());
            } else {
                stringBuilder.append(",").append(viewReplaceItemDetailBO.getPartName());
            }
            oldReplaceItemMap.put(viewReplaceItemDetailBO.getTaskNo(), stringBuilder);
        }

        // 查询旧版本工时信息
        List<ViewRepairItemDetailBO> viewRepairItemDetailBOList = repairTaskMapper
                .queryRepairItemDetailList(settlementReportBOList);
        Map<String, StringBuilder> oldRepairItemMap = new HashMap<>(16);
        for (ViewRepairItemDetailBO viewRepairItemDetailBO : viewRepairItemDetailBOList) {
            StringBuilder stringBuilder = oldRepairItemMap.get(viewRepairItemDetailBO.getTaskNo());
            if (stringBuilder == null) {
                stringBuilder = new StringBuilder(viewRepairItemDetailBO.getRepairName());
            } else {
                stringBuilder.append(",").append(viewRepairItemDetailBO.getRepairName());
            }
            oldRepairItemMap.put(viewRepairItemDetailBO.getTaskNo(), stringBuilder);
        }

        // 查询精友换件信息
        List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper.queryReplaceItemList(settlementReportBOList);
        Map<String, StringBuilder> newReplaceItemMap = new HashMap<>(16);
        for (MtcLossFitInfo mtcLossFitInfo : mtcLossFitInfoList) {
            StringBuilder stringBuilder = newReplaceItemMap.get(mtcLossFitInfo.getTaskNo());
            if (stringBuilder == null) {
                stringBuilder = new StringBuilder(mtcLossFitInfo.getItemName());
            } else {
                stringBuilder.append(",").append(mtcLossFitInfo.getItemName());
            }
            newReplaceItemMap.put(mtcLossFitInfo.getTaskNo(), stringBuilder);
        }

        // 查询精友工时信息
        List<MtcLossRepairInfoBO> mtcLossRepairInfoBOList = mtcLossRepairInfoMapper
                .queryRepairItemList(settlementReportBOList);
        Map<String, StringBuilder> newRepairItemMap = new HashMap<>(16);
        for (MtcLossRepairInfoBO mtcLossRepairInfoBO : mtcLossRepairInfoBOList) {
            StringBuilder stringBuilder = newRepairItemMap.get(mtcLossRepairInfoBO.getTaskNo());
            if (stringBuilder == null) {
                stringBuilder = new StringBuilder(mtcLossRepairInfoBO.getItemName());
                stringBuilder.append("(").append(mtcLossRepairInfoBO.getRepairModeName()).append(")");
            } else {
                stringBuilder.append(",").append(mtcLossRepairInfoBO.getItemName()).append("(")
                        .append(mtcLossRepairInfoBO.getRepairModeName()).append(")");
            }
            newRepairItemMap.put(mtcLossRepairInfoBO.getTaskNo(), stringBuilder);
        }

        for (SettlementReportBO settlementReportBO : settlementReportBOList) {
            if (StringUtils.equals("4", settlementReportBO.getRepairTypeId())) {
                settlementReportBO.setVehicleReciveTime(StringUtils.EMPTY);
                settlementReportBO.setVehicleRepairFinishTime(StringUtils.EMPTY);
                settlementReportBO.setVehicleCheckFinishTime(StringUtils.EMPTY);
                settlementReportBO.setPartName("轮胎*" + settlementReportBO.getTirenumber());
                settlementReportBO.setPartTotalMoney(StringUtils.EMPTY);
                settlementReportBO.setRepairTotalMoney(StringUtils.EMPTY);
                settlementReportBO.setTotalMoney(StringUtils.EMPTY);
            } else {
                if (newReplaceItemMap.containsKey(settlementReportBO.getTaskNo())) {
                    settlementReportBO.setPartName(newReplaceItemMap.get(settlementReportBO.getTaskNo()).toString());
                } else if (oldReplaceItemMap.containsKey(settlementReportBO.getTaskNo())) {
                    settlementReportBO.setPartName(oldReplaceItemMap.get(settlementReportBO.getTaskNo()).toString());
                }
                if (newRepairItemMap.containsKey(settlementReportBO.getTaskNo())) {
                    settlementReportBO.setRepairName(newRepairItemMap.get(settlementReportBO.getTaskNo()).toString());
                } else if (oldRepairItemMap.containsKey(settlementReportBO.getTaskNo())) {
                    settlementReportBO.setRepairName(oldRepairItemMap.get(settlementReportBO.getTaskNo()).toString());
                }
            }
            // 计算税前总价
            if (StringUtils.isNotBlank(settlementReportBO.getTotalMoney()) && settlementReportBO.getTotalMoneyPreTax() == null) {
                String taxRate = apolloPropertyUtils.getString("vehicleRepairAmountTaxRate");
                BigDecimal totalMoneyPreTax = new BigDecimal(settlementReportBO.getTotalMoney()).divide(new BigDecimal(String.valueOf(1 + Double.parseDouble(taxRate))), 2, BigDecimal.ROUND_HALF_UP);
                settlementReportBO.setTotalMoneyPreTax(String.valueOf(totalMoneyPreTax));
            }
        }

        return new PageInfo<>(settlementReportBOList);
    }

    @Override
    public PageInfo<SubSettlementReportBO> getSubSettlementReportInfo(Integer pageNum, Integer pageSize, String orgId,
                                                                      Integer repairTypeId, String taskNo, String vehicleNo, String vehicleModelSeq, String taskInflowStartTime,
                                                                      String taskInflowEndTime, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                                      String vehicleRepairStartTime, String vehicleRepairEndTime, String vehicleCheckStartTime,
                                                                      String vehicleCheckEndTime, String repairDepotId, Integer renttype, List<Integer> renttypeList,
                                                                      Integer subType, String projectType, Integer isAll, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        ComModel comModel = ComUtil.getUserInfo(request);
        SettlementReportInfoQueryBO settlementReportInfoQueryBO = getSettlementReportInfoQueryBO(orgId, taskNo,
                repairTypeId, renttype, vehicleNo, vehicleModelSeq, taskInflowStartTime, taskInflowEndTime,
                vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime,
                vehicleCheckStartTime, vehicleCheckEndTime, null,null,
                repairDepotId, null, subType, null, null, null, projectType, comModel);
        settlementReportInfoQueryBO.setPageNum(pageNum);
        settlementReportInfoQueryBO.setPageSize(pageSize);

        PageInfo pageInfo = new PageInfo();
        List<SubSettlementReportBO> subSettlementReportBOList = repairTaskMapper.getSubSettlementReportInfo(settlementReportInfoQueryBO);
        if (!CollectionUtils.isEmpty(subSettlementReportBOList)) {
            pageInfo.setList(subSettlementReportBOList);
        }

        if (isAll == 1) {
            Integer total = repairTaskMapper.getSubSettlementReportInfoForCount(settlementReportInfoQueryBO);
            pageInfo.setTotal(total);
        } else {
            pageInfo.setTotal(-1);
        }

        return pageInfo;
    }

    @Override
    public DefaultServiceRespDTO operateExportSettlementReportInfo(String orgId, Integer repairTypeId, String taskNo,
                                                                   String vehicleNo, String vehicleModelSeq, String taskInflowStartTime, String taskInflowEndTime,
                                                                   String vehicleReciveStartTime, String vehicleReciveEndTime, String vehicleRepairStartTime,
                                                                   String vehicleRepairEndTime, String vehicleCheckStartTime, String vehicleCheckEndTime,
                                                                   String verificationLossCheckStartTime, String verificationLossCheckEndTime, String repairDepotId,
                                                                   Integer renttype, List<Integer> renttypeList, Integer noDeductiblesFlag, Integer factOperateTag, String accidentNo, Integer reviewToSelFeeFlag, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);
        try {
            ComModel comModel = ComUtil.getUserInfo(request);
            SettlementReportInfoQueryBO settlementReportInfoQueryBO = getSettlementReportInfoQueryBO(orgId, taskNo,
                    repairTypeId, renttype, vehicleNo, vehicleModelSeq, taskInflowStartTime, taskInflowEndTime,
                    vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime,
                    vehicleCheckStartTime, vehicleCheckEndTime, verificationLossCheckStartTime, verificationLossCheckEndTime,
                    repairDepotId, noDeductiblesFlag, null, factOperateTag, accidentNo, reviewToSelFeeFlag, null, comModel);

            String dateStr = new SimpleDateFormat("yyyyMMddHHmmss").format(System.currentTimeMillis());
            String fileName = Contants.SETTLEMENT_REPORT_NAME + dateStr + Contants.XLSX;
            String filePath = requestFileUrl + fileName;

            MtcFileInfo insertMtcFileInfo = mtcFileInfoServiceImpl.saveMtcFileInfo(comModel, 1, fileName, 1);

            Workbook workbook = new SXSSFWorkbook(100);
            Sheet sheet = workbook.createSheet();

            Cell cell;

            String[] headers = apolloPropertyUtils.getString("export.SettlementReport.title")
                    .split(Contants.COMMA_SIGN_SPLIT_NAME);

            int columnNum = headers.length;
            Row row = sheet.createRow(0);
            for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
                cell = row.createCell(columnIndex);
                cell.setCellValue(headers[columnIndex]);
            }

            Integer pageSize = 500;
            String queryIndex;
            int rowIndex = 1;
            settlementReportInfoQueryBO.setPageSize(pageSize);
            List<SettlementReportBO> settlementReportBOList = repairTaskMapper
                    .getSettlementReportInfoForExcel(settlementReportInfoQueryBO);
            while (settlementReportBOList != null && settlementReportBOList.size() > 0) {

                settlementReportInfoQueryBO.setList(settlementReportBOList);

                // 查询旧版本换件信息
                List<ViewReplaceItemDetailBO> viewReplaceItemDetailBOList = repairTaskMapper
                        .queryReplaceItemDetailList(settlementReportBOList);
                Map<String, StringBuilder> oldReplaceItemMap = new HashMap<>(16);
                for (ViewReplaceItemDetailBO viewReplaceItemDetailBO : viewReplaceItemDetailBOList) {
                    StringBuilder stringBuilder = oldReplaceItemMap.get(viewReplaceItemDetailBO.getTaskNo());
                    if (stringBuilder == null) {
                        stringBuilder = new StringBuilder(viewReplaceItemDetailBO.getPartName());
                    } else {
                        stringBuilder.append(",").append(viewReplaceItemDetailBO.getPartName());
                    }
                    oldReplaceItemMap.put(viewReplaceItemDetailBO.getTaskNo(), stringBuilder);
                }

                // 查询旧版本工时信息
                List<ViewRepairItemDetailBO> viewRepairItemDetailBOList = repairTaskMapper
                        .queryRepairItemDetailList(settlementReportBOList);
                Map<String, StringBuilder> oldRepairItemMap = new HashMap<>(16);
                for (ViewRepairItemDetailBO viewRepairItemDetailBO : viewRepairItemDetailBOList) {
                    StringBuilder stringBuilder = oldRepairItemMap.get(viewRepairItemDetailBO.getTaskNo());
                    if (stringBuilder == null) {
                        stringBuilder = new StringBuilder(viewRepairItemDetailBO.getRepairName());
                    } else {
                        stringBuilder.append(",").append(viewRepairItemDetailBO.getRepairName());
                    }
                    oldRepairItemMap.put(viewRepairItemDetailBO.getTaskNo(), stringBuilder);
                }

                // 查询精友换件信息
                List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper
                        .queryReplaceItemList(settlementReportBOList);
                Map<String, StringBuilder> newReplaceItemMap = new HashMap<>(16);
                for (MtcLossFitInfo mtcLossFitInfo : mtcLossFitInfoList) {
                    StringBuilder stringBuilder = newReplaceItemMap.get(mtcLossFitInfo.getTaskNo());
                    if (stringBuilder == null) {
                        stringBuilder = new StringBuilder(mtcLossFitInfo.getItemName());
                    } else {
                        stringBuilder.append(",").append(mtcLossFitInfo.getItemName());
                    }
                    newReplaceItemMap.put(mtcLossFitInfo.getTaskNo(), stringBuilder);
                }

                // 查询精友工时信息
                List<MtcLossRepairInfoBO> mtcLossRepairInfoBOList = mtcLossRepairInfoMapper
                        .queryRepairItemList(settlementReportBOList);
                Map<String, StringBuilder> newRepairItemMap = new HashMap<>(16);
                for (MtcLossRepairInfoBO mtcLossRepairInfoBO : mtcLossRepairInfoBOList) {
                    StringBuilder stringBuilder = newRepairItemMap.get(mtcLossRepairInfoBO.getTaskNo());
                    if (stringBuilder == null) {
                        stringBuilder = new StringBuilder(mtcLossRepairInfoBO.getItemName());
                        stringBuilder.append("(").append(mtcLossRepairInfoBO.getRepairModeName()).append(")");
                    } else {
                        stringBuilder.append(",").append(mtcLossRepairInfoBO.getItemName()).append("(")
                                .append(mtcLossRepairInfoBO.getRepairModeName()).append(")");
                    }
                    newRepairItemMap.put(mtcLossRepairInfoBO.getTaskNo(), stringBuilder);
                }

                for (SettlementReportBO settlementReportBO : settlementReportBOList) {
                    if (StringUtils.equals("4", settlementReportBO.getRepairTypeId())) {
                        settlementReportBO.setVehicleReciveTime(StringUtils.EMPTY);
                        settlementReportBO.setVehicleRepairFinishTime(StringUtils.EMPTY);
                        settlementReportBO.setVehicleCheckFinishTime(StringUtils.EMPTY);
                        settlementReportBO.setPartName("轮胎*" + settlementReportBO.getTirenumber());
                        settlementReportBO.setPartTotalMoney(StringUtils.EMPTY);
                        settlementReportBO.setRepairTotalMoney(StringUtils.EMPTY);
                        settlementReportBO.setTotalMoney(StringUtils.EMPTY);
                    } else {
                        if (newReplaceItemMap.containsKey(settlementReportBO.getTaskNo())) {
                            settlementReportBO
                                    .setPartName(newReplaceItemMap.get(settlementReportBO.getTaskNo()).toString());
                        } else if (oldReplaceItemMap.containsKey(settlementReportBO.getTaskNo())) {
                            settlementReportBO
                                    .setPartName(oldReplaceItemMap.get(settlementReportBO.getTaskNo()).toString());
                        }
                        if (newRepairItemMap.containsKey(settlementReportBO.getTaskNo())) {
                            settlementReportBO
                                    .setRepairName(newRepairItemMap.get(settlementReportBO.getTaskNo()).toString());
                        } else if (oldRepairItemMap.containsKey(settlementReportBO.getTaskNo())) {
                            settlementReportBO
                                    .setRepairName(oldRepairItemMap.get(settlementReportBO.getTaskNo()).toString());
                        }
                    }
                    row = sheet.createRow(rowIndex);
                    row.createCell(0).setCellValue(settlementReportBO.getVehicleNo());
                    row.createCell(1).setCellValue(settlementReportBO.getVehicleModelName());
                    row.createCell(2).setCellValue(settlementReportBO.getVin());
                    String noDeductiblesFlagDesc = "";
                    if (settlementReportBO.getNoDeductiblesFlag() == null) {
                        noDeductiblesFlagDesc = "空";
                    } else if (settlementReportBO.getNoDeductiblesFlag() == 0) {
                        noDeductiblesFlagDesc = "否";
                    } else if (settlementReportBO.getNoDeductiblesFlag() == 1) {
                        noDeductiblesFlagDesc = "是";
                    }
                    row.createCell(3).setCellValue(noDeductiblesFlagDesc);
                    row.createCell(4).setCellValue(settlementReportBO.getRepairTypeName());
                    row.createCell(5).setCellValue(settlementReportBO.getAccidentReportNumber());
                    row.createCell(6).setCellValue(ComUtil.getReviewToSelFeeFlagStr(settlementReportBO.getReviewToSelFeeFlag()));
                    //自费和进保任务确认类型
                    if(settlementReportBO.getConfirmType() != null){
                        switch (settlementReportBO.getConfirmType()){
                            case 1:
                                row.createCell(7).setCellValue("公司自费");
                                break;
                            case 2:
                                row.createCell(7).setCellValue("客户付费");
                                break;
                            case 3:
                                row.createCell(7).setCellValue("我方有责");
                                break;
                            case 4:
                                row.createCell(7).setCellValue("我方无责");
                                break;
                            default:
                                break;
                        }
                    }

                    row.createCell(8).setCellValue(settlementReportBO.getRepairDepotName());
                    row.createCell(9).setCellValue(settlementReportBO.getRepairDepotId());
                    row.createCell(10).setCellValue(settlementReportBO.getVehicleReciveTime());
                    row.createCell(11).setCellValue(settlementReportBO.getVehicleRepairFinishTime());
                    row.createCell(12).setCellValue(settlementReportBO.getPartName());
                    if (StringUtils.isNotBlank(settlementReportBO.getPartTotalMoney())) {
                        row.createCell(13).setCellValue(Double.parseDouble(settlementReportBO.getPartTotalMoney()));
                    } else {
                        row.createCell(13).setCellValue(settlementReportBO.getPartTotalMoney());
                    }
                    row.createCell(14).setCellValue(settlementReportBO.getRepairName());
                    if (StringUtils.isNotBlank(settlementReportBO.getRepairTotalMoney())) {
                        row.createCell(15).setCellValue(Double.parseDouble(settlementReportBO.getRepairTotalMoney()));
                    } else {
                        row.createCell(15).setCellValue(settlementReportBO.getRepairTotalMoney());
                    }
                    if (StringUtils.isNotBlank(settlementReportBO.getOtherMoney())) {
                        row.createCell(16).setCellValue(Double.parseDouble(settlementReportBO.getOtherMoney()));
                    } else {
                        row.createCell(16).setCellValue(settlementReportBO.getOtherMoney());
                    }
                    if (StringUtils.isNotBlank(settlementReportBO.getTotalMoney())) {
                        row.createCell(17).setCellValue(Double.parseDouble(settlementReportBO.getTotalMoney()));
                    } else {
                        row.createCell(17).setCellValue(settlementReportBO.getTotalMoney());
                    }
                    row.createCell(18).setCellValue(settlementReportBO.getEstimatedClaimAmount().toString());
                    row.createCell(19).setCellValue(settlementReportBO.getUserAssumedAmount().toString());
                    row.createCell(20).setCellValue(settlementReportBO.getSelfFundedAmount().toString());
                    row.createCell(21).setCellValue(settlementReportBO.getRepairSettleAmount().toString());
                    row.createCell(22).setCellValue(settlementReportBO.getRepairFaxSettleAmount().toString());
                    if (StringUtils.isNotBlank(settlementReportBO.getVehicleScrapeValue())) {
                        double vehicleScrapeValue = Double.parseDouble(settlementReportBO.getVehicleScrapeValue());
                        row.createCell(23).setCellValue(vehicleScrapeValue == 0 ? "" : settlementReportBO.getVehicleScrapeValue());
                    } else {
                        row.createCell(23).setCellValue(settlementReportBO.getVehicleScrapeValue());
                    }
                    row.createCell(24).setCellValue(settlementReportBO.getTaskNo());
                    row.createCell(25).setCellValue(settlementReportBO.getTaskInflowTime());
                    row.createCell(26).setCellValue(settlementReportBO.getVerificationLossCheckTime());
                    row.createCell(27).setCellValue(settlementReportBO.getVehicleCheckFinishTime());
                    row.createCell(28).setCellValue(settlementReportBO.getRepairGrade());
                    row.createCell(29).setCellValue(settlementReportBO.getOrgName());
                    row.createCell(30).setCellValue(settlementReportBO.getOperateOrgName());
                    row.createCell(31).setCellValue(ComUtil.getRenttypeStr(settlementReportBO.getRenttype()));
                    row.createCell(32).setCellValue(ComUtil.getFaceOperateTagStr(settlementReportBO.getFactOperateTag()));
                    if (StringUtils.isNotBlank(settlementReportBO.getTotalMileage())) {
                        row.createCell(33).setCellValue(Double.parseDouble(settlementReportBO.getTotalMileage()));
                    } else {
                        row.createCell(33).setCellValue(settlementReportBO.getTotalMileage());
                    }
                    if (StringUtils.isNotBlank(settlementReportBO.getManualNo())) {
                        row.createCell(34).setCellValue(settlementReportBO.getManualNo());
                    }
                    if (null != settlementReportBO.getConfirmCarDamageType()) {
                        row.createCell(35).setCellValue(ComUtil.confirmCarDamageTypeFormat(settlementReportBO.getConfirmCarDamageType()));
                    }
                    int reviewToSelFeeFlagValue = settlementReportBO.getReviewToSelFeeFlag();
                    String repairTypeIdValue = settlementReportBO.getRepairTypeId();
                    if (reviewToSelFeeFlagValue == 0 && "1".equals(repairTypeIdValue)){
                        BigDecimal maxVehicleInsuranceTotalAmount = new BigDecimal(settlementReportBO.getRepairReviewTotalAmount()).multiply(BigDecimal.valueOf(1.3));
                        if (maxVehicleInsuranceTotalAmount.compareTo(new BigDecimal(settlementReportBO.getVehicleInsuranceTotalAmount())) < 0) {
                            row.createCell(36).setCellValue("是");
                        } else {
                            row.createCell(36).setCellValue("否");
                        }
                    } else {
                        row.createCell(36).setCellValue("否");
                    }
                    if (StringUtils.isNotBlank(settlementReportBO.getAssociatedOrder())) {
                        row.createCell(37).setCellValue(settlementReportBO.getAssociatedOrder());
                    }
                    if (StringUtils.isNotBlank(settlementReportBO.getSettlementNo())) {
                        row.createCell(38).setCellValue(settlementReportBO.getSettlementNo());
                    }
                    if (StringUtils.isNotBlank(settlementReportBO.getContinueDays())) {
                        row.createCell(39).setCellValue(settlementReportBO.getContinueDays());
                    }
                    rowIndex = rowIndex + 1;
                }

                queryIndex = settlementReportBOList.get(settlementReportBOList.size() - 1).getTaskNo();
                settlementReportInfoQueryBO.setQueryIndex(queryIndex);
                settlementReportBOList = repairTaskMapper.getSettlementReportInfoForExcel(settlementReportInfoQueryBO);
            }

            FileOutputStream fileOutputStream = new FileOutputStream(filePath);

            try {
                workbook.write(fileOutputStream);
                fileOutputStream.flush();// 缓存清空输出
            } catch (FileNotFoundException e1) {
                log.error(ComUtil.getExceptionMsg(e1));
            } catch (IOException e) {
                log.error(ComUtil.getExceptionMsg(e));
            } finally {
                if (workbook != null) {
                    try {
                        workbook.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                }
                if (fileOutputStream != null) {
                    try {
                        fileOutputStream.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                }
            }

            updateMtcFileInfo(comModel, 2, fileName, insertMtcFileInfo.getId());

            return vo;
        } catch (Exception e) {
            log.error("exportSettlementReportInfo", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
            return vo;
        }
    }

    @Override
    public DefaultServiceRespDTO operateExportSubSettlementReportInfo(String orgId, Integer repairTypeId, String taskNo,
                                                                      String vehicleNo, String vehicleModelSeq, String taskInflowStartTime, String taskInflowEndTime,
                                                                      String vehicleReciveStartTime, String vehicleReciveEndTime, String vehicleRepairStartTime,
                                                                      String vehicleRepairEndTime, String vehicleCheckStartTime, String vehicleCheckEndTime, String repairDepotId,
                                                                      Integer renttype, List<Integer> renttypeList, Integer subType, String projectType, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);
        try {
            ComModel comModel = ComUtil.getUserInfo(request);
            SettlementReportInfoQueryBO settlementReportInfoQueryBO = getSettlementReportInfoQueryBO(orgId, taskNo,
                    repairTypeId, renttype, vehicleNo, vehicleModelSeq, taskInflowStartTime, taskInflowEndTime,
                    vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime,
                    vehicleCheckStartTime, vehicleCheckEndTime,null, null,
                    repairDepotId, null, subType, null, null, null, projectType, comModel);

            String dateStr = new SimpleDateFormat("yyyyMMddHHmmss").format(System.currentTimeMillis());
            String fileName = Contants.SUB_SETTLEMENT_REPORT_NAME + dateStr + Contants.XLSX;
            String filePath = requestFileUrl + fileName;

            MtcFileInfo insertMtcFileInfo = mtcFileInfoServiceImpl.saveMtcFileInfo(comModel, 1, fileName, 1);

            Workbook workbook = new SXSSFWorkbook(100);
            Sheet sheet = workbook.createSheet();

            Cell cell;

            String[] headers = apolloPropertyUtils.getString("export.SubSettlementReport.title")
                    .split(Contants.COMMA_SIGN_SPLIT_NAME);

            int columnNum = headers.length;
            Row row = sheet.createRow(0);
            for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
                cell = row.createCell(columnIndex);
                cell.setCellValue(headers[columnIndex]);
            }

            Integer pageSize = 5000;
            String queryIndex;
            int rowIndex = 1;
            settlementReportInfoQueryBO.setPageSize(pageSize);
            List<SettlementReportBO> settlementReportBOList = repairTaskMapper
                    .getSettlementReportInfoForExcel(settlementReportInfoQueryBO);
            while (settlementReportBOList != null && settlementReportBOList.size() > 0) {

                settlementReportInfoQueryBO.setList(settlementReportBOList);
                List<SubSettlementReportBO> resultList = repairTaskMapper
                        .getSubSettlementReportInfoForExcel(settlementReportInfoQueryBO);

                for (SubSettlementReportBO subSettlementReportBO : resultList) {
                    row = sheet.createRow(rowIndex);
                    row.createCell(0).setCellValue(subSettlementReportBO.getSubType());
                    row.createCell(1).setCellValue(subSettlementReportBO.getProjectType());
                    row.createCell(2).setCellValue(subSettlementReportBO.getPriceOrigin());
                    row.createCell(3).setCellValue(subSettlementReportBO.getWorkType());
                    if (StringUtils.isNotBlank(subSettlementReportBO.getRefFee())) {
                        row.createCell(4).setCellValue(Double.parseDouble(subSettlementReportBO.getRefFee()));
                    } else {
                        row.createCell(4).setCellValue(subSettlementReportBO.getRefFee());
                    }
                    if (StringUtils.isNotBlank(subSettlementReportBO.getLossFee())) {
                        row.createCell(5).setCellValue(Double.parseDouble(subSettlementReportBO.getLossFee()));
                    } else {
                        row.createCell(5).setCellValue(subSettlementReportBO.getLossFee());
                    }
                    if (StringUtils.isNotBlank(subSettlementReportBO.getPartNum())) {
                        row.createCell(6).setCellValue(Double.parseDouble(subSettlementReportBO.getPartNum()));
                    } else {
                        row.createCell(6).setCellValue(subSettlementReportBO.getPartNum());
                    }
                    if (StringUtils.isNotBlank(subSettlementReportBO.getResidualValue())) {
                        row.createCell(7).setCellValue(Double.parseDouble(subSettlementReportBO.getResidualValue()));
                    } else {
                        row.createCell(7).setCellValue(subSettlementReportBO.getResidualValue());
                    }
                    row.createCell(8).setCellValue(subSettlementReportBO.getTaskNo());
                    row.createCell(9).setCellValue(subSettlementReportBO.getVehicleNo());
                    row.createCell(10).setCellValue(subSettlementReportBO.getRentType());
                    row.createCell(11).setCellValue(subSettlementReportBO.getVehicleModelName());
                    row.createCell(12).setCellValue(subSettlementReportBO.getVin());
                    row.createCell(13).setCellValue(subSettlementReportBO.getRepairTypeName());
                    row.createCell(14).setCellValue(subSettlementReportBO.getRepairDepotName());
                    row.createCell(15).setCellValue(subSettlementReportBO.getVehicleReceiveTime());
                    row.createCell(16).setCellValue(subSettlementReportBO.getVehicleRepairFinishTime());
                    row.createCell(17).setCellValue(subSettlementReportBO.getTaskInflowTime());
                    row.createCell(18).setCellValue(subSettlementReportBO.getVehicleCheckFinishTime());
                    row.createCell(19).setCellValue(subSettlementReportBO.getTaskGrade());
                    row.createCell(20).setCellValue(subSettlementReportBO.getOperationOrgName());
                    rowIndex = rowIndex + 1;
                }

                queryIndex = settlementReportBOList.get(settlementReportBOList.size() - 1).getTaskNo();
                settlementReportInfoQueryBO.setQueryIndex(queryIndex);
                settlementReportBOList = repairTaskMapper.getSettlementReportInfoForExcel(settlementReportInfoQueryBO);
            }

            FileOutputStream fileOutputStream = new FileOutputStream(filePath);

            try {
                workbook.write(fileOutputStream);
                fileOutputStream.flush();// 缓存清空输出
            } catch (FileNotFoundException e1) {
                log.error(ComUtil.getExceptionMsg(e1));
            } catch (IOException e) {
                log.error(ComUtil.getExceptionMsg(e));
            } finally {
                if (workbook != null) {
                    try {
                        workbook.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                }
                if (fileOutputStream != null) {
                    try {
                        fileOutputStream.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                }
            }

            updateMtcFileInfo(comModel, 2, fileName, insertMtcFileInfo.getId());

            return vo;
        } catch (Exception e) {
            log.error("exportSubSettlementReportInfo", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
            return vo;
        }
    }

    /**
     * 超时任务统计一览
     *
     * @param pageNum                页码
     * @param pageSize               每页显示条数
     * @param orgId                  车辆运营单位
     * @param repairTypeId           修理类型
     * @param repairGrade            修理级别
     * @param vehicleNo              车牌号
     * @param repairDepotName        修理厂名称
     * @param vehicleReciveStartTime 车辆接收开始时间
     * @param vehicleReciveEndTime   车辆接收结束时间
     * @param vehicleRepairStartTime 实际修理完成开始时间
     * @param vehicleRepairEndTime   实际修理完成结束时间
     * @param renttype               业务状态
     * @return
     */
    @Override
    public PageInfo<TimeoutTaskStatisticsBO> getTimeoutTaskList(Integer pageNum, Integer pageSize, String orgId,
                                                                Integer repairTypeId, String repairGrade, String vehicleNo, String repairDepotName,
                                                                String vehicleReciveStartTime, String vehicleReciveEndTime, String vehicleRepairStartTime,
                                                                String vehicleRepairEndTime, Integer renttype, List<Integer> renttypeList, Integer factOperateTag,
                                                                HttpServletRequest request) {
        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }
        if (StringUtils.isNotBlank(vehicleNo)) {
            vehicleNo = vehicleNo.trim();
        }
        if (StringUtils.isNotBlank(repairDepotName)) {
            repairDepotName = repairDepotName.trim();
        }
        PageHelper.startPage(pageNum, pageSize, false);
        List<TimeoutTaskStatisticsBO> list = repairTaskMapper.getTimeoutTaskList(orgId, repairTypeId, repairGrade,
                vehicleNo, repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime,
                vehicleRepairEndTime, renttype, renttypeList, factOperateTag);
        Integer total = repairTaskMapper.getTimeoutTaskNum(orgId, repairTypeId, repairGrade, vehicleNo, repairDepotName,
                vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime, renttype,
                renttypeList, factOperateTag);
        PageInfo<TimeoutTaskStatisticsBO> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(total);
        return pageInfo;
    }

    @Override
    public DefaultServiceRespDTO exportTimeoutTask(String orgId, Integer repairTypeId, String repairGrade,
                                                   String vehicleNo, String repairDepotName, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                   String vehicleRepairStartTime, String vehicleRepairEndTime, Integer renttype, List<Integer> renttypeList,
                                                   Integer factOperateTag,
                                                   HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        try {
            /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
            if (StringUtils.isBlank(orgId)) {
                ComModel comModel = ComUtil.getUserInfo(request);
                orgId = comModel.getOrgId();
            }
            if (StringUtils.isNotBlank(vehicleNo)) {
                vehicleNo = vehicleNo.trim();
            }
            if (StringUtils.isNotBlank(repairDepotName)) {
                repairDepotName = repairDepotName.trim();
            }

            int pageSize = 10000;
            // 查询数据条数
            int count = repairTaskMapper.getTimeoutTaskNum(orgId, repairTypeId, repairGrade, vehicleNo, repairDepotName,
                    vehicleReciveStartTime, vehicleReciveEndTime, vehicleRepairStartTime, vehicleRepairEndTime,
                    renttype, renttypeList, factOperateTag);
            // 总页数
            int totalPageNum = (count + pageSize - 1) / pageSize;
            // 声明一个工作薄
            Workbook workbook = new SXSSFWorkbook(100);

            // 生成一个表格
            Sheet sheet = workbook.createSheet();

            String[] headers = apolloPropertyUtils.getString("export.TimeoutTask.title")
                    .split(Contants.COMMA_SIGN_SPLIT_NAME);

            int columnNum = headers.length;
            // 产生表格标题行
            Row row = sheet.createRow(0);
            for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
                Cell cell = row.createCell(columnIndex);
                cell.setCellValue(headers[columnIndex]);
            }

            for (int i = 0; i < totalPageNum; i++) {
                PageHelper.startPage(i + 1, pageSize);
                List<TimeoutTaskStatisticsBO> list = repairTaskMapper.getTimeoutTaskList(orgId, repairTypeId,
                        repairGrade, vehicleNo, repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime,
                        vehicleRepairStartTime, vehicleRepairEndTime, renttype, renttypeList, factOperateTag);
                for (int rowIndex = 0; rowIndex < list.size(); rowIndex++) {
                    TimeoutTaskStatisticsBO timeoutTaskStatisticsBO = list.get(rowIndex);
                    row = sheet.createRow(i * pageSize + rowIndex + 1);
                    // 第一列：车辆运营单位
                    Cell cell0 = row.createCell(0);
                    // 第二列：车牌号
                    Cell cell1 = row.createCell(1);
                    // 第三列：任务编号
                    Cell cell2 = row.createCell(2);
                    // 第四列：修理厂名称
                    Cell cell3 = row.createCell(3);
                    // 第五列：修理类型
                    Cell cell4 = row.createCell(4);
                    // 第六列：修理级别
                    Cell cell5 = row.createCell(5);
                    // 第七列：车型
                    Cell cell6 = row.createCell(6);
                    // 第八列：车架号
                    Cell cell7 = row.createCell(7);
                    // 第九列：车辆接收时间
                    Cell cell8 = row.createCell(8);
                    // 第十列：预计修理完成时间
                    Cell cell9 = row.createCell(9);
                    // 第十一列:实际修理完成时间
                    Cell cell10 = row.createCell(10);
                    // 第十二列：超时原因
                    Cell cell11 = row.createCell(11);
                    // 第十三列：业务状态
                    Cell cell12 = row.createCell(12);

                    /* 往Excel表的该行列里面赋值 */
                    cell0.setCellValue(timeoutTaskStatisticsBO.getOrgName());
                    cell1.setCellValue(timeoutTaskStatisticsBO.getVehicleNo());
                    cell2.setCellValue(timeoutTaskStatisticsBO.getTaskNo());
                    cell3.setCellValue(timeoutTaskStatisticsBO.getRepairDepotName());
                    cell4.setCellValue(timeoutTaskStatisticsBO.getRepairTypeName());
                    cell5.setCellValue(timeoutTaskStatisticsBO.getRepairGrade());
                    cell6.setCellValue(timeoutTaskStatisticsBO.getVehicleModelName());
                    cell7.setCellValue(timeoutTaskStatisticsBO.getVin());
                    cell8.setCellValue(timeoutTaskStatisticsBO.getVehicleReciveTime());
                    cell9.setCellValue(timeoutTaskStatisticsBO.getVehiclePlanFinishTime());
                    cell10.setCellValue(timeoutTaskStatisticsBO.getVehicleRepairTime());
                    cell11.setCellValue(timeoutTaskStatisticsBO.getTimeoutReason());
                    cell12.setCellValue(ComUtil.getRenttypeStr(timeoutTaskStatisticsBO.getRenttype()));
                }
            }

            // String exportDirector =
            // apolloPropertyUtils.getString(Contants.EXPROT_DIRECTORY);
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateStr = dateformat.format(System.currentTimeMillis());
            String execelName = Contants.TIMEOUTTASK_MANAGEMENT_FUNCTION_NAME + dateStr + Contants.XLSX;
            // File file = new File("d:\\" + execelName);
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + execelName);
            OutputStream out = response.getOutputStream();
            // OutputStream out = null;
            try {
                // out = new FileOutputStream(file);
                workbook.write(out);
                out.flush();// 缓存清空输出
            } catch (FileNotFoundException e1) {
                log.error(ComUtil.getExceptionMsg(e1));
            } catch (IOException e) {
                log.error(ComUtil.getExceptionMsg(e));
            } finally {
                if (workbook != null) {
                    try {
                        workbook.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                    workbook = null;
                }
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                    out = null;
                }

            }
            return vo;
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
            return vo;
        }
    }

    /**
     * 报价统计一览
     *
     * @param pageNum                        页码
     * @param pageSize                       每页显示条数
     * @param orgId                          车辆运营单位
     * @param repairGrade                    修理级别
     * @param vehicleNo                      车牌号
     * @param repairDepotName                修理厂名称
     * @param vehicleReciveStartTime         车辆接收开始时间
     * @param vehicleReciveEndTime           车辆接收结束时间
     * @param verificationLossCheckStartTime 核价通过开始时间
     * @param verificationLossCheckEndTime   核价通过结束时间
     * @param renttype                       业务状态
     * @param request
     * @return
     */
    @Override
    public PageInfo<OfferStatisticsBO> getOfferStatisticsList(Integer pageNum, Integer pageSize, String orgId,
                                                              String repairGrade, String vehicleNo, String repairDepotName, String vehicleReciveStartTime,
                                                              String vehicleReciveEndTime, String verificationLossCheckStartTime, String verificationLossCheckEndTime,
                                                              String taskNo, Integer renttype, List<Integer> renttypeList, Integer factOperateTag, HttpServletRequest request) {
        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            ComModel comModel = ComUtil.getUserInfo(request);
            orgId = comModel.getOrgId();
        }
        if (StringUtils.isNotBlank(vehicleNo)) {
            vehicleNo = vehicleNo.trim();
        }
        if (StringUtils.isNotBlank(repairDepotName)) {
            repairDepotName = repairDepotName.trim();
        }
        if (StringUtils.isNotBlank(taskNo)) {
            taskNo = taskNo.trim();
        }
        PageHelper.startPage(pageNum, pageSize, false);
        List<OfferStatisticsBO> list = repairTaskMapper.getOfferStatisticsList(orgId, repairGrade, vehicleNo,
                repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime, verificationLossCheckStartTime,
                verificationLossCheckEndTime, taskNo, renttype, renttypeList, factOperateTag);
        Integer total = repairTaskMapper.getOfferStatisticsNum(orgId, repairGrade, vehicleNo, repairDepotName,
                vehicleReciveStartTime, vehicleReciveEndTime, verificationLossCheckStartTime,
                verificationLossCheckEndTime, renttype, renttypeList, factOperateTag);
        PageInfo<OfferStatisticsBO> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(total);
        return pageInfo;
    }

    @Override
    public DefaultServiceRespDTO exportOfferStatistics(String orgId, String repairGrade, String vehicleNo,
                                                       String repairDepotName, String vehicleReciveStartTime, String vehicleReciveEndTime,
                                                       String verificationLossCheckStartTime, String verificationLossCheckEndTime, String taskNo, Integer renttype,
                                                       List<Integer> renttypeList, Integer factOperateTag, HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        try {
            /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
            if (StringUtils.isBlank(orgId)) {
                ComModel comModel = ComUtil.getUserInfo(request);
                orgId = comModel.getOrgId();
            }
            if (StringUtils.isNotBlank(vehicleNo)) {
                vehicleNo = vehicleNo.trim();
            }
            if (StringUtils.isNotBlank(repairDepotName)) {
                repairDepotName = repairDepotName.trim();
            }

            int pageSize = 10000;
            // 查询数据条数
            int count = repairTaskMapper.getOfferStatisticsNum(orgId, repairGrade, vehicleNo, repairDepotName,
                    vehicleReciveStartTime, vehicleReciveEndTime, verificationLossCheckStartTime,
                    verificationLossCheckEndTime, renttype, renttypeList, factOperateTag);
            // 总页数
            int totalPageNum = (count + pageSize - 1) / pageSize;
            // 声明一个工作薄
            Workbook workbook = new SXSSFWorkbook(100);

            // 生成一个表格
            Sheet sheet = workbook.createSheet();

            String[] headers = apolloPropertyUtils.getString("export.OfferStatistics.title")
                    .split(Contants.COMMA_SIGN_SPLIT_NAME);

            int columnNum = headers.length;
            // 产生表格标题行
            Row row = sheet.createRow(0);
            for (int columnIndex = 0; columnIndex < columnNum; columnIndex++) {
                Cell cell = row.createCell(columnIndex);
                cell.setCellValue(headers[columnIndex]);
            }

            for (int i = 0; i < totalPageNum; i++) {
                PageHelper.startPage(i + 1, pageSize);
                List<OfferStatisticsBO> list = repairTaskMapper.getOfferStatisticsList(orgId, repairGrade, vehicleNo,
                        repairDepotName, vehicleReciveStartTime, vehicleReciveEndTime, verificationLossCheckStartTime,
                        verificationLossCheckEndTime, taskNo, renttype, renttypeList, factOperateTag);
                for (int rowIndex = 0; rowIndex < list.size(); rowIndex++) {
                    OfferStatisticsBO offerStatisticsBO = list.get(rowIndex);
                    row = sheet.createRow(i * pageSize + rowIndex + 1);
                    // 第一列：车辆运营单位
                    Cell cell0 = row.createCell(0);
                    // 第二列：车牌号
                    Cell cell1 = row.createCell(1);
                    // 第三列：任务编号
                    Cell cell2 = row.createCell(2);
                    // 第四列：修理厂名称
                    Cell cell3 = row.createCell(3);
                    // 第五列：一级业务处理人
                    Cell cell4 = row.createCell(4);
                    // 第六列：修理类型
                    Cell cell5 = row.createCell(5);
                    // 第七列：修理级别
                    Cell cell6 = row.createCell(6);
                    // 第八列：车型
                    Cell cell7 = row.createCell(7);
                    // 第九列：车架号
                    Cell cell8 = row.createCell(8);
                    // 第十列：首次报价金额（元）
                    Cell cell9 = row.createCell(9);
                    // 第十一列：核价通过金额（元）
                    Cell cell10 = row.createCell(10);
                    // 第十二列：车辆接收时间
                    Cell cell11 = row.createCell(11);
                    // 第十三列：核价通过时间
                    Cell cell12 = row.createCell(12);
                    // 第十四列：业务状态
                    Cell cell13 = row.createCell(13);

                    /* 往Excel表的该行列里面赋值 */
                    cell0.setCellValue(offerStatisticsBO.getOrgName());
                    cell1.setCellValue(offerStatisticsBO.getVehicleNo());
                    cell2.setCellValue(offerStatisticsBO.getTaskNo());
                    cell3.setCellValue(offerStatisticsBO.getRepairDepotName());
                    cell4.setCellValue(offerStatisticsBO.getActorUser());
                    cell5.setCellValue(offerStatisticsBO.getRepairTypeName());
                    cell6.setCellValue(offerStatisticsBO.getRepairGrade());
                    cell7.setCellValue(offerStatisticsBO.getVehicleModelName());
                    cell8.setCellValue(offerStatisticsBO.getVin());
                    cell9.setCellValue(offerStatisticsBO.getBidAmount());
                    cell10.setCellValue(offerStatisticsBO.getPassedAmount());
                    cell11.setCellValue(offerStatisticsBO.getVehicleReciveTime());
                    cell12.setCellValue(offerStatisticsBO.getPricingPasseTime());
                    cell13.setCellValue(ComUtil.getRenttypeStr(offerStatisticsBO.getRenttype()));
                }
            }

            // String exportDirector =
            // apolloPropertyUtils.getString(Contants.EXPROT_DIRECTORY);
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateStr = dateformat.format(System.currentTimeMillis());
            String execelName = Contants.OFFERSTATISTICS_MANAGEMENT_FUNCTION_NAME + dateStr + Contants.XLSX;
            // File file = new File("d:\\" + execelName);
            response.setHeader("Content-Type", "application/vnd.ms-excel");
            response.setHeader("content-disposition", "attachment;filename=" + execelName);
            OutputStream out = response.getOutputStream();
            // OutputStream out = null;
            try {
                // out = new FileOutputStream(file);
                workbook.write(out);
                out.flush();// 缓存清空输出
            } catch (FileNotFoundException e1) {
                log.error(ComUtil.getExceptionMsg(e1));
            } catch (IOException e) {
                log.error(ComUtil.getExceptionMsg(e));
            } finally {
                if (workbook != null) {
                    try {
                        workbook.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                    workbook = null;
                }
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        log.error(ComUtil.getExceptionMsg(e));
                    }
                    out = null;
                }

            }
            return vo;
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.toString());
            return vo;
        }
    }

    private SettlementReportInfoQueryBO getSettlementReportInfoQueryBO(String orgId, String taskNo,
                                                                       Integer repairTypeId, Integer renttype, String vehicleNo, String vehicleModelSeq,
                                                                       String taskInflowStartTime, String taskInflowEndTime, String vehicleReciveStartTime,
                                                                       String vehicleReciveEndTime, String vehicleRepairStartTime, String vehicleRepairEndTime,
                                                                       String vehicleCheckStartTime, String vehicleCheckEndTime, 
                                                                       String verificationLossCheckStartTime, String verificationLossCheckEndTime,
                                                                       String repairDepotId, Integer noDeductiblesFlag,
                                                                       Integer subType, Integer factOperateTag,
                                                                       String accidentNo, Integer reviewToSelFeeFlag, String projectType, ComModel comModel) {
        SettlementReportInfoQueryBO settlementReportInfoQueryBO = new SettlementReportInfoQueryBO();

        /* 页面传过来的分子公司id为空时，将当前登录人id设置进去 */
        if (StringUtils.isBlank(orgId)) {
            orgId = comModel.getOrgId();
            settlementReportInfoQueryBO.setOrgId(orgId);
        } else {
            settlementReportInfoQueryBO.setOrgId(orgId);
        }
        if (StringUtils.isNotBlank(taskNo)) {
            taskNo = taskNo.trim();
            settlementReportInfoQueryBO.setTaskNo(taskNo);
        }
        if (repairTypeId != null) {
            settlementReportInfoQueryBO.setRepairTypeId(repairTypeId);
        }
        if (renttype != null) {
            settlementReportInfoQueryBO.setRenttype(renttype);
        }
        if (StringUtils.isNotBlank(vehicleNo)) {
            vehicleNo = vehicleNo.trim();
            settlementReportInfoQueryBO.setVehicleNo(vehicleNo);
        }
        if (StringUtils.isNotBlank(vehicleModelSeq)) {
            vehicleModelSeq = vehicleModelSeq.trim();
            settlementReportInfoQueryBO.setVehicleModelSeq(vehicleModelSeq);
        }
        if (StringUtils.isNotBlank(taskInflowStartTime)) {
            taskInflowStartTime += " 00:00:00";
            settlementReportInfoQueryBO.setTaskInflowStartTime(taskInflowStartTime);
        }
        if (StringUtils.isNotBlank(taskInflowEndTime)) {
            taskInflowEndTime += " 23:59:59";
            settlementReportInfoQueryBO.setTaskInflowEndTime(taskInflowEndTime);
        }
        if (StringUtils.isNotBlank(vehicleReciveStartTime)) {
            vehicleReciveStartTime += " 00:00:00";
            settlementReportInfoQueryBO.setVehicleReciveStartTime(vehicleReciveStartTime);
        }
        if (StringUtils.isNotBlank(vehicleReciveEndTime)) {
            vehicleReciveEndTime += " 23:59:59";
            settlementReportInfoQueryBO.setVehicleReciveEndTime(vehicleReciveEndTime);
        }
        if (StringUtils.isNotBlank(vehicleRepairStartTime)) {
            vehicleRepairStartTime += " 00:00:00";
            settlementReportInfoQueryBO.setVehicleRepairStartTime(vehicleRepairStartTime);
        }
        if (StringUtils.isNotBlank(vehicleRepairEndTime)) {
            vehicleRepairEndTime += " 23:59:59";
            settlementReportInfoQueryBO.setVehicleRepairEndTime(vehicleRepairEndTime);
        }
        if (StringUtils.isNotBlank(vehicleCheckStartTime)) {
            vehicleCheckStartTime += " 00:00:00";
            settlementReportInfoQueryBO.setVehicleCheckStartTime(vehicleCheckStartTime);
        }
        if (StringUtils.isNotBlank(vehicleCheckEndTime)) {
            vehicleCheckEndTime += " 23:59:59";
            settlementReportInfoQueryBO.setVehicleCheckEndTime(vehicleCheckEndTime);
        }
        // 增加核损通过时间条件
        if (StringUtils.isNotBlank(verificationLossCheckStartTime)) {
            verificationLossCheckStartTime += " 00:00:00";
            settlementReportInfoQueryBO.setVerificationLossCheckStartTime(verificationLossCheckStartTime);
        }
        if (StringUtils.isNotBlank(verificationLossCheckEndTime)) {
            verificationLossCheckEndTime += " 23:59:59";
            settlementReportInfoQueryBO.setVerificationLossCheckEndTime(verificationLossCheckEndTime);
        }
        List<String> repairDepotIds = new ArrayList<>();
        if (StringUtils.isBlank(repairDepotId)) {
            repairDepotIds = repairDepotInfoMapper.getRepairDepotIds(orgId);
            if (CollectionUtils.isEmpty(repairDepotIds)) {
                repairDepotIds.add(StringUtils.EMPTY);
            }
        } else {
            String[] ids = repairDepotId.split(",");
            repairDepotIds.addAll(Arrays.asList(ids));
        }
        settlementReportInfoQueryBO.setRepairDepotIds(repairDepotIds);
        if (noDeductiblesFlag != null) {
            settlementReportInfoQueryBO.setNoDeductiblesFlag(noDeductiblesFlag);
        }
        if (subType != null) {
            settlementReportInfoQueryBO.setSubType(subType);
        }
        if (factOperateTag != null) {
            settlementReportInfoQueryBO.setFactOperateTag(factOperateTag);
        }
        if (StringUtils.isNotBlank(accidentNo)) {
            settlementReportInfoQueryBO.setAccidentNo(accidentNo);
        }
        if (null != reviewToSelFeeFlag) {
            settlementReportInfoQueryBO.setReviewToSelFeeFlag(reviewToSelFeeFlag);
        }
        if (StringUtils.isNotBlank(projectType)) {
            settlementReportInfoQueryBO.setProjectType(projectType);
        }
        return settlementReportInfoQueryBO;
    }

    private void updateMtcFileInfo(ComModel comModel, Integer fileStatus, String fileName, Long id) {
        MtcFileInfo updateMtcFileInfo = new MtcFileInfo();
        updateMtcFileInfo.setId(id);
        updateMtcFileInfo.setFileStatus(fileStatus);
        updateMtcFileInfo.setFilePath(downloadUrl + fileName);
        updateMtcFileInfo.setUpdateOperId(comModel.getUpdateOperId());
        updateMtcFileInfo.setUpdateTime(new Date());
        updateMtcFileInfo.setUpdateOperName(comModel.getUserName());
        mtcFileInfoMapper.updateByPrimaryKeySelective(updateMtcFileInfo);
    }

}
