package com.extracme.evcard.mtc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportExcelRepairTypeEnum {
    /**
     * 修理类型
     */
    INSURED_MAINTENANCE_NO_SELF(1, "事故维修-非转自费"),

    SELF_MAINTENANCE(2, "自费维修"),

    VEHICLE_CARE(3, "车辆保养"),
    
    ROUTINE_MAINTENANCE(6, "常规保养"),

    TERMINAL_REPAIR(7, "终端维修"),

    INSURED_MAINTENANCE_SELF(8, "事故维修-转自费"),

    SHORT_RENTAL_WARRANTY(9, "短租包修（外观类）"),

    SHORT_RENTAL_WARRANTY_NOMAL(10, "短租包修（一搬类）");

    /**
     * 修理类型ID
     */
    private final Integer typeId;

    /**
     * 修理类型名称
     */
    private final String typeName;
}
