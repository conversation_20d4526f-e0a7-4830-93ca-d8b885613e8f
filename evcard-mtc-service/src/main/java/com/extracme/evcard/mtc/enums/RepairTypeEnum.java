package com.extracme.evcard.mtc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 维修类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RepairTypeEnum {

    /**
     * 修理类型
     */
    SELF_BUILD_TASK(0, "自建任务", "1", "车辆维修"),

    INSURED_MAINTENANCE(1, "事故维修", "1", "车辆维修"),

    SELF_MAINTENANCE(2, "自费维修", "1", "车辆维修"),

    VEHICLE_CARE(3, "车辆保养", "2", "车辆保养"),

    TIRE_TASK(4, "轮胎任务", "1", "车辆维修"),

    SELF_MAINTENANCE_OLD(5, "自费维修（原车辆保养）", "1", "车辆维修"),

    ROUTINE_MAINTENANCE(6, "常规保养", "2", "车辆保养"),

    TERMINAL_REPAIR(7, "终端维修", "1", "车辆维修"),

    SHORT_RENTAL_WARRANTY(9, "短租包修（外观类）", "1", "车辆维修"),

    SHORT_RENTAL_WARRANTY_NOMAL(10, "短租包修（一搬类）", "1", "车辆维修");

    /**
     * 修理类型ID
     */
    private final Integer typeId;

    /**
     * 修理类型名称
     */
    private final String typeName;

    /**
     * 费用类型Code
     */
    private final String feeTypeCode;

    /**
     * 费用类型
     */
    private final String feeType;

    /**
     * 根据修理类型对应中文描述
     * @param code 修理类型ID
     * @return 修理类型名称
     */
    public static String getEnumDesc(Integer code){
        for (RepairTypeEnum item : RepairTypeEnum.values()) {
            if (item.getTypeId().equals(code)) {
                return item.getTypeName();
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据修理类型对应中文描述
     *
     * @param code 修理类型ID
     * @return 修理类型名称
     */
    public static String getFeeTypeCode(Integer code) {
        for (RepairTypeEnum item : RepairTypeEnum.values()) {
            if (item.getTypeId().equals(code)) {
                return item.getFeeTypeCode();
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据修理类型对应中文描述
     *
     * @param code 修理类型ID
     * @return 修理类型名称
     */
    public static String getFeeType(Integer code) {
        for (RepairTypeEnum item : RepairTypeEnum.values()) {
            if (item.getTypeId().equals(code)) {
                return item.getFeeType();
            }
        }
        return StringUtils.EMPTY;
    }
}
