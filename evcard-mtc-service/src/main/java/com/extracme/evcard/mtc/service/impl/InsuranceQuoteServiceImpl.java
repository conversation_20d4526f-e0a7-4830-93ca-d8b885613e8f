package com.extracme.evcard.mtc.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.bdp.dto.input.UpdateAccidentReportNumberInput;
import com.extracme.evcard.bdp.service.IBdpMtcTaskInfoService;
import com.extracme.evcard.mtc.bean.*;
import com.extracme.evcard.mtc.bo.*;
import com.extracme.evcard.mtc.common.*;
import com.extracme.evcard.mtc.configuration.MtcSystemConfig;
import com.extracme.evcard.mtc.dao.*;
import com.extracme.evcard.mtc.dto.*;
import com.extracme.evcard.mtc.dto.order.AbstractOrderInfoDTO;
import com.extracme.evcard.mtc.dto.order.LongRentOrderInfoDTO;
import com.extracme.evcard.mtc.enums.CurrentTacheEnum;
import com.extracme.evcard.mtc.listener.event.BaseRepairEvent;
import com.extracme.evcard.mtc.listener.subject.InsuranceQuotaSubject;
import com.extracme.evcard.mtc.model.*;
import com.extracme.evcard.mtc.service.*;
import com.extracme.evcard.mtc.util.ApolloPropertyUtils;
import com.extracme.evcard.mtc.util.BeanUtilsConvert;
import com.extracme.evcard.mtc.util.HttpClientUtils;
import com.extracme.evcard.rpc.rtms.service.ITboxControl;
import com.extracme.evcard.rpc.vehicle.dto.VehicleStaticInfo;
import com.extracme.evcard.rpc.vehicle.service.IVehicleService;
import com.extracme.evcard.sso.dto.SsoUserBaseInfoDto;
import com.extracme.evcard.sso.service.SsoUserService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXB;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.extracme.evcard.mtc.common.ComUtil.REMARK_REMARK_REDIS_KEY;

@Service
public class InsuranceQuoteServiceImpl implements InsuranceQuoteService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ApolloPropertyUtils apolloPropertyUtils;
    @Resource
    private JedisUtils jedisUtils;

    @Autowired
    private MtcSystemConfig mtcSystemConfig;

    @Resource
    private OperatorLogService operatorLogServiceImpl;
    @Resource
    private InsuranceQuoteMapper insuranceQuoteMapper;
    @Resource
    private ReplaceItemDetailMapper replaceItemDetailMapper;
    @Resource
    private RepairItemDetailMapper repairItemDetailMapper;
    @Resource
    private MarstCodeInfoMapper marstCodeInfoMapper;
    @Resource
    private RepairRemarkMapper repairRemarkMapper;
    @Resource
    private RepairTaskMapper repairTaskMapper;
    @Resource
    private VehicleRepairPicMapper vehicleRepairPicMapper;
    @Resource
    RepairDepotInfoMapper repairDepotInfoMapper;
    @Resource

    MtcOperatorLogMapper mtcOperatorLogMapper;

    @Autowired
    private CarDamageRequestMapper carDamageRequestMapper;
    @Autowired
    private OrgInfoMapper orgInfoMapper;
    @Autowired
    private MtcLossInfoMapper mtcLossInfoMapper;
    @Autowired
    private MtcCollisionPartsMapper mtcCollisionPartsMapper;
    @Autowired
    private MtcLossFitInfoMapper mtcLossFitInfoMapper;
    @Autowired
    private MtcLossRepairInfoMapper mtcLossRepairInfoMapper;
    @Autowired
    private MtcLossOuterRepairInfoMapper mtcLossOuterRepairInfoMapper;
    @Autowired
    private MtcLossRepairSumInfoMapper mtcLossRepairSumInfoMapper;
    @Autowired
    private MtcLossAssistInfoMapper mtcLossAssistInfoMapper;
    @Autowired
    private ITboxControl tboxControlService;
    @Autowired
    private IVehicleService vehicleService;
    @Resource
    private VehicleRepairMapper vehicleRepairMapper;
    @Resource
    private MtcBudgetManagementMapper mtcBudgetManagementMapper;
    @Resource
    private IMtcRepairItemCheckInfoService mtcRepairItemCheckInfoService;

    @Autowired
    private InsuranceQuotaSubject insuranceQuotaSubject;
    @Resource
    private VehicleMaintenanceLossAuditPriceService vehicleMaintenanceLossAuditPriceService;

    @Resource
    private IBdpMtcTaskInfoService bdpMtcTaskInfoService;
    @Autowired
    private ManualUtils manualUtils;
    @Resource
    private RepairTaskService repairTaskService;
    @Resource
    private SsoUserService ssoUserService;

    @Resource
    private NewAndOldOrderConversionService newAndOldOrderConversionService;

    /**
     * 维修报价查询按钮
     *
     * @param insuranceQuoteBO
     * @param request
     * @return
     */
    @Override
    public InsuranceQuoteAllResultBO selectInsuranceTask(InsuranceQuoteBO insuranceQuoteBO,
                                                         HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        insuranceQuoteBO.setRepairDepotOrgId(comModel.getOrgId());
        RepairDepotDetailBO repairDepotDetailBO = repairDepotInfoMapper.selectBySSO(comModel.getCreateOperId());

        if (repairDepotDetailBO != null) {
            insuranceQuoteBO.setRepairDepotId(repairDepotDetailBO.getRepairDepotId());
        }else if (StringUtils.isBlank(insuranceQuoteBO.getOrgId())) {
            // 检索条件下拉框的分子公司id
            insuranceQuoteBO.setOrgId(comModel.getOrgId());
        }
        if ("240".equals(insuranceQuoteBO.getInsuranceQuoteTaskSchedule())) {
            insuranceQuoteBO.setCurrentTache(20L);
        }

        PageHelper.startPage(insuranceQuoteBO.getPageNum(), insuranceQuoteBO.getPageSize());
        List<InsuranceQuoteResultBO> modelList = insuranceQuoteMapper.selectInsuranceTask(insuranceQuoteBO);

        List<InsuranceQuoteCountResultBO> countList = new ArrayList<>();
        InsuranceQuoteCountBO insuranceQuoteCountBO = insuranceQuoteMapper.selectInsuranceTaskCount(insuranceQuoteBO);
        InsuranceQuoteCountResultBO insuranceQuoteCountResultBO = new InsuranceQuoteCountResultBO();
        insuranceQuoteCountResultBO.setCountKey(230 + "");
        insuranceQuoteCountResultBO.setCountValue(insuranceQuoteCountBO.getFinished());
        insuranceQuoteCountResultBO.setShowValue("已完成");
        countList.add(insuranceQuoteCountResultBO);

        insuranceQuoteCountResultBO = new InsuranceQuoteCountResultBO();
        insuranceQuoteCountResultBO.setCountKey(240 + "");
        insuranceQuoteCountResultBO.setCountValue(insuranceQuoteCountBO.getRefused());
        insuranceQuoteCountResultBO.setShowValue("被驳回");
        countList.add(insuranceQuoteCountResultBO);

        insuranceQuoteCountResultBO = new InsuranceQuoteCountResultBO();
        insuranceQuoteCountResultBO.setCountKey(220 + "");
        insuranceQuoteCountResultBO.setCountValue(insuranceQuoteCountBO.getChanged());
        insuranceQuoteCountResultBO.setShowValue("待改派");
        countList.add(insuranceQuoteCountResultBO);

        insuranceQuoteCountResultBO = new InsuranceQuoteCountResultBO();
        insuranceQuoteCountResultBO.setCountKey(210 + "");
        insuranceQuoteCountResultBO.setCountValue(insuranceQuoteCountBO.getHandling());
        insuranceQuoteCountResultBO.setShowValue("处理中");
        countList.add(insuranceQuoteCountResultBO);

        insuranceQuoteCountResultBO = new InsuranceQuoteCountResultBO();
        insuranceQuoteCountResultBO.setCountKey(200 + "");
        insuranceQuoteCountResultBO.setCountValue(insuranceQuoteCountBO.getNoHandle());
        insuranceQuoteCountResultBO.setShowValue("未处理");
        countList.add(insuranceQuoteCountResultBO);

        insuranceQuoteCountResultBO = new InsuranceQuoteCountResultBO();
        insuranceQuoteCountResultBO.setCountKey(250 + "");
        insuranceQuoteCountResultBO.setCountValue(insuranceQuoteCountBO.getClosed());
        insuranceQuoteCountResultBO.setShowValue("已关闭");
        countList.add(insuranceQuoteCountResultBO);

        // 加入翻页信息
        PageInfo<InsuranceQuoteResultBO> pageList = new PageInfo<>(modelList);
        InsuranceQuoteAllResultBO InsuranceQuoteAllResultBO = new InsuranceQuoteAllResultBO();
        InsuranceQuoteAllResultBO.setPageList(pageList);
        InsuranceQuoteAllResultBO.setCountList(countList);
        return InsuranceQuoteAllResultBO;
    }

    public int compare(String o1, String o2) {

        // 指定排序器按照降序排列
        return o2.compareTo(o1);
    }

    /*    *//**
     * 项目名称：evcard-mtc-service 类名称：SaveVO 类描述： 创建人：niwen-倪文
     * 创建时间：2017年12月12日 下午4:47:56 修改备注： @version1.0
     */
    /*
     * class SaveVO<T> { private List<T> list = new ArrayList<T>();
     *
     * SaveVO(List<T> list) { this.list = list; }
     *
     * public FrontDefaultWebRespVO getErrorVO(int save, String messageHeader) {
     * log.debug("添加" + messageHeader + "..."); if (save < 1) {
     * FrontDefaultWebRespVO vo = new FrontDefaultWebRespVO();
     * log.error(messageHeader + "保存失败,数据为：" + JSON.toJSONString(list));
     * vo.setCode(Contants.RETURN_ERROR_CODE); vo.setMessage(messageHeader +
     * "保存失败"); return vo; }
     *
     * return FrontDefaultWebRespVO.SUCCESS;
     *
     * } }
     */

    /**
     * 维修报价换件项目画面
     *
     * @param replaceItemDetailBOList
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO insertReplaceItem(String taskNo, List<ReplaceItemDetailBO> replaceItemDetailBOList,
                                                   HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(taskNo);

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的已完成状态
        if (Contants.INSURANCE_QUOTE_COMPLETED == taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经处于维修报价的已完成状态，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        List<ReplaceItemDetail> replaceItemDetailModelList = new ArrayList<ReplaceItemDetail>();

        Map<String, String> typeMap = new HashMap<String, String>();
        // 删除之前保存的
        replaceItemDetailMapper.deleteBytaskNo(taskNo);

        // 定损合计
        Double repairReplaceTotalAmount = (double) 0;
        if (CollectionUtils.isNotEmpty(replaceItemDetailBOList)) {

            for (ReplaceItemDetailBO replaceItemDetailBO : replaceItemDetailBOList) {

                String partName = replaceItemDetailBO.getPartName() == null ? "" : replaceItemDetailBO.getPartName();
                String groupingName = replaceItemDetailBO.getGroupingName() == null ? ""
                        : replaceItemDetailBO.getGroupingName();
                if (typeMap.containsKey(partName + groupingName)) {
                    vo.setCode(-1);
                    vo.setMessage("已添加该零件");
                    return vo;
                } else {
                    typeMap.put(partName + groupingName, "");
                }

                ReplaceItemDetail replaceItemDetailModel = new ReplaceItemDetail();

                BeanUtilsConvert.copyPropertiess(replaceItemDetailBO, replaceItemDetailModel);

                if (StringUtils.isNotBlank(replaceItemDetailBO.getInsuranceQuoteAmount())) {
                    replaceItemDetailModel.setViewAmount(new BigDecimal(replaceItemDetailBO.getInsuranceQuoteAmount()));
                }

                replaceItemDetailModel.setCreateOperId(operId);
                replaceItemDetailModel.setCreateOperName(operName);
                replaceItemDetailModel.setCreateTime(time);
                replaceItemDetailModel.setUpdateOperId(operId);
                replaceItemDetailModel.setUpdateOperName(operName);
                replaceItemDetailModel.setUpdateTime(time);
                replaceItemDetailModel.setStatus(Contants.ONE);
                replaceItemDetailModelList.add(replaceItemDetailModel);

                if (StringUtils.isNotBlank(replaceItemDetailBO.getInsuranceQuoteAmount())) {
                    repairReplaceTotalAmount += Double.valueOf(replaceItemDetailBO.getInsuranceQuoteAmount());
                }

                vo = MessageUtil.checkNumericLen("总金额",
                        BigDecimal.valueOf(repairReplaceTotalAmount).setScale(2, RoundingMode.HALF_UP).toString(), 7,
                        2);

                if (vo.getCode() != 0) {
                    return vo;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(replaceItemDetailModelList)) {
            // 批量插入
            replaceItemDetailMapper.batchInsert(replaceItemDetailModelList);
        }
        // 更新换件金额合计
        RepairTask repairTask = new RepairTask();
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setRepairReplaceTotalAmount(
                BigDecimal.valueOf(repairReplaceTotalAmount).setScale(2, RoundingMode.HALF_UP));
        repairTask.setVehicleReplaceTotalAmount(
                BigDecimal.valueOf(repairReplaceTotalAmount).setScale(2, RoundingMode.HALF_UP));
        repairTask.setTaskNo(taskNo);
        repairTaskMapper.updateRepairTaskByTaskNo(repairTask);

        return vo;
    }

    /**
     * 维修报价删除换件项目
     *
     * @param id
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO deleteReplaceItem(String id, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 根据维修项目id取得任务编号
        String taskNo = replaceItemDetailMapper.selectTasknoById(Long.valueOf(id));

        if (StringUtils.isBlank(taskNo)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(taskNo);

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的已完成状态
        if (Contants.INSURANCE_QUOTE_COMPLETED == taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经处于维修报价的已完成状态，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        int delete = 1;
        if (replaceItemDetailMapper.selectById(Long.parseLong(id)) != null) {
            delete = replaceItemDetailMapper.deleteById(Long.parseLong(id));
            if (delete < 1) {

                log.error(Contants.LOG_MTC_REPLACE_ITEM_DETAIL + "删除失败,数据id为：" + id);
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage(Contants.LOG_MTC_REPLACE_ITEM_DETAIL + "删除失败");
                operatorLogServiceImpl.saveMtcOperatorLog(Contants.TABLENAME_MTC_REPLACE_ITEM_DETAIL,
                        Long.parseLong(id),
                        ComUtil.getOpeContent(Contants.TABLE_OPE_DELETE, Contants.LOG_MTC_REPLACE_ITEM_DETAIL),
                        Contants.INSURANCE_QUOTE, comModel);
                return vo;
            }
        }
        return vo;
    }

    /**
     * 维修报价添加修理项目
     *
     * @param repairItemDetailBOList
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO insertRepairItem(String taskNo, List<RepairItemDetailBO> repairItemDetailBOList,
                                                  HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(taskNo);

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的已完成状态
        if (Contants.INSURANCE_QUOTE_COMPLETED == taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经处于维修报价的已完成状态，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        List<RepairItemDetail> repairItemDetailModelLList = new ArrayList<RepairItemDetail>();

        // 删除之前保存的
        repairItemDetailMapper.deleteBytaskNo(taskNo);

        // 定损合计
        Double repairRepairTotalAmount = (double) 0;

        Map<String, String> typeMap = new HashMap<String, String>();
        if (CollectionUtils.isNotEmpty(repairItemDetailBOList)) {
            for (RepairItemDetailBO repairItemDetailBO : repairItemDetailBOList) {
                String repairName = repairItemDetailBO.getRepairName() == null ? ""
                        : repairItemDetailBO.getRepairName();
                String groupingName = repairItemDetailBO.getGroupingName() == null ? ""
                        : repairItemDetailBO.getGroupingName();
                if (typeMap.containsKey(repairName + groupingName)) {
                    vo.setCode(-1);
                    vo.setMessage("已添加该修理类目");
                    return vo;
                } else {
                    typeMap.put(repairName + groupingName, "");
                }
                RepairItemDetail repairItemDetailModel = null;

                repairItemDetailModel = new RepairItemDetail();
                BeanUtilsConvert.copyPropertiess(repairItemDetailBO, repairItemDetailModel);
                if (StringUtils.isNotBlank(repairItemDetailBO.getRepairAmount())) {
                    repairItemDetailModel.setViewAmount(new BigDecimal(repairItemDetailBO.getRepairAmount()));
                }
                repairItemDetailModel.setCreateOperId(operId);
                repairItemDetailModel.setCreateOperName(operName);
                repairItemDetailModel.setCreateTime(time);
                repairItemDetailModel.setUpdateOperId(operId);
                repairItemDetailModel.setUpdateOperName(operName);
                repairItemDetailModel.setUpdateTime(time);
                repairItemDetailModel.setStatus(Contants.ONE);
                repairItemDetailModelLList.add(repairItemDetailModel);

                if (StringUtils.isNotBlank(repairItemDetailBO.getRepairAmount())) {
                    repairRepairTotalAmount += Double.valueOf(repairItemDetailBO.getRepairAmount());
                }

                vo = MessageUtil.checkNumericLen("总金额",
                        BigDecimal.valueOf(repairRepairTotalAmount).setScale(2, RoundingMode.HALF_UP).toString(), 7, 2);

                if (vo.getCode() != 0) {
                    return vo;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(repairItemDetailModelLList)) {
            // 批量插入
            repairItemDetailMapper.batchInsert(repairItemDetailModelLList);
        }
        // 更新维修金额合计
        RepairTask repairTask = new RepairTask();
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setRepairRepairTotalAmount(
                BigDecimal.valueOf(repairRepairTotalAmount).setScale(2, RoundingMode.HALF_UP));
        repairTask.setVehicleRepairTotalAmount(
                BigDecimal.valueOf(repairRepairTotalAmount).setScale(2, RoundingMode.HALF_UP));
        repairTask.setTaskNo(taskNo);
        repairTaskMapper.updateRepairTaskByTaskNo(repairTask);

        return vo;
    }

    /**
     * 维修报价删除修理项目
     *
     * @param id
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO deleteRepairItem(String id, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 根据维修项目id取得任务编号
        String taskNo = repairItemDetailMapper.selectTasknoById(Long.valueOf(id));

        if (StringUtils.isBlank(taskNo)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(taskNo);

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的已完成状态
        if (Contants.INSURANCE_QUOTE_COMPLETED == taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经处于维修报价的已完成状态，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        int delete = 1;
        if (repairItemDetailMapper.selectById(Long.parseLong(id)) != null) {
            delete = repairItemDetailMapper.deleteById(Long.parseLong(id));
            if (delete < 1) {
                log.error(Contants.LOG_MTC_REPAIR_ITEM_DETAIL + "删除失败,数据id为：" + id);
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage(Contants.LOG_MTC_REPAIR_ITEM_DETAIL + "删除失败");
                operatorLogServiceImpl.saveMtcOperatorLog(Contants.TABLENAME_MTC_REPAIR_ITEM_DETAIL, Long.parseLong(id),
                        ComUtil.getOpeContent(Contants.TABLE_OPE_DELETE, Contants.LOG_MTC_REPAIR_ITEM_DETAIL),
                        Contants.INSURANCE_QUOTE, comModel);
                return vo;
            }
        }
        return vo;
    }

    /**
     * 维修报价添加备注
     *
     * @param repairRemarkBO
     * @param comModel
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO insertRepairRemark(RepairRemarkBO repairRemarkBO, ComModel comModel) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        int save = 1;
        String remark = null;
        Integer tache = null;
        if ("1".equals(repairRemarkBO.getRepairStage())) {
            remark = "维修报价";
            tache = 20;
        } else if ("2".equals(repairRemarkBO.getRepairStage())) {
            remark = "核损核价";
            tache = 30;
        } else if ("3".equals(repairRemarkBO.getRepairStage())) {
            remark = "车辆维修";
            tache = 50;
        } else if ("4".equals(repairRemarkBO.getRepairStage())) {
            remark = "车辆验收";
            tache = 60;
        } else if ("5".equals(repairRemarkBO.getRepairStage())) {
            remark = "进保预审";
        } else if ("6".equals(repairRemarkBO.getRepairStage())) {
            remark = "轮胎任务";
        }
        RemarkMesseage remarkMesseage = repairTaskMapper.getId(repairRemarkBO.getTaskNo());
        /*
         * if (remarkMesseage.getCurrentTache() != tache) {
         * vo.setCode(Contants.RETURN_ERROR_CODE);
         * vo.setMessage("任务不处于此环节，无法添加备注"); return vo; }
         */
        RepairRemark repairRemarkModel = null;
        if (repairRemarkBO.getId() != null && !repairRemarkBO.getId().isEmpty()
                && !"0".equals(repairRemarkBO.getId())) {
            repairRemarkModel = repairRemarkMapper.selectById(Long.parseLong(repairRemarkBO.getId()));
        }
        if (repairRemarkModel != null && repairRemarkModel.getId() != null && repairRemarkModel.getId() != 0) {
            // 备注没有修改的情况
            // BeanUtilsConvert.copyPropertiess(repairRemarkBO,repairRemarkModel);
            // save = repairRemarkMapper.updateByIdSelective(repairRemarkModel);
            // operatorLogServiceImpl.saveMtcOperatorLog(Contants.TABLENAME_MTC_REPAIR_REMARK,repairRemarkModel.getId(),
            // ComUtil.getOpeContent(Contants.TABLE_OPE_UPDATE,
            // Contants.LOG_MTC_REPAIR_REMARK),Contants.INSURANCE_QUOTE
            // ,comModel);
        } else {
            repairRemarkModel = new RepairRemark();
            BeanUtilsConvert.copyPropertiess(repairRemarkBO, repairRemarkModel);
            BeanUtilsConvert.copyPropertiess(comModel, repairRemarkModel);
            repairRemarkModel.setStatus(Contants.ONE);
        }
        save = repairRemarkMapper.save(repairRemarkModel);
        log.debug("添加" + Contants.LOG_MTC_REPAIR_REMARK + "...");
        if (save < 1) {
            log.error(Contants.LOG_MTC_REPAIR_REMARK + "保存失败,数据为：" + JSON.toJSONString(repairRemarkBO));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.LOG_MTC_REPAIR_REMARK + "保存失败");
            return vo;
        }
        jedisUtils.del(REMARK_REMARK_REDIS_KEY + repairRemarkBO.getTaskNo());
        jedisUtils.addSet(REMARK_REMARK_REDIS_KEY + repairRemarkBO.getTaskNo(), new String[]{comModel.getUserName()});
        operatorLogServiceImpl.saveMtcOperatorLog(Contants.TABLENAME_MTC_REPAIR_TASK, remarkMesseage.getId(),
                ComUtil.getOpeContent(Contants.TABLE_OPE_ADD, Contants.LOG_MTC_REPAIR_REMARK) + "："
                        + repairRemarkBO.getRemark(),
                remark, comModel);
        return vo;
    }

    /**
     * 维修报价删除备注
     *
     * @param id
     * @param comModel
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO deleteRepairRemark(String id, String repairStage, ComModel comModel) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        RepairRemark repareRemarkModel = repairRemarkMapper.selectById(Long.parseLong(id));
        if (null == repareRemarkModel) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.LOG_MTC_REPAIR_REMARK + "删除失败，数据已删除！");
            return vo;
        }

        RemarkMesseage remarkMesseage = repairTaskMapper.getId(repareRemarkModel.getTaskNo());
        String remark = null;
        if ("1".equals(repairStage)) {
            remark = "维修报价";
        } else if ("2".equals(repairStage)) {
            remark = "核损核价";
        } else if ("3".equals(repairStage)) {
            remark = "车辆维修";
        } else if ("4".equals(repairStage)) {
            remark = "车辆验收";
        } else if ("5".equals(repairStage)) {
            remark = "进保预审";
        }
        if (repareRemarkModel.getCreateOperId().equals(comModel.getCreateOperId())) {
            int delete = repairRemarkMapper.deleteById(Long.parseLong(id));
            if (delete < 1) {
                log.error(Contants.LOG_MTC_REPAIR_REMARK + "删除失败,数据id为：" + id);
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage(Contants.LOG_MTC_REPAIR_REMARK + "删除失败");
                return vo;
            }
        } else {
            log.error(Contants.LOG_MTC_REPAIR_REMARK + "删除失败,数据id为：" + id);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.LOG_MTC_REPAIR_REMARK + "删除失败" + "。该备注为其他用户创建，无法删除");
            return vo;
        }

        operatorLogServiceImpl.saveMtcOperatorLog(Contants.TABLENAME_MTC_REPAIR_TASK, remarkMesseage.getId(),
                "删除了备注：" + repareRemarkModel.getRemark(), remark, comModel);
        return vo;
    }

    /**
     * 修理类型下拉框
     *
     * @param request
     * @return
     */
    @Override
    public List<MarstCodeInfoListBO> getRepairTypeList(HttpServletRequest request) {
        List<MarstCodeInfoListBO> list = marstCodeInfoMapper.getMarstCodeInfoList(Contants.MARST_TABLE_NAME,
                Contants.MARST_REPAIR_TYPE_FIELD_NAME);
        return list;
    }

    /**
     * 维修报价提交审核
     *
     * @param comModel
     * @return
     */
    @Transactional
    @Override
    @SuppressWarnings({"unchecked"})
    public DefaultServiceRespDTO submitInsuranceTask(String id, UpdateRepairTaskBO updateRepairTaskBO, ComModel comModel) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

        // 定损单金额必填判断
        if (Integer.parseInt(repairTaskView.getRepairTypeId()) == Contants.REPAIR_TYPE_ID_ONE && repairTaskView.getReviewToSelFeeFlag() == 0) {
            if (StringUtils.isBlank(repairTaskView.getLossOrderAmount()) && StringUtils.isBlank(updateRepairTaskBO.getLossOrderAmount())) {
                vo.setCode(Contants.APPLETS_RETURN_ERROR_CODE);
                vo.setMessage("定损单金额未填，无法提交审核！");
                return vo;
            }
        }

        // 使用本地配件库
        if (RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskView.getRepairTypeId()), repairTaskView.getReviewToSelFeeFlag())) {
            List<MtcRepairItemCheckInfoDTO> mtcRepairItemCheckInfoDTOList = mtcRepairItemCheckInfoService.queryCheckListByTaskNo(repairTaskView.getTaskNo());
            if (CollectionUtils.isEmpty(mtcRepairItemCheckInfoDTOList)) {
                return new DefaultServiceRespDTO(Contants.APPLETS_RETURN_ERROR_CODE, "请先进行维修报价");
            }
        }
        // 使用精友配件库
        else {
            MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
            mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
            mtcLossInfoDTO.setStatus(1);
            List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
            if (CollectionUtils.isEmpty(mtcLossInfoList)
                    || StringUtils.isNotBlank(mtcLossInfoList.get(0).getAuditHandlerCode())) {
                return new DefaultServiceRespDTO(Contants.APPLETS_RETURN_ERROR_CODE, "请先进行维修报价");
            }
        }

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(updateRepairTaskBO.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的处理中状态
        if (Contants.INSURANCE_QUOTE_PROCESSING != taskSchedule.getInsuranceQuoteTaskSchedule()
                && Contants.INSURANCE_QUOTE_REJECT != taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价的处理中状态，请重新刷新一览页面");
            return vo;
        }

        vo = updateRepairTaskCheck(updateRepairTaskBO);
        if (vo.getCode() != 0) {
            return vo;
        }

        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        RepairTask repairTask = new RepairTask();
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(Long.parseLong(id));
        if (updateRepairTaskBO.getConfirmType() != null) {
            repairTask.setConfirmType(updateRepairTaskBO.getConfirmType());
        }
        // 事故报案号
        if (StringUtils.isNotBlank(updateRepairTaskBO.getAccidentReportNumber())) {
            repairTask.setAccidentReportNumber(updateRepairTaskBO.getAccidentReportNumber());
        }
        // 预计修理天数
        if (StringUtils.isNotBlank(updateRepairTaskBO.getExpectedRepairDays())) {
            repairTask.setExpectedRepairDays(Long.valueOf(updateRepairTaskBO.getExpectedRepairDays()));
        }
        // 定损单金额
        if (StringUtils.isNotBlank(updateRepairTaskBO.getLossOrderAmount())) {
            repairTask.setLossOrderAmount(new BigDecimal(updateRepairTaskBO.getLossOrderAmount()));
        }
        // 是否使用小程序 0-否 1-是
        if (StringUtils.isNotBlank(updateRepairTaskBO.getIsUsedApplets())) {
            repairTask.setIsUsedApplets(updateRepairTaskBO.getIsUsedApplets());
        }

        // 首次定损金额
        if (null == repairTaskView.getRepairTotalAmountFirst()) {
            // 定损总计
            if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairInsuranceTotalAmount())) {
                repairTask.setRepairTotalAmountFirst(new BigDecimal(updateRepairTaskBO.getRepairInsuranceTotalAmount()));
            }
        }

        // 预估理赔金额
        if (null != updateRepairTaskBO.getEstimatedClaimAmount()) {
            repairTask.setEstimatedClaimAmount(updateRepairTaskBO.getEstimatedClaimAmount());
            if (repairTask.getEstimatedClaimAmount().compareTo(repairTaskView.getEstimatedClaimAmount()) != 0) {
                // 修改预估理赔金额日志
                String updateLog = "修改预估理赔金额：" + repairTask.getEstimatedClaimAmount();
                LogUtil.saveMtcOperatorLog(mtcOperatorLogMapper, Long.parseLong(repairTaskView.getId()), updateLog, CurrentTacheEnum.getEnumDesc(repairTaskView.getCurrentTache()), comModel, time, repairTaskView.getCurrentTache(), Contants.ONE);
            }
        }

        repairTask.setNuclearLossReversionFlag(BigDecimal.ZERO);
        // 预计修理天数
        if (StringUtils.isNotBlank(updateRepairTaskBO.getExpectedRepairDays())) {
            RepairTaskViewBO repairTaskNewForTime = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date;
            try {
                date = sdf.parse(repairTaskNewForTime.getVehicleReciveTime());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, Integer.parseInt(updateRepairTaskBO.getExpectedRepairDays()));

                // 车辆预计完成时间
                repairTask.setExpectedRepairComplete(Timestamp.valueOf(sdf.format(calendar.getTime())));
            } catch (ParseException e) {
                log.error("预计修理完成时间计算错误");
                e.printStackTrace();
            }

        }

        // 更新状况卡控用当前环节(维修报价)
        repairTask.setControlStatusCurrentTache((long) Contants.CURRENT_TACHE_INSURANCE_QUOTE);
        log.error("维修报价提交审核  更新状况卡控用当前环节(维修报价) " + repairTask.getControlStatusCurrentTache());

        // 维修任务垫付
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustPaysDirect())) {
            repairTask.setCustPaysDirect(updateRepairTaskBO.getCustPaysDirect());
        }
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
            repairTask.setCustAmount(updateRepairTaskBO.getCustAmount());
        }

        // 确认车损类型
        AbstractOrderInfoDTO abstractOrderInfoDTO = newAndOldOrderConversionService
                .getOrderInfoById(repairTaskView.getAssociatedOrder(), repairTaskView.getVin());
        if (ObjectUtil.isNotEmpty(abstractOrderInfoDTO) && abstractOrderInfoDTO.getOrderType() == 1) {
            LongRentOrderInfoDTO longRentOrderInfoDTO = (LongRentOrderInfoDTO) abstractOrderInfoDTO;
            if (repairTaskView.getRepairTypeId().equals("1") || repairTaskView.getRepairTypeId().equals("2")) {
                if (null != longRentOrderInfoDTO.getVehicleClientMaintenanceFee() && longRentOrderInfoDTO.getVehicleClientMaintenanceFee() == 1) {
                    repairTask.setConfirmCarDamageType(2);
                }
            } else if (repairTaskView.getRepairTypeId().equals("3")) {
                if (null != longRentOrderInfoDTO.getClientUpkeepTag() && longRentOrderInfoDTO.getClientUpkeepTag() == 1) {
                    repairTask.setConfirmCarDamageType(2);
                }
            }
        }

        // 保存
        Integer count = repairTaskMapper.updateRepairTask(repairTask);
        if (count > 0) {
            List<VehicleRepairPic> allVehicleRepairPics = vehicleRepairPicMapper.getAllPicListByTaskNo(repairTaskView.getTaskNo());
            List<VehicleRepairPic> insertPicList;
            List<VehicleRepairPic> deletePicList;
            try {
                // 损坏部位图片
                insertPicList = new ArrayList<>(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)),
                        BigDecimal.valueOf(1),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList = new ArrayList<>(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)));
                // 维修图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getRepairPicture(), BigDecimal.valueOf(2)),
                        BigDecimal.valueOf(2),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getRepairPicture(), BigDecimal.valueOf(2)));
                // 事故责任认定书图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getAccidentLiabilityConfirmationPicture(), BigDecimal.valueOf(19)),
                        BigDecimal.valueOf(19),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getAccidentLiabilityConfirmationPicture(), BigDecimal.valueOf(19)));
                // 保司定损单图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getInsuranceCompanyLossOrderPicture(), BigDecimal.valueOf(20)),
                        BigDecimal.valueOf(20),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getInsuranceCompanyLossOrderPicture(), BigDecimal.valueOf(20)));
                // 我方驾驶证图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getOurDriverLicensePicture(), BigDecimal.valueOf(21)),
                        BigDecimal.valueOf(21),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getOurDriverLicensePicture(), BigDecimal.valueOf(21)));
                // 客户直付凭证
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)),
                        BigDecimal.valueOf(22),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)));
                // 损坏部位视频
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)),
                        BigDecimal.valueOf(23),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)));
            } catch (MtcBusinessRuntimeException e) {
                log.error("维修报价提交审核，包装图片异常！");
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage(e.getMessage());
                return vo;
            }

            if (CollectionUtils.isNotEmpty(deletePicList)) {
                // 批量删除
                vehicleRepairPicMapper.delMaterialPic(deletePicList.stream().map(VehicleRepairPic::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(insertPicList)) {
                // 批量插入
                vehicleRepairPicMapper.batchInsert(insertPicList);
            }
            try {
                // 非转自费的事故维修任务
                if (Integer.parseInt(repairTaskView.getRepairTypeId()) == Contants.REPAIR_TYPE_ID_ONE && repairTaskView.getReviewToSelFeeFlag() == 0) {
                    // 同步业财定损单金额
                    syncRepairDamageAmount(repairTaskView.getAccidentNo(), updateRepairTaskBO.getLossOrderAmount(), comModel.getToken());
                    // 同步业财图片
                    syncAccidentFileByWX(repairTaskView.getAccidentNo(), vehicleRepairPicMapper.getAllPicListByTaskNo(repairTaskView.getTaskNo()), comModel.getToken());
                }
            } catch (Exception e) {
                log.error("同步业财异常！", e);
            }
        }
        if (vo.getCode() != 0) {
            return vo;
        }
        InsuranceQuoteUpdateTaskBO insuranceQuoteUpdateTaskBO = new InsuranceQuoteUpdateTaskBO();
        insuranceQuoteUpdateTaskBO.setId(id);
        insuranceQuoteUpdateTaskBO.setCurrentTache(Contants.CURRENT_TACHE_VERIFICATION_LOSS);
        insuranceQuoteUpdateTaskBO.setInsuranceQuoteTaskSchedule(Contants.INSURANCE_QUOTE_COMPLETED);
        insuranceQuoteUpdateTaskBO.setVerificationLossTaskSchedule(Contants.VERIFICATION_LOSS_UNTREATED);

        BeanUtilsConvert.copyPropertiess(comModel, insuranceQuoteUpdateTaskBO);
        int save = insuranceQuoteMapper.updateInsuranceTask(insuranceQuoteUpdateTaskBO);
        if (save < 1) {
            log.error("维修报价提交审核失败,任务id为：" + id);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("维修报价提交审核失败");
            return vo;
        }

        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
        mtcOperatorLog.setUpdateOperId(operId);
        mtcOperatorLog.setUpdateOperName(operName);
        mtcOperatorLog.setUpdateTime(time);
        mtcOperatorLog.setCreateOperId(operId);
        mtcOperatorLog.setCreateOperName(operName);
        mtcOperatorLog.setCreateTime(time);
        mtcOperatorLog.setRemark("维修报价");
        mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
        mtcOperatorLog.setStatus(Contants.ONE);
        Integer custPaysDirect = updateRepairTaskBO.getCustPaysDirect();
        String opeContent = "提交审核，金额为：" + updateRepairTaskBO.getRepairInsuranceTotalAmount() + "元,";
        if (ObjectUtil.isNotEmpty(custPaysDirect)) {
            if (custPaysDirect > 0) {
                if (custPaysDirect == 1) {
                    opeContent = opeContent + " 是否客户直付:是";
                } else if (custPaysDirect == 2) {
                    opeContent = opeContent + " 是否客户直付:否";
                }
            }
            if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
                opeContent = opeContent + ", 金额" + NumberUtil.decimalFormat("0.00",updateRepairTaskBO.getCustAmount());
            }
        }
        mtcOperatorLog.setOpeContent(opeContent);
        mtcOperatorLog.setCurrentTache((long) Contants.CURRENT_TACHE_INSURANCE_QUOTE);
        mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcOperatorLogMapper.saveSelective(mtcOperatorLog);

        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setUpdateOperId(operId);
        mtcProcessLog.setUpdateOperName(operName);
        mtcProcessLog.setUpdateTime(time);
        mtcProcessLog.setCreateOperId(operId);
        mtcProcessLog.setCreateOperName(operName);
        mtcProcessLog.setCreateTime(time);
        mtcProcessLog.setRemark("核损核价");
        mtcProcessLog.setRecoderId(id);
        mtcProcessLog.setStatus(0);
        mtcProcessLog.setOpeContent("待处理");
        mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcProcessLog.setCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
        // 添加流程log
        mtcOperatorLogMapper.saveSelective(mtcProcessLog);


        if (!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskView.getRepairTypeId()), repairTaskView.getReviewToSelFeeFlag())) {
            // 任务状态同步（请求定损系统）
            String orgId = comModel.getOrgId();
            String orgName = orgInfoMapper.getOrgName(orgId);
            // PACKET > HEAD
            HeadBean headBean = new HeadBean();
            headBean.setRequestType("007");
            headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            // PACKET > BODY >EvalLossInfo
            EvalLossInfoSynchronize evalLossInfoSynchronize = new EvalLossInfoSynchronize();
            evalLossInfoSynchronize.setLossNo(repairTaskView.getTaskNo());
            evalLossInfoSynchronize.setReportCode(repairTaskView.getTaskNo());
            evalLossInfoSynchronize.setDmgVhclId(repairTaskView.getTaskNo());
            evalLossInfoSynchronize.setStatusCode("012");
            evalLossInfoSynchronize.setStatusName("定损提交到核损");
            evalLossInfoSynchronize.setComCode(orgId);
            evalLossInfoSynchronize.setCompany(orgName);
            evalLossInfoSynchronize.setBranchComCode(orgId);
            evalLossInfoSynchronize.setBranchComName(orgName);
            evalLossInfoSynchronize.setHandlerCode(String.valueOf(operId));
            evalLossInfoSynchronize.setHandlerName(operName);
            evalLossInfoSynchronize.setOperationLink("05");
            evalLossInfoSynchronize.setOperationResults("定损提交到核损");
            // PACKET > BODY
            BodyBeanSynchronize bodyBeanSynchronize = new BodyBeanSynchronize();
            bodyBeanSynchronize.setEvalLossInfoSynchronize(evalLossInfoSynchronize);
            // PACKET
            PacketBeanSynchronize packetBeanSynchronize = new PacketBeanSynchronize();
            packetBeanSynchronize.setHeadBean(headBean);
            packetBeanSynchronize.setBodyBeanSynchronize(bodyBeanSynchronize);
            StringWriter stringWriter = new StringWriter();
            JAXB.marshal(packetBeanSynchronize, stringWriter);
            String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "007");
            Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
            if (MapUtils.isEmpty(resultMap)) {
                log.error("任务状态同步失败：-------------->" + JSON.toJSONString(resultMap));
                return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务状态同步失败");
            }
            Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
            if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
                log.error("任务状态同步失败：-------------->" + JSON.toJSONString(resultMap));
                return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务状态同步失败");
            }
        }

        //通知维修报价提交审核
        insuranceQuotaSubject.notifyTaskSubmit(new BaseRepairEvent(time, repairTaskView.getTaskNo()));
        // 记录任务状态持续时间
        insuranceQuotaSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));
        log.debug("维修报价提交审核成功,任务id为：" + id);

        // 通知车管附加费
        if (updateRepairTaskBO.getAccidentReportNumber() != null && !updateRepairTaskBO.getAccidentReportNumber().equals(repairTaskView.getAccidentReportNumber())){
            UpdateAccidentReportNumberInput input = new UpdateAccidentReportNumberInput();
            input.setTaskNo(updateRepairTaskBO.getTaskNo());
            input.setNewAccidentReportNumber(updateRepairTaskBO.getAccidentReportNumber());
            input.setOldAccidentReportNumber(repairTaskView.getAccidentReportNumber());

            log.info("维修报价修改保险报案号同步附加费请求参数:{}",JSON.toJSONString(input));
            bdpMtcTaskInfoService.updateAccidentReportNumber(input);
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                ThreadPoolUtils.EXECUTOR.submit(() -> {
                    // 梧桐维修任务增加日志
                    SsoUserBaseInfoDto ssoUserBaseInfoDto = ssoUserService.getUserByUserName(comModel.getUserName());
                    repairTaskService.addRepairTaskLogForWt(new AddRepairTaskLogForWtDTO(
                            repairTaskView.getTaskNo(), "维修报价通过", "",
                            comModel.getUserName(), ssoUserBaseInfoDto.getdAccount()
                    ));

                    // 维修报价提交审核
                    // 整体调整客户直付金额
                    repairTaskService.adjustCustAmount(new OverallAdjustCustDTO(repairTaskView.getTaskNo(), repairTaskView.getCustPaysDirect(), repairTaskView.getCustAmount()), comModel);
                });
            }
        });


        return vo;
    }

    /**
     * 同步维修任务图片至业财系统
     *
     * @param vehicleRepairPicList 维修任务图片list
     * @return
     */
    public void syncAccidentFileByWX(String accidentNo, List<VehicleRepairPic> vehicleRepairPicList, String token) {
        String syncAccidentFileByWXUrl = apolloPropertyUtils.getString("bfc.syncAccidentFileByWXUrl");

        Map<String, String> header = new HashMap<>();
        header.put("token", token);

        JSONObject parameters = new JSONObject();
        JSONArray list = new JSONArray();
        parameters.put("list", list);
        for (VehicleRepairPic vehicleRepairPic : vehicleRepairPicList) {
            int fileType = getBfcFileType(vehicleRepairPic.getPicType().intValue());
            if (fileType != 0) {
                JSONObject file = new JSONObject();
                file.put("accidentNo", accidentNo);
                // 文件类型 1=驾驶人证件 2=事故照片 3=责任认定书 4=定损单
                // 21 1 19 20
                file.put("fileType", fileType);
                file.put("filePath", vehicleRepairPic.getPicUrl());
                list.add(file);
            }
        }

        try {
            JSONObject httpResult = HttpClientUtils.sendRestHttp_post(syncAccidentFileByWXUrl, "POST", JSON.toJSONString(parameters), header);
            if (null != httpResult) {
                log.info("SERVICE_URL：" + syncAccidentFileByWXUrl + "请求：" + JSON.toJSONString(parameters) + "结果：" + httpResult.toJSONString());
                if (httpResult.getInteger("retCode") == 0) {
                    log.info("同步维修任务图片至业财系统成功！");
                }
            }
        } catch (Exception e) {
            log.error("同步维修任务图片至业财系统：请求参数：" + JSON.toJSONString(parameters), e);
        }
    }

    /**
     * 获取业财文件类型
     *
     * @param picType 维修任务图片类型
     * @return 业财文件类型
     */
    private Integer getBfcFileType(Integer picType) {
        if (picType == 21) {
            return 1;
        }
        if (picType == 1) {
            return 2;
        }
        if (picType == 19) {
            return 3;
        }
        if (picType == 20) {
            return 4;
        }
        return 0;
    }

    /**
     * 同步定损单金额至业财系统
     *
     * @param accidentNo      事故编号
     * @param lossOrderAmount 定损单金额
     * @param token           token
     */
    public void syncRepairDamageAmount(String accidentNo, String lossOrderAmount, String token) {
        String syncRepairDamageAmountUrl = apolloPropertyUtils.getString("bfc.syncRepairDamageAmountUrl");

        Map<String, String> header = new HashMap<>();
        header.put("token", token);

        JSONObject parameters = new JSONObject();
        parameters.put("accidentNo", accidentNo);
        parameters.put("repairDamageAmount", lossOrderAmount);

        JSONObject httpResult;
        try {
            httpResult = HttpClientUtils.sendRestHttp_post(syncRepairDamageAmountUrl, "POST", JSON.toJSONString(parameters), header);
            if (null != httpResult) {
                log.info("SERVICE_URL：" + syncRepairDamageAmountUrl + "请求：" + JSON.toJSONString(parameters) + "结果：" + httpResult.toJSONString());
                if (httpResult.getInteger("retCode") == 0) {
                    log.info("同步定损单金额至业财系统成功！");
                }
            }
        } catch (Exception e) {
            log.error("同步定损单金额至业财系统：请求参数：" + JSON.toJSONString(parameters), e);
        }
    }

    /**
     * 维修报价申请改派
     *
     *
     * @param comModel@return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO reassignmentInsuranceTask(String id,
                                                           InsuranceQuoteUpdateTaskBO insuranceQuoteUpdateTaskBO, ComModel comModel) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的已完成状态
        if (Contants.INSURANCE_QUOTE_COMPLETED == taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经处于维修报价的已完成状态，请重新刷新一览页面");
            return vo;
        }

        vo = MessageUtil.checkLength("改派原因", insuranceQuoteUpdateTaskBO.getReassignmentReasons(), 500);

        if (vo.getCode() != 0) {
            return vo;
        }

        insuranceQuoteUpdateTaskBO.setId(id);
        insuranceQuoteUpdateTaskBO.setCurrentTache(Contants.CURRENT_TACHE_REASSIGNMENT);
        insuranceQuoteUpdateTaskBO.setInsuranceQuoteTaskSchedule(Contants.INSURANCE_QUOTE_REASSIGNMENT);
        insuranceQuoteUpdateTaskBO.setReassignmentTaskSchedule(Contants.REASSIGNMENT_APPLY_UNTREATED);
        BeanUtilsConvert.copyPropertiess(comModel, insuranceQuoteUpdateTaskBO);
        int save = insuranceQuoteMapper.updateInsuranceTask(insuranceQuoteUpdateTaskBO);
        if (save < 1) {
            log.error("维修报价提交审核失败,任务id为：" + id);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("维修报价申请改派失败");
            return vo;
        }

        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
        mtcOperatorLog.setUpdateOperId(operId);
        mtcOperatorLog.setUpdateOperName(operName);
        mtcOperatorLog.setUpdateTime(time);
        mtcOperatorLog.setCreateOperId(operId);
        mtcOperatorLog.setCreateOperName(operName);
        mtcOperatorLog.setCreateTime(time);
        mtcOperatorLog.setRemark("申请改派");
        mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
        mtcOperatorLog.setStatus(Contants.ONE);
        mtcOperatorLog.setOpeContent("申请改派，金额为：" + repairTaskNew.getRepairInsuranceTotalAmount() + "元");
        mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcOperatorLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_INSURANCE_QUOTE));
        // 添加基础log
        mtcOperatorLogMapper.saveSelective(mtcOperatorLog);

        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setUpdateOperId(operId);
        mtcProcessLog.setUpdateOperName(operName);
        mtcProcessLog.setUpdateTime(time);
        mtcProcessLog.setCreateOperId(operId);
        mtcProcessLog.setCreateOperName(operName);
        mtcProcessLog.setCreateTime(time);
        mtcProcessLog.setRemark("申请改派");
        mtcProcessLog.setRecoderId(id);
        mtcProcessLog.setStatus(0);
        mtcProcessLog.setOpeContent("待改派");
        mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcProcessLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_REASSIGNMENT_APPLY));
        // 添加流程log
        mtcOperatorLogMapper.saveSelective(mtcProcessLog);

        log.debug("维修报价申请改派成功,任务id为：" + id);
        //通知维修报价改派申请
        insuranceQuotaSubject.notifyTaskReassignment(new BaseRepairEvent(time,repairTaskNew.getTaskNo()));
        // 记录任务状态持续时间
        insuranceQuotaSubject.recordPeriod(new BaseRepairEvent(time, repairTaskNew.getTaskNo()));
        return vo;
    }

    /**
     * 维修报价保养转保养待维修
     *
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO changeRepairTypeInsuranceTask(String id, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        ComModel comModel = ComUtil.getUserInfo(request);
        InsuranceQuoteUpdateTaskBO insuranceQuoteUpdateTaskBO = new InsuranceQuoteUpdateTaskBO();
        insuranceQuoteUpdateTaskBO.setId(id);
        // 保养转维修状态更新为未选择
        insuranceQuoteUpdateTaskBO.setMaintainToRepairFlag("-1");
        // insuranceQuoteUpdateTaskBO.setRepairTypeId(Contants.REPAIR_TYPE_ID_TWO);
        // insuranceQuoteUpdateTaskBO.setRepairTypeName("自费维修");
        insuranceQuoteUpdateTaskBO.setCurrentTache(Contants.CURRENT_TACHE_MAINTENA_TO_REPAIR);
        BeanUtilsConvert.copyPropertiess(comModel, insuranceQuoteUpdateTaskBO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的处理中状态
        if (Contants.INSURANCE_QUOTE_PROCESSING != taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价的处理中状态，请重新刷新一览页面");
            return vo;
        }

        int save = insuranceQuoteMapper.updateInsuranceTask(insuranceQuoteUpdateTaskBO);
        if (save < 1) {

            log.error("维修报价保养转自费维修失败,任务id为：" + id);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("维修报价保养转自费维修失败");
            return vo;
        }

        operatorLogServiceImpl.saveMtcOperatorLog(Contants.TABLENAME_MTC_REPAIR_TASK, Long.parseLong(id),
                "保养转自费维修，金额为：" + repairTaskNew.getRepairInsuranceTotalAmount() + "元", Contants.INSURANCE_QUOTE,
                comModel);
        log.debug("维修报价保养转自费维修成功,任务id为：" + id);

        // 记录任务状态持续时间
        insuranceQuotaSubject.recordPeriod(new BaseRepairEvent(new Date(), repairTaskNew.getTaskNo()));

        return vo;
    }

    /**
     * 维修报价保养完成
     *
     * @param request
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO maintenanceOver(String id, String taskNo, String maintainAmount, int repairFlag,
                                                 HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于维修报价环节
        if (Contants.CURRENT_TACHE_INSURANCE_QUOTE != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查是否是维修报价的处理中状态
        if (Contants.INSURANCE_QUOTE_PROCESSING != taskSchedule.getInsuranceQuoteTaskSchedule()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于维修报价的处理中状态，请重新刷新一览页面");
            return vo;
        }

        vo = MessageUtil.checkNumericLen("保养费用", maintainAmount, 7, 2);
        if (vo.getCode() != 0) {
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        InsuranceQuoteUpdateTaskBO insuranceQuoteUpdateTaskBO = new InsuranceQuoteUpdateTaskBO();
        insuranceQuoteUpdateTaskBO.setId(id);
        insuranceQuoteUpdateTaskBO.setCurrentTache(Contants.CURRENT_TACHE_INSURANCE);
        // 保养转维修状态更新为未选择
        if (repairFlag == 1) {
            insuranceQuoteUpdateTaskBO.setMaintainToRepairFlag("-1");
            insuranceQuoteUpdateTaskBO.setCurrentTache(Contants.CURRENT_TACHE_MAINTENA_TO_REPAIR);
        }
        insuranceQuoteUpdateTaskBO.setInsuranceQuoteTaskSchedule(Contants.INSURANCE_QUOTE_COMPLETED);
        BigDecimal maintainAmountD = new BigDecimal(maintainAmount);
        insuranceQuoteUpdateTaskBO.setMaintainAmount(maintainAmountD);
        BeanUtilsConvert.copyPropertiess(comModel, insuranceQuoteUpdateTaskBO);
        int save = insuranceQuoteMapper.updateInsuranceTask(insuranceQuoteUpdateTaskBO);
        if (save < 1) {
            log.error("维修报价保养失败,任务id为：" + id);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("维修报价保养失败");
            return vo;
        }

        operatorLogServiceImpl.saveMtcOperatorLog(Contants.TABLENAME_MTC_REPAIR_TASK, Long.parseLong(id),
                "保养完成，金额为：" + maintainAmount + "元", Contants.INSURANCE_QUOTE, comModel);
        log.debug("维修报价保养完成成功,任务id为：" + id);

        // 当不需要维修时调用调度接口
        if (repairFlag == 0) {
            // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
            int idsUseMode = 1;
            try {
                idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
            } catch (Exception e) {
                idsUseMode = 1;
            }

            if (idsUseMode == 1) {

                // 调用调度核损核价接口
                String info = "";
                // 取得详情
                RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

                MtcMoveUpGettingVehicleBO mtcMoveUpGettingVehicle = new MtcMoveUpGettingVehicleBO();
                // 获得维修厂省市区
                mtcMoveUpGettingVehicle.setDispatchTaskSeq(taskNo);
                // 获得维修厂省市区
                AtcPricingAdoptBO repairTaskArea = repairDepotInfoMapper
                        .getDepotArea(repairTaskView.getRepairDepotId());
                if (repairTaskArea != null) {
                    mtcMoveUpGettingVehicle.setProvinceName(repairTaskArea.getProvinceName());
                    mtcMoveUpGettingVehicle.setCityName(repairTaskArea.getCityName());
                    mtcMoveUpGettingVehicle.setAreaName(repairTaskArea.getAreaName());
                }

                try {
                    info = HttpRequest.postMethod(apolloPropertyUtils.getString("evcard.ids.dns")
                            + "repair-factory-new/mtcMoveUpGettingVehicle", mtcMoveUpGettingVehicle);
                } catch (Exception ex) {
                    log.info("调度系统异常:" + ex.toString());
                    throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                }

                log.info("保养完成调用调度接口：" + info);

                if (StringUtils.isBlank(info)) {
                    throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                } else {
                    vo = JSON.parseObject(info, DefaultServiceRespDTO.class);

                    if (vo.getCode() != 0) {
                        throw new MtcBusinessRuntimeException(vo.getMessage(), String.valueOf(vo.getCode()));
                    }
                }

                info = "";

            }
        }

        return vo;
    }

    /**
     * 核损核价任务详情
     *
     * @param id 任务id
     * @return
     */
    @Override
    @Transactional
    public RepairTaskViewBO operateRepairTaskViewIns(Long id, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        // 取得换件详细
        List<ViewReplaceItemDetailBO> replaceItemDetailList = repairTaskMapper
                .getReplaceItemDetailList(repairTaskView.getTaskNo());
        repairTaskView.setReplaceItemDetail(replaceItemDetailList);


        // 取得修理详情
        List<ViewRepairItemDetailBO> repairItemDetailList = repairTaskMapper
                .getViewReplaceItemDetailList(repairTaskView.getTaskNo());
        repairTaskView.setRepairItemDetail(repairItemDetailList);

        // 第一次定损时，设为空
        if (null == repairTaskView.getRepairTotalAmountFirst() && !repairTaskView.getRepairTypeId().equals("3") && !repairTaskView.getRepairTypeId().equals("7")) {
            repairTaskView.setVehicleRepairTotalAmount(null);
            repairTaskView.setVehicleReplaceTotalAmount(null);
            repairTaskView.setVehicleInsuranceTotalAmount(null);
        }

        if (CollectionUtils.isNotEmpty(repairTaskView.getReplaceItemDetail())) {
            for (ViewReplaceItemDetailBO viewReplaceItemDetail : repairTaskView.getReplaceItemDetail()) {
                if (StringUtils.isNotBlank(viewReplaceItemDetail.getInsuranceQuoteAmount())
                        && StringUtils.isNotBlank(viewReplaceItemDetail.getViewAmount())
                        && !StringUtils.equals(viewReplaceItemDetail.getInsuranceQuoteAmount(),
                        viewReplaceItemDetail.getViewAmount())) {
                    viewReplaceItemDetail.setIsObjection("1");
                } else {
                    viewReplaceItemDetail.setIsObjection("0");
                }
            }
        }

        if (CollectionUtils.isNotEmpty(repairTaskView.getRepairItemDetail())) {
            for (ViewRepairItemDetailBO viewRepairItemDetail : repairTaskView.getRepairItemDetail()) {
                if (StringUtils.isNotBlank(viewRepairItemDetail.getRepairAmount())
                        && StringUtils.isNotBlank(viewRepairItemDetail.getViewAmount()) && !StringUtils
                        .equals(viewRepairItemDetail.getRepairAmount(), viewRepairItemDetail.getViewAmount())) {
                    viewRepairItemDetail.setIsObjection("1");
                } else {
                    viewRepairItemDetail.setIsObjection("0");
                }
            }
        }

        if (repairTaskView.getCurrentTache() == Contants.CURRENT_TACHE_INSURANCE_QUOTE) {
            // 定损定价时进行计算
            BigDecimal repairRepairTotalAmount = BigDecimal.ZERO;
            BigDecimal repairReplaceTotalAmount = BigDecimal.ZERO;
            BigDecimal repairInsuranceTotalAmount = BigDecimal.ZERO;

            if (StringUtils.isNotBlank(repairTaskView.getRepairRepairTotalAmount())) {
                repairRepairTotalAmount = new BigDecimal(repairTaskView.getRepairRepairTotalAmount());
            }

            if (StringUtils.isNotBlank(repairTaskView.getRepairReplaceTotalAmount())) {
                repairReplaceTotalAmount = new BigDecimal(repairTaskView.getRepairReplaceTotalAmount());
            }

            repairInsuranceTotalAmount = repairRepairTotalAmount.add(repairReplaceTotalAmount).setScale(2,
                    BigDecimal.ROUND_HALF_UP);
            repairTaskView.setRepairInsuranceTotalAmount(repairInsuranceTotalAmount.toString());

            // 未处理查看之后更新状态为处理中
            if (StringUtils.equals(Contants.INSURANCE_QUOTE_UNTREATED + StringUtils.EMPTY,
                    repairTaskView.getInsuranceQuoteTaskSchedule())
                    || StringUtils.equals(Contants.INSURANCE_QUOTE_REJECT + StringUtils.EMPTY,
                    repairTaskView.getInsuranceQuoteTaskSchedule())) {

                RepairTask repairTask = new RepairTask();
                repairTask.setUpdateOperId(operId);
                repairTask.setUpdateOperName(operName);
                repairTask.setUpdateTime(time);
                repairTask.setId(id);
                if (Contants.INSURANCE_QUOTE_UNTREATED == Integer.parseInt(repairTaskView.getInsuranceQuoteTaskSchedule())
                        || Contants.INSURANCE_QUOTE_REJECT == Integer.parseInt(repairTaskView.getInsuranceQuoteTaskSchedule())) {
                    repairTask.setInsuranceQuoteTaskSchedule((long) Contants.INSURANCE_QUOTE_PROCESSING);
                }

                // 保存
                repairTaskMapper.updateRepairTask(repairTask);
            }
        }

        String repairOtherTotalAmount = null;
        String vehicleOtherTotalAmount = null;
        if (StringUtils.isNotBlank(repairTaskView.getRepairInsuranceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getRepairReplaceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getRepairRepairTotalAmount())) {
            repairOtherTotalAmount = new BigDecimal(repairTaskView.getRepairInsuranceTotalAmount())
                    .subtract(new BigDecimal(repairTaskView.getRepairReplaceTotalAmount()))
                    .subtract(new BigDecimal(repairTaskView.getRepairRepairTotalAmount())).toString();
        }
        if (StringUtils.isNotBlank(repairTaskView.getVehicleInsuranceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getVehicleReplaceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getVehicleRepairTotalAmount())) {
            vehicleOtherTotalAmount = new BigDecimal(repairTaskView.getVehicleInsuranceTotalAmount())
                    .subtract(new BigDecimal(repairTaskView.getVehicleReplaceTotalAmount()))
                    .subtract(new BigDecimal(repairTaskView.getVehicleRepairTotalAmount())).toString();
        }
        repairTaskView.setRepairOtherTotalAmount(repairOtherTotalAmount);
        repairTaskView.setVehicleOtherTotalAmount(vehicleOtherTotalAmount);
        repairTaskView.setMtcLossFitInfoBOList(mtcLossFitInfoMapper.queryMtcLossFitItemList(repairTaskView.getTaskNo()));
        repairTaskView.setMtcLossRepairInfoBOList(mtcLossRepairInfoMapper.queryMtcLossRepairItemList(repairTaskView.getTaskNo()));
        repairTaskView.setMtcLossAssistInfoBOList(mtcLossAssistInfoMapper.queryMtcLossAssistItemList(repairTaskView.getTaskNo()));

        repairTaskView.setMtcRepairItemCheckInfoDTOList(mtcRepairItemCheckInfoService.queryCheckListByTaskNo(repairTaskView.getTaskNo()));
        repairTaskView.setReviewRepairItemCheckInfoDTOList(mtcRepairItemCheckInfoService.queryCheckListByTaskNoAndInsurancePreReviewStatus(repairTaskView.getTaskNo(), 1));

        // 查询判断风险提示
        String riskTip = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(repairTaskView.getVin())) {
            // TODO 核损核价环节，判断该任务维修报价的换件项目和修理项目，是否存在与本次任务车辆前一万公里内（车辆总里程数满足 A-10000≤X≤A 的维修任务，A为本次任务总里程数）维修任务中重复维修的项目
            if (StringUtils.isNotBlank(repairTaskView.getTotalMileage())){
                BigDecimal totalMileage = new BigDecimal(repairTaskView.getTotalMileage());
                BigDecimal minTotalMileage = totalMileage.subtract(BigDecimal.valueOf(10000));
                log.info("id :{}",repairTaskView.getId());
                log.info("vin :{}",repairTaskView.getVin());
                log.info("totalMileage :{}",totalMileage);
                log.info("minTotalMileage :{}",minTotalMileage);
                // 查询当前车辆任务前一万公里内的任务记录
                List<RepairTaskViewBO> list = repairTaskMapper.getRepairTaskViewList(Long.valueOf(repairTaskView.getId()),repairTaskView.getVin(),minTotalMileage,totalMileage);
                boolean repeat = false;
                if (CollectionUtils.isNotEmpty(list)){
                    List<SettlementReportBO> settlementReportList = new ArrayList<>();
                    SettlementReportBO settlementReportBO;
                    List<String> taskNoList = new ArrayList<>();
                    for (RepairTaskViewBO bo : list) {
                        String taskNo = bo.getTaskNo();
                        settlementReportBO = new SettlementReportBO();
                        settlementReportBO.setTaskNo(taskNo);
                        settlementReportList.add(settlementReportBO);
                        taskNoList.add(taskNo);
                    }

                    List<MtcLossFitInfoBO> mtcLossFitInfoBOList = repairTaskView.getMtcLossFitInfoBOList();
                    if (CollectionUtils.isNotEmpty(mtcLossFitInfoBOList) && !repeat){
                        List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper.queryReplaceItemList(settlementReportList);
                        first:
                        for (MtcLossFitInfo info : mtcLossFitInfoList) {
                            String itemName = info.getItemName();
                            for (MtcLossFitInfoBO mtcLossFitInfo : mtcLossFitInfoBOList) {
                                if (itemName.equals(mtcLossFitInfo.getItemName())){
                                    log.info("1重复的任务编号 :{},更换项目名称：{}",info.getTaskNo(),itemName);
                                    log.info("itemName11 :{}",itemName);
                                    log.info("itemName12 :{}",mtcLossFitInfo.getItemName());
                                    repeat = true;
                                    break first;
                                }
                            }
                        }
                    }
                    List<MtcLossRepairInfoBO> mtcLossRepairInfoBOList = repairTaskView.getMtcLossRepairInfoBOList();
                    if (CollectionUtils.isNotEmpty(mtcLossRepairInfoBOList) && !repeat){
                        List<MtcLossRepairInfoBO> mtcLossRepairInfoList = mtcLossRepairInfoMapper.queryRepairItemList(settlementReportList);
                        first:
                        for (MtcLossRepairInfoBO info : mtcLossRepairInfoList) {
                            String itemName = info.getItemName();
                            for (MtcLossRepairInfoBO repairInfo : mtcLossRepairInfoBOList) {
                                if (itemName.equals(repairInfo.getItemName())){
                                    log.info("2重复的任务编号 :{},更换项目名称：{}",info.getTaskNo(),itemName);
                                    log.info("itemName21 :{}",itemName);
                                    log.info("itemName22 :{}",repairInfo.getItemName());
                                    repeat = true;
                                    break first;
                                }
                            }
                        }
                    }
                    List<MtcRepairItemCheckInfoDTO> mtcRepairItemCheckInfoDTOList = repairTaskView.getMtcRepairItemCheckInfoDTOList();
                    if (CollectionUtils.isNotEmpty(mtcRepairItemCheckInfoDTOList) && !repeat){
                        List<MtcRepairItemCheckInfoDTO> mtcRepairItemCheckInfoList = mtcRepairItemCheckInfoService.queryCheckListByTaskNoList(taskNoList);
                        first:
                        for (MtcRepairItemCheckInfoDTO dto : mtcRepairItemCheckInfoList) {
                            String itemName = dto.getItemName();
                            for (MtcRepairItemCheckInfoDTO mtcRepairItemCheckInfo : mtcRepairItemCheckInfoDTOList) {
                                if (itemName.equals(mtcRepairItemCheckInfo.getItemName())){
                                    log.info("3重复的任务编号 :{},更换项目名称：{}",dto.getTaskNo(),itemName);
                                    log.info("itemName31 :{}",itemName);
                                    log.info("itemName32 :{}",mtcRepairItemCheckInfo.getItemName());
                                    repeat = true;
                                    break first;
                                }
                            }
                        }
                    }
                    if (repeat){
                        riskTip = "该任务存在车辆一万公里内重复维修项目";
                    }
                }
            }
            if (StringUtils.isBlank(riskTip)){
                VehicleRepairRecordQueryBO bo = new VehicleRepairRecordQueryBO();
                bo.setVin(repairTaskView.getVin());
                bo.setId(id);
                List<VehicleRepairRecordBO> recordList = vehicleRepairMapper.queryVehicleRepairRecord(bo);
                int repairCount = 0;
                int amountCount = 0;
                boolean repeat = false;
                // 历史车辆维修数大于1
                if (CollectionUtils.isNotEmpty(recordList) && recordList.size() > 0) {
                    if (recordList.size() > 1) {
                        first:
                        for (int i = 0; i < recordList.size() - 1; i++) {
                            VehicleRepairRecordBO record = recordList.get(i);
                            Date taskInflowTime = record.getTaskInflowTime();
                            Date vehicleCheckTime = record.getVehicleCheckTime();
                            for (int j = i + 1; j < recordList.size(); j++) {
                                VehicleRepairRecordBO temp = recordList.get(j);
                                if (vehicleCheckTime == null || temp.getVehicleCheckTime() == null) {
                                    repeat = true;
                                    break first;
                                }
                                if (!record.getId().equals(temp.getId())
                                        && taskInflowTime.getTime() <= temp.getVehicleCheckTime().getTime()
                                        && vehicleCheckTime.getTime() >= temp.getTaskInflowTime().getTime()) {
                                    repeat = true;
                                    break first;
                                }
                            }
                        }
                    }
                    if (repeat) {
                        riskTip = "该车辆存在重复维修的任务！";
                    } else {
                        for (VehicleRepairRecordBO vehicleRepairRecordBO : recordList) {
                            // 维修金额大于等于2000
                            if (vehicleRepairRecordBO.getVehicleRepairTotalAmount().doubleValue() >= 2000) {
                                amountCount += 1;
                            }
                            // 30天内
                            Date now = new Date();
                            if (now.getTime() - vehicleRepairRecordBO.getTaskInflowTime().getTime() <= 30L * 24 * 60 * 60 * 1000) {
                                repairCount += 1;
                            }
                        }
                        if (amountCount >= 2) {
                            riskTip = String.format("该车辆已经累计%S次维修金额超出2000元！", amountCount);
                        } else if (repairCount > 0) {
                            riskTip = String.format("该车辆在30天内已经维修了%S次！", repairCount);
                        }
                    }
                }
            }
        }
        repairTaskView.setRiskTip(riskTip);
        // 查询车辆残值时间
        // 当前时间（默认）
        String queryVehicleScrapeValueTimeString = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DATE_TYPE2));
        if (StringUtils.isNotBlank(repairTaskView.getVerificationLossCheckTime())) {
            // 核损通过时间（存在核损通过时间）
            queryVehicleScrapeValueTimeString = repairTaskView.getVerificationLossCheckTime();
        } else {
            repairTaskView.setVehicleScrapeValue(vehicleMaintenanceLossAuditPriceService.queryCurrentVehicleScrapeValue(repairTaskView.getVin()).toString());
        }
        repairTaskView.setQueryVehicleScrapeValueTime(DateUtil.getFormatDate(queryVehicleScrapeValueTimeString, DateUtil.DATE_TYPE2, DateUtil.DATE_TYPE5));

        if (org.apache.commons.lang.StringUtils.isNotBlank(repairTaskView.getAccidentNo())) {
            // 事故理赔金额
            AccidentDamageDTO accidentDamageDTO = manualUtils.getAccidentDamage(repairTaskView.getAccidentNo(), comModel.getToken());
            repairTaskView.setAccidentDamage(accidentDamageDTO);
            // 事故任务详情
            AccidentInfoDetailDTO accidentInfoDetailDTO = manualUtils.getAccidentInfoDetail(repairTaskView.getAccidentNo(), comModel.getToken());
            if (accidentInfoDetailDTO != null && CollectionUtils.isNotEmpty(accidentInfoDetailDTO.getAccidentFileList())){
                List<AccidentFileDataDTO> accidentFileList = accidentInfoDetailDTO.getAccidentFileList();
                Map<String, List<AccidentFileDataDTO>> map = accidentFileList.stream().collect(Collectors.groupingBy(x -> x.getAccidentNo()));
                List<AccidentCaseFileDataDTO> accidentCaseFileDataList = Lists.newArrayList();
                map.forEach((caseNo,list)->{
                    if(StringUtils.isNotBlank(caseNo) && caseNo.startsWith("BA")){
                        AccidentCaseFileDataDTO fileDataDTO = new AccidentCaseFileDataDTO();
                        fileDataDTO.setCaseNo(caseNo);
                        fileDataDTO.setAccidentFileList(list);
                        accidentCaseFileDataList.add(fileDataDTO);
                    }
                    // 事故照片
                    if (StringUtils.isNotBlank(caseNo) && caseNo.startsWith("SG")){
                        // 设置下载路径
                        manualUtils.setInsurDownloadPath(list);
                        accidentInfoDetailDTO.setAccidentPicList(list);
                    }
                });
                accidentInfoDetailDTO.setAccidentCaseFileDataList(accidentCaseFileDataList);
            }
            repairTaskView.setAccidentInfoDetail(accidentInfoDetailDTO);
        }

        // 查询保单信息
        String sendRepairTime = repairTaskView.getSendRepairTime();
        if (StringUtils.isNotBlank(sendRepairTime)){
            String sendRepairTimeFormat = sendRepairTime.substring(0, 10);
            repairTaskView.setInsuranceInfoList(manualUtils.getInsuranceInfo(repairTaskView.getVin(), sendRepairTimeFormat));
            repairTaskView.setSendRepairTime(sendRepairTimeFormat);
        }

        // 查询梧桐维修照片和情况说明
        WtRepairFileBo wtRepairFile = manualUtils.getWtRepairFileList(repairTaskView.getTaskNo());
        if (wtRepairFile != null){
            repairTaskView.setWtRepairFileList(wtRepairFile.getRepairFiles());
            repairTaskView.setSituationDesc(wtRepairFile.getSituationDesc());
        }

        return repairTaskView;
    }

    /**
     * 任务详情时间节点
     */
    @Override
    public TaskScheduleLogTime repairTaskTimePoint(String taskNo, HttpServletRequest request) {
        TaskScheduleLogTime logTime = new TaskScheduleLogTime();

        RepairTask repairTask = repairTaskMapper.selectByTaskNo(taskNo);
        logTime.setInsurancePreReviewTaskSchedule(repairTask != null ? repairTask.getInsurancePreReviewTaskSchedule() : -1);

        List<RealTimeOperatorLogBO> list = mtcOperatorLogMapper.queryRepairTaskTimePoint(taskNo);
        for (RealTimeOperatorLogBO operatorLogBO : list) {
            if (logTime.getInsurancePreReviewTaskSchedule() != -1) {
                // 车辆交接
                if ("10".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("2");
                }
                // 进保预审
                else if ("110".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("3");
                }
                // 定损报价
                else if ("20".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("4");
                }
                // 核损报价
                else if ("30".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("5");
                }
                // 改派申请
                else if ("40".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("5");
                }
                // 车辆维修
                else if ("50".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("6");
                }
                // 车辆验收&材料收集&损失登记&结算管理
                else if (Integer.parseInt(operatorLogBO.getCurrentTache()) >= 60 && !"110".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("7");
                }
            } else {
                // 车辆交接
                if ("10".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("2");
                }
                // 定损报价
                else if ("20".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("3");
                }
                // 核损报价
                else if ("30".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("4");
                }
                // 改派申请
                else if ("40".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("4");
                }
                // 车辆维修
                else if ("50".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("5");
                }
                // 车辆验收&材料收集&损失登记&结算管理
                else if (Integer.parseInt(operatorLogBO.getCurrentTache()) >= 60 && !"110".equals(operatorLogBO.getCurrentTache())) {
                    logTime.setCurrentTache("6");
                }
            }

            if ("车辆交接".equals(operatorLogBO.getRemark())) {
                logTime.setVehicleTransferTaskScheduleLogTime(operatorLogBO.getCreateTimeString());
                logTime.setFlowTime(operatorLogBO.getFlowTimeString());
                continue;
            }
            if ("10".equals(operatorLogBO.getCurrentTache()) && StringUtils.isBlank(operatorLogBO.getRemark())) {
                logTime.setFlowTime(operatorLogBO.getFlowTimeString());
                continue;
            }
            if ("维修报价".equals(operatorLogBO.getRemark())) {
                logTime.setInsuranceQuoteTaskScheduleLogTime(operatorLogBO.getCreateTimeString());
                logTime.setFlowTime(operatorLogBO.getFlowTimeString());
                continue;
            }
            if ("核损核价".equals(operatorLogBO.getRemark())) {
                if (Integer.parseInt(operatorLogBO.getCurrentTache()) >= 40) {
                    logTime.setVerificationLossTaskScheduleLogTime(operatorLogBO.getCreateTimeString());
                }
                logTime.setFlowTime(operatorLogBO.getFlowTimeString());
                continue;
            }
            if ("车辆维修".equals(operatorLogBO.getRemark())) {
                logTime.setVehicleRepairTaskScheduleLogTime(operatorLogBO.getCreateTimeString());
                logTime.setFlowTime(operatorLogBO.getFlowTimeString());
                continue;
            }
            if ("车辆验收".equals(operatorLogBO.getRemark())) {
                logTime.setVehicleCheckTaskScheduleLogTime(operatorLogBO.getCreateTimeString());
                logTime.setFlowTime(operatorLogBO.getFlowTimeString());
                continue;
            }
            if ("进保预审".equals(operatorLogBO.getRemark())) {
                logTime.setInsurancePreReviewTaskScheduleLogTime(operatorLogBO.getCreateTimeString());
                logTime.setFlowTime(operatorLogBO.getFlowTimeString());
                continue;
            }
        }
        if (repairTask != null && repairTask.getVerificationLossTaskSchedule() == 340) {
            logTime.setTaskCloseTime(ComUtil.dateToString(repairTask.getUpdateTime()));
        }
        return logTime;
    }

    /**
     * 定损核价保存CHECK
     *
     * @return
     */
    private DefaultServiceRespDTO updateRepairTaskCheck(UpdateRepairTaskBO updateRepairTaskBO) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 事故报案号长度
        vo = MessageUtil.checkLength(Contants.ACCIDENT_REPORT_NUMBER, updateRepairTaskBO.getAccidentReportNumber(), 50);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 预计修理天数长度
        vo = MessageUtil.checkInteger(Contants.EXPECTED_REPAIR_DAYS, updateRepairTaskBO.getExpectedRepairDays());

        if (vo.getCode() != 0) {
            return vo;
        }

        return vo;

    }

    /**
     * 维修报价查看或占据任务
     *
     * @param id   任务id
     * @param type 操作类型
     * @return
     */
    @Override
    @Transactional
    public DefaultServiceRespDTO insuranceQuoteView(Long id, Integer type, HttpServletRequest request) {

        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        ComModel comModel = ComUtil.getUserInfo(request);
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        // 取得换件详细
        List<ViewReplaceItemDetailBO> replaceItemDetailList = repairTaskMapper
                .getReplaceItemDetailList(repairTaskView.getTaskNo());

        // 取得修理详情
        List<ViewRepairItemDetailBO> repairItemDetailList = repairTaskMapper
                .getViewReplaceItemDetailList(repairTaskView.getTaskNo());

        repairTaskView.setReplaceItemDetail(replaceItemDetailList);
        repairTaskView.setRepairItemDetail(repairItemDetailList);

        // 查询维修项目详情(保养和终端任务)
        List<MtcRepairItemCheckInfoDTO> mtcRepairItemCheckInfoDTOList = mtcRepairItemCheckInfoService.queryCheckListByTaskNo(repairTaskView.getTaskNo());
        repairTaskView.setMtcRepairItemCheckInfoDTOList(mtcRepairItemCheckInfoDTOList);

        // 查询维修项目详情(预审维修项目)
        List<MtcRepairItemCheckInfoDTO> reviewRepairItemCheckInfoDTOList = mtcRepairItemCheckInfoService.queryCheckListByTaskNoAndInsurancePreReviewStatus(repairTaskView.getTaskNo(), 1);
        repairTaskView.setReviewRepairItemCheckInfoDTOList(reviewRepairItemCheckInfoDTOList);

        // 第一次定损时，设为空
        if (null == repairTaskView.getRepairTotalAmountFirst() && !repairTaskView.getRepairTypeId().equals("3") && !repairTaskView.getRepairTypeId().equals("7")) {
            repairTaskView.setVehicleRepairTotalAmount(null);
            repairTaskView.setVehicleReplaceTotalAmount(null);
            repairTaskView.setVehicleInsuranceTotalAmount(null);

        }

        if (CollectionUtils.isNotEmpty(repairTaskView.getReplaceItemDetail())) {

            for (ViewReplaceItemDetailBO viewReplaceItemDetail : repairTaskView.getReplaceItemDetail()) {

                if (StringUtils.isNotBlank(viewReplaceItemDetail.getInsuranceQuoteAmount())
                        && StringUtils.isNotBlank(viewReplaceItemDetail.getViewAmount())
                        && !StringUtils.equals(viewReplaceItemDetail.getInsuranceQuoteAmount(),
                        viewReplaceItemDetail.getViewAmount())) {
                    viewReplaceItemDetail.setIsObjection("1");
                } else {
                    viewReplaceItemDetail.setIsObjection("0");
                }

            }

        }

        if (CollectionUtils.isNotEmpty(repairTaskView.getRepairItemDetail())) {

            for (ViewRepairItemDetailBO viewRepairItemDetail : repairTaskView.getRepairItemDetail()) {

                if (StringUtils.isNotBlank(viewRepairItemDetail.getRepairAmount())
                        && StringUtils.isNotBlank(viewRepairItemDetail.getViewAmount()) && !StringUtils
                        .equals(viewRepairItemDetail.getRepairAmount(), viewRepairItemDetail.getViewAmount())) {
                    viewRepairItemDetail.setIsObjection("1");
                } else {
                    viewRepairItemDetail.setIsObjection("0");
                }
            }

        }

        if (repairTaskView.getCurrentTache() == Contants.CURRENT_TACHE_INSURANCE_QUOTE) {

            // 定损定价时进行计算
            BigDecimal repairRepairTotalAmount = BigDecimal.ZERO;

            BigDecimal repairReplaceTotalAmount = BigDecimal.ZERO;

            BigDecimal repairInsuranceTotalAmount = BigDecimal.ZERO;

            if (StringUtils.isNotBlank(repairTaskView.getRepairRepairTotalAmount())) {
                repairRepairTotalAmount = new BigDecimal(repairTaskView.getRepairRepairTotalAmount());
            }

            if (StringUtils.isNotBlank(repairTaskView.getRepairReplaceTotalAmount())) {
                repairReplaceTotalAmount = new BigDecimal(repairTaskView.getRepairReplaceTotalAmount());
            }

            /*
             * repairInsuranceTotalAmount =
             * repairRepairTotalAmount.add(repairReplaceTotalAmount).setScale(2,
             * BigDecimal.ROUND_HALF_UP);
             * repairTaskView.setRepairInsuranceTotalAmount(
             * repairInsuranceTotalAmount.toString());
             */

            // 未处理操作之后更新状态为处理中
            if (Contants.INSURANCEQUOTEVIEW.equals(type)) {

                if (StringUtils.equals(Contants.INSURANCE_QUOTE_UNTREATED + StringUtils.EMPTY,
                        repairTaskView.getInsuranceQuoteTaskSchedule())
//                        || StringUtils.equals(Contants.INSURANCE_QUOTE_REJECT + StringUtils.EMPTY,
//                        repairTaskView.getInsuranceQuoteTaskSchedule())
                        || StringUtils.equals(Contants.INSURANCE_QUOTE_PROCESSING + StringUtils.EMPTY,
                        repairTaskView.getInsuranceQuoteTaskSchedule())) {

                    RepairTask repairTask = new RepairTask();

                    repairTask.setUpdateOperId(operId);
                    repairTask.setUpdateOperName(operName);
                    repairTask.setId(id);
                    repairTask.setInsuranceQuoteTaskSchedule(Long.valueOf(Contants.INSURANCE_QUOTE_PROCESSING));

                    // 保存
                    repairTaskMapper.updateRepairTask(repairTask);
                }
            }

            if (StringUtils.isNotBlank(repairTaskView.getRepairInsuranceTotalAmount())
                    && StringUtils.isNotBlank(repairTaskView.getRepairReplaceTotalAmount())
                    && StringUtils.isNotBlank(repairTaskView.getRepairRepairTotalAmount())) {
                repairTaskView.setRepairOtherTotalAmount(new BigDecimal(repairTaskView.getRepairInsuranceTotalAmount())
                        .subtract(new BigDecimal(repairTaskView.getRepairReplaceTotalAmount()))
                        .subtract(new BigDecimal(repairTaskView.getRepairRepairTotalAmount())).toString());
            }
            if (StringUtils.isNotBlank(repairTaskView.getVehicleInsuranceTotalAmount())
                    && StringUtils.isNotBlank(repairTaskView.getVehicleReplaceTotalAmount())
                    && StringUtils.isNotBlank(repairTaskView.getVehicleRepairTotalAmount())) {
                repairTaskView
                        .setVehicleOtherTotalAmount(new BigDecimal(repairTaskView.getVehicleInsuranceTotalAmount())
                                .subtract(new BigDecimal(repairTaskView.getVehicleReplaceTotalAmount()))
                                .subtract(new BigDecimal(repairTaskView.getVehicleRepairTotalAmount())).toString());
            }
        }

        if (StringUtils.isNotBlank(repairTaskView.getAccidentNo())) {
            AccidentInfoDetailDTO accidentInfoDetailDTO = manualUtils.getAccidentInfoDetail(repairTaskView.getAccidentNo(), ComUtil.getUserInfo(request).getToken());
            if (accidentInfoDetailDTO != null && CollectionUtils.isNotEmpty(accidentInfoDetailDTO.getAccidentFileList())){
                List<AccidentFileDataDTO> accidentFileList = accidentInfoDetailDTO.getAccidentFileList();
                Map<String, List<AccidentFileDataDTO>> map = accidentFileList.stream().collect(Collectors.groupingBy(x -> x.getAccidentNo()));
                List<AccidentCaseFileDataDTO> accidentCaseFileDataList = Lists.newArrayList();
                map.forEach((caseNo,list)->{
                    if(StringUtils.isNotBlank(caseNo) && caseNo.startsWith("BA")){
                        AccidentCaseFileDataDTO fileDataDTO = new AccidentCaseFileDataDTO();
                        fileDataDTO.setCaseNo(caseNo);
                        fileDataDTO.setAccidentFileList(list);
                        accidentCaseFileDataList.add(fileDataDTO);
                    }
                    // 事故照片
                    if (StringUtils.isNotBlank(caseNo) && caseNo.startsWith("SG")){
                        accidentInfoDetailDTO.setAccidentPicList(list);
                    }
                });
                accidentInfoDetailDTO.setAccidentCaseFileDataList(accidentCaseFileDataList);
            }
            repairTaskView.setAccidentInfoDetail(accidentInfoDetailDTO);

            // 事故理赔金额
            AccidentDamageDTO accidentDamageDTO = manualUtils.getAccidentDamage(repairTaskView.getAccidentNo(), ComUtil.getUserInfo(request).getToken());
            repairTaskView.setAccidentDamage(accidentDamageDTO);
        }

        // 查询保单信息
        String sendRepairTime = repairTaskView.getSendRepairTime();
        if (StringUtils.isNotBlank(sendRepairTime)){
            String sendRepairTimeFormat = sendRepairTime.substring(0, 10);
            repairTaskView.setInsuranceInfoList(manualUtils.getInsuranceInfo(repairTaskView.getVin(), sendRepairTimeFormat));
            repairTaskView.setSendRepairTime(sendRepairTimeFormat);
        }

        // 查询梧桐维修照片和情况说明
        WtRepairFileBo wtRepairFile = manualUtils.getWtRepairFileList(repairTaskView.getTaskNo());
        if (wtRepairFile != null){
            repairTaskView.setWtRepairFileList(wtRepairFile.getRepairFiles());
            repairTaskView.setSituationDesc(wtRepairFile.getSituationDesc());
        }


        String result = JSONObject.toJSONString(repairTaskView);
        vo.setData(result);
        return vo;
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public DefaultServiceRespDTO lossAssessment(Long id, HttpServletRequest request) {
        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if (!StringUtils.contains("200,210,240", repairTaskView.getInsuranceQuoteTaskSchedule())) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "当前状态不能进行维修报价");
        }
        // 登录用户信息
        ComModel comModel = ComUtil.getUserInfo(request);
        if (comModel == null) {
            comModel = new ComModel();
        }
        String orgId = comModel.getOrgId();
        String orgName = orgInfoMapper.getOrgName(orgId);
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("001");
        headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        // PACKET > BODY >ReqInfo
        ReqInfoAssessment reqInfoAssessment = new ReqInfoAssessment();
        // 判断初次定损还是核损退回定损
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        for (MtcLossInfo mtcLossInfo : mtcLossInfoList) {
            if (StringUtils.isNotBlank(mtcLossInfo.getAuditHandlerCode())) {
                reqInfoAssessment.setAuditLossFlag("1");
                break;
            }
        }
        reqInfoAssessment.setReturnURL(mtcSystemConfig.getAssessmentBackURL());
        String refreshUrl = StringUtils.replace(mtcSystemConfig.getAssessmentRefreshURL(), "id", id.toString());
        if (StringUtils.equals("2", repairTaskView.getRepairTypeId())
                || StringUtils.equals("6", repairTaskView.getRepairTypeId())
                || StringUtils.equals("9", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "own");
        } else if (StringUtils.equals("3", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "maintenance");
        }
        reqInfoAssessment.setRefreshURL(refreshUrl);
        // PACKET > BODY >EvalLossInfoAssessment
        EvalLossInfoAssessment evalLossInfoAssessment = carDamageRequestMapper.queryEvalLossInfoAssessment(id);
        evalLossInfoAssessment.setComCode(orgId);
        evalLossInfoAssessment.setCompany(orgName);
        evalLossInfoAssessment.setBranchComCode(orgId);
        evalLossInfoAssessment.setBranchComName(orgName);
        // PACKET >BODY>FactoryInfo
        FactoryInfo factoryInfo = carDamageRequestMapper.queryFactoryInfo(id);
        if (StringUtils.isBlank(factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("");
            factoryInfo.setFactoryQualification("");
        } else if (StringUtils.equals("A", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("1");
            factoryInfo.setFactoryQualification("1");
        } else if (StringUtils.equals("B", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("2");
            factoryInfo.setFactoryQualification("1");
        } else {
            factoryInfo.setFactoryType("3");
            factoryInfo.setFactoryQualification("1");
        }
        // PACKET >BODY>FactoryBrand
        FactoryBrand factoryBrand = new FactoryBrand();
        List<FactoryBrand> factoryBrandList = new ArrayList<>();
        factoryBrandList.add(factoryBrand);
        // PACKET > BODY >LossPolicy
        LossPolicy lossPolicy = carDamageRequestMapper.queryLossPolicy(id);
        List<LossPolicy> lossPolicyList = new ArrayList<>();
        lossPolicyList.add(lossPolicy);
        // PACKET > BODY >LossCoverVehicle
        LossCoverVehicle lossCoverVehicle = carDamageRequestMapper.queryLossCoverVehicle(id);
        // PACKET > BODY >LossInsured
        LossInsured lossInsured = carDamageRequestMapper.queryLossInsured(id);
        List<LossInsured> lossInsuredList = new ArrayList<>();
        lossInsuredList.add(lossInsured);
        // PACKET > BODY >LossReporting
        LossReporting lossReporting = carDamageRequestMapper.queryLossReporting(id);
        // PACKET > BODY
        BodyBeanAssessment bodyBeanAssessment = new BodyBeanAssessment();
        bodyBeanAssessment.setReqInfoAssessment(reqInfoAssessment);
        bodyBeanAssessment.setEvalLossInfoAssessment(evalLossInfoAssessment);
        bodyBeanAssessment.setFactoryInfo(factoryInfo);
        bodyBeanAssessment.setFactoryBrandList(factoryBrandList);
        bodyBeanAssessment.setLossPolicyList(lossPolicyList);
        bodyBeanAssessment.setLossCoverVehicle(lossCoverVehicle);
        bodyBeanAssessment.setLossInsuredList(lossInsuredList);
        bodyBeanAssessment.setLossReporting(lossReporting);
        // PACKET
        PacketBeanAssessment packetBean = new PacketBeanAssessment();
        packetBean.setHeadBean(headBean);
        packetBean.setBodyBeanAssessment(bodyBeanAssessment);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBean, stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "001");
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error(repairTaskView.getTaskNo() + "lossAssessment返回为空");
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
        Map<String, String> bodyMap = (Map<String, String>) resultMap.get("BODY");
        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error(repairTaskView.getTaskNo() + "lossAssessment返回:" + JSON.toJSONString(resultMap));
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = new DefaultServiceRespDTO();
        defaultServiceRespDTO.setData(bodyMap.get("URL"));
        return defaultServiceRespDTO;
    }

    @Override
    public DefaultServiceRespDTO lossAssessment(Long id, String parameterString, HttpServletRequest request) {
        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if (!StringUtils.contains("200,210,240", repairTaskView.getInsuranceQuoteTaskSchedule())) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "当前状态不能进行维修报价");
        }
        // 登录用户信息
        ComModel comModel = ComUtil.getUserInfo(request);
        if (comModel == null) {
            comModel = new ComModel();
        }
        String orgId = comModel.getOrgId();
        String orgName = orgInfoMapper.getOrgName(orgId);
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("001");
        headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        // PACKET > BODY >ReqInfo
        ReqInfoAssessment reqInfoAssessment = new ReqInfoAssessment();
        // 判断初次定损还是核损退回定损
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        for (MtcLossInfo mtcLossInfo : mtcLossInfoList) {
            if (StringUtils.isNotBlank(mtcLossInfo.getAuditHandlerCode())) {
                reqInfoAssessment.setAuditLossFlag("1");
                break;
            }
        }
        reqInfoAssessment.setReturnURL(mtcSystemConfig.getAssessmentBackURL());
        String refreshUrl = apolloPropertyUtils.getString("assessmentRefreshURLNew").replace("id", id.toString());
        if (StringUtils.equals("2", repairTaskView.getRepairTypeId())
                || StringUtils.equals("5", repairTaskView.getRepairTypeId())
                || StringUtils.equals("9", repairTaskView.getRepairTypeId())
                || StringUtils.equals("6", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "own");
        } else if (StringUtils.equals("3", repairTaskView.getRepairTypeId())
                || StringUtils.equals("7", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "maintenance");
        }
        refreshUrl = refreshUrl.replace("parameterString", parameterString);
        reqInfoAssessment.setRefreshURL(refreshUrl);
        // PACKET > BODY >EvalLossInfoAssessment
        EvalLossInfoAssessment evalLossInfoAssessment = carDamageRequestMapper.queryEvalLossInfoAssessment(id);
        evalLossInfoAssessment.setComCode(orgId);
        evalLossInfoAssessment.setCompany(orgName);
        evalLossInfoAssessment.setBranchComCode(orgId);
        evalLossInfoAssessment.setBranchComName(orgName);
        // PACKET >BODY>FactoryInfo
        FactoryInfo factoryInfo = carDamageRequestMapper.queryFactoryInfo(id);
        if (StringUtils.isBlank(factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("");
            factoryInfo.setFactoryQualification("");
        } else if (StringUtils.equals("A", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("1");
            factoryInfo.setFactoryQualification("1");
        } else if (StringUtils.equals("B", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("2");
            factoryInfo.setFactoryQualification("1");
        } else {
            factoryInfo.setFactoryType("3");
            factoryInfo.setFactoryQualification("1");
        }
        // PACKET >BODY>FactoryBrand
        FactoryBrand factoryBrand = new FactoryBrand();
        List<FactoryBrand> factoryBrandList = new ArrayList<>();
        factoryBrandList.add(factoryBrand);
        // PACKET > BODY >LossPolicy
        LossPolicy lossPolicy = carDamageRequestMapper.queryLossPolicy(id);
        List<LossPolicy> lossPolicyList = new ArrayList<>();
        lossPolicyList.add(lossPolicy);
        // PACKET > BODY >LossCoverVehicle
        LossCoverVehicle lossCoverVehicle = carDamageRequestMapper.queryLossCoverVehicle(id);
        // PACKET > BODY >LossInsured
        LossInsured lossInsured = carDamageRequestMapper.queryLossInsured(id);
        List<LossInsured> lossInsuredList = new ArrayList<>();
        lossInsuredList.add(lossInsured);
        // PACKET > BODY >LossReporting
        LossReporting lossReporting = carDamageRequestMapper.queryLossReporting(id);
        // PACKET > BODY
        BodyBeanAssessment bodyBeanAssessment = new BodyBeanAssessment();
        bodyBeanAssessment.setReqInfoAssessment(reqInfoAssessment);
        bodyBeanAssessment.setEvalLossInfoAssessment(evalLossInfoAssessment);
        bodyBeanAssessment.setFactoryInfo(factoryInfo);
        bodyBeanAssessment.setFactoryBrandList(factoryBrandList);
        bodyBeanAssessment.setLossPolicyList(lossPolicyList);
        bodyBeanAssessment.setLossCoverVehicle(lossCoverVehicle);
        bodyBeanAssessment.setLossInsuredList(lossInsuredList);
        bodyBeanAssessment.setLossReporting(lossReporting);
        // PACKET
        PacketBeanAssessment packetBean = new PacketBeanAssessment();
        packetBean.setHeadBean(headBean);
        packetBean.setBodyBeanAssessment(bodyBeanAssessment);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBean, stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "001");
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error(repairTaskView.getTaskNo() + "lossAssessment返回为空");
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
        Map<String, String> bodyMap = (Map<String, String>) resultMap.get("BODY");
        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error(repairTaskView.getTaskNo() + "lossAssessment返回:" + JSON.toJSONString(resultMap));
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = new DefaultServiceRespDTO();
        defaultServiceRespDTO.setData(bodyMap.get("URL"));
        return defaultServiceRespDTO;
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public DefaultServiceRespDTO evaluateLoss(Long id, HttpServletRequest request) {
        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if (!StringUtils.contains("300,310,315", repairTaskView.getVerificationLossTaskSchedule())) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "当前状态不能进行核损核价");
        }
        // 检查是否存在定损数据
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
        mtcLossInfoDTO.setStatus(1);
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        if (CollectionUtils.isEmpty(mtcLossInfoList)) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "未经过定损系统维修报价，请退回后重新定损");
        }
        // 登录用户信息
        ComModel comModel = ComUtil.getUserInfo(request);
        if (comModel == null) {
            comModel = new ComModel();
        }
        String orgId = comModel.getOrgId();
        String orgName = orgInfoMapper.getOrgName(orgId);
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("005");
        headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        // PACKET > BODY >ReqInfo
        ReqInfoEvaluate reqInfoEvaluate = new ReqInfoEvaluate();
        reqInfoEvaluate.setReturnURL(mtcSystemConfig.getEvaluateBackURL());
        String refreshUrl = mtcSystemConfig.getEvaluateRefreshURL().replace("id", id.toString())
                .replace("orgCode", repairTaskView.getOrgId())
                .replace("timestamp", String.valueOf(System.currentTimeMillis()));
        if (StringUtils.equals("2", repairTaskView.getRepairTypeId())
                || StringUtils.equals("5", repairTaskView.getRepairTypeId())
                || StringUtils.equals("9", repairTaskView.getRepairTypeId())
                || StringUtils.equals("6", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "own");
        }
        else if (StringUtils.equals("3", repairTaskView.getRepairTypeId())
                || StringUtils.equals("7", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "maintenance");
        }
        reqInfoEvaluate.setRefreshURL(refreshUrl);
        // PACKET > BODY >EvalLossInfo
        EvalLossInfoEvaluate evalLossInfoEvaluate = carDamageRequestMapper.queryEvalLossInfoEvaluate(id);
        evalLossInfoEvaluate.setApprComCode(orgId);
        evalLossInfoEvaluate.setApprCompany(orgName);
        evalLossInfoEvaluate.setApprBranchComCode(orgId);
        evalLossInfoEvaluate.setApprBranchComName(orgName);
        evalLossInfoEvaluate.setApprHandlerCode(orgId);
        evalLossInfoEvaluate.setApprHandlerName(orgName);
        // PACKET > BODY
        BodyBeanEvaluate bodyBeanEvaluate = new BodyBeanEvaluate();
        bodyBeanEvaluate.setReqInfoEvaluate(reqInfoEvaluate);
        bodyBeanEvaluate.setEvalLossInfoEvaluate(evalLossInfoEvaluate);
        // PACKET
        PacketBeanEvaluate packetBeanEvaluate = new PacketBeanEvaluate();
        packetBeanEvaluate.setHeadBean(headBean);
        packetBeanEvaluate.setBodyBeanEvaluate(bodyBeanEvaluate);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBeanEvaluate, stringWriter);
        System.out.println(stringWriter.toString());
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "005");
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error(repairTaskView.getTaskNo() + "evaluateLoss返回为空");
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
        Map<String, String> bodyMap = (Map<String, String>) resultMap.get("BODY");
        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error(repairTaskView.getTaskNo() + "evaluateLoss返回:" + JSON.toJSONString(resultMap));
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = new DefaultServiceRespDTO();
        defaultServiceRespDTO.setData(bodyMap.get("URL"));
        return defaultServiceRespDTO;
    }

    @Override
    public DefaultServiceRespDTO evaluateLoss(Long id, String parameterString, HttpServletRequest request) {
        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if (!StringUtils.contains("300,310,315", repairTaskView.getVerificationLossTaskSchedule())) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "当前状态不能进行核损核价");
        }
        // 检查是否存在定损数据
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
        mtcLossInfoDTO.setStatus(1);
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        if (CollectionUtils.isEmpty(mtcLossInfoList)) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "未经过定损系统维修报价，请退回后重新定损");
        }
        // 登录用户信息
        ComModel comModel = ComUtil.getUserInfo(request);
        if (comModel == null) {
            comModel = new ComModel();
        }
        String orgId = comModel.getOrgId();
        String orgName = orgInfoMapper.getOrgName(orgId);
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("005");
        headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        // PACKET > BODY >ReqInfo
        ReqInfoEvaluate reqInfoEvaluate = new ReqInfoEvaluate();
        reqInfoEvaluate.setReturnURL(mtcSystemConfig.getEvaluateBackURL());
        String refreshUrl = apolloPropertyUtils.getString("evaluateRefreshURLNew").replace("id", id.toString());
        if (StringUtils.equals("2", repairTaskView.getRepairTypeId())
                || StringUtils.equals("5", repairTaskView.getRepairTypeId())
                || StringUtils.equals("9", repairTaskView.getRepairTypeId())
                || StringUtils.equals("6", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "own");
        }
        else if (StringUtils.equals("3", repairTaskView.getRepairTypeId())
                || StringUtils.equals("7", repairTaskView.getRepairTypeId())) {
            refreshUrl = StringUtils.replace(refreshUrl, "insurance", "maintenance");
        }
        refreshUrl = refreshUrl.replace("parameterString", parameterString);
        reqInfoEvaluate.setRefreshURL(refreshUrl);
        // PACKET > BODY >EvalLossInfo
        EvalLossInfoEvaluate evalLossInfoEvaluate = carDamageRequestMapper.queryEvalLossInfoEvaluate(id);
        evalLossInfoEvaluate.setApprComCode(orgId);
        evalLossInfoEvaluate.setApprCompany(orgName);
        evalLossInfoEvaluate.setApprBranchComCode(orgId);
        evalLossInfoEvaluate.setApprBranchComName(orgName);
        evalLossInfoEvaluate.setApprHandlerCode(orgId);
        evalLossInfoEvaluate.setApprHandlerName(orgName);
        // PACKET > BODY
        BodyBeanEvaluate bodyBeanEvaluate = new BodyBeanEvaluate();
        bodyBeanEvaluate.setReqInfoEvaluate(reqInfoEvaluate);
        bodyBeanEvaluate.setEvalLossInfoEvaluate(evalLossInfoEvaluate);
        // PACKET
        PacketBeanEvaluate packetBeanEvaluate = new PacketBeanEvaluate();
        packetBeanEvaluate.setHeadBean(headBean);
        packetBeanEvaluate.setBodyBeanEvaluate(bodyBeanEvaluate);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBeanEvaluate, stringWriter);
        System.out.println(stringWriter.toString());
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "005");
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error(repairTaskView.getTaskNo() + "evaluateLoss返回为空");
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");


        Map<String, String> bodyMap = (Map<String, String>) resultMap.get("BODY");
        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error(repairTaskView.getTaskNo() + "evaluateLoss返回:" + JSON.toJSONString(resultMap));
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = new DefaultServiceRespDTO();
        defaultServiceRespDTO.setData(bodyMap.get("URL"));
        return defaultServiceRespDTO;
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public DefaultServiceRespDTO viewLoss(Long id, ComModel comModel) {
        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        // 检查是否存在定损数据
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
        mtcLossInfoDTO.setStatus(1);
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        if (CollectionUtils.isEmpty(mtcLossInfoList)) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "未经过定损系统维修报价，不能查看明细");
        }
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("014");
        headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        // PACKET > BODY >EvalLossInfo
        EvalLossInfoView evalLossInfoView = new EvalLossInfoView();
        evalLossInfoView.setDmgVhclId(repairTaskView.getTaskNo());
        evalLossInfoView.setLossNo(repairTaskView.getTaskNo());
        evalLossInfoView.setReportCode(repairTaskView.getTaskNo());
        // PACKET > BODY
        BodyBeanView bodyBeanView = new BodyBeanView();
        bodyBeanView.setEvalLossInfoView(evalLossInfoView);
        // PACKET
        PacketBeanView packetBeanView = new PacketBeanView();
        packetBeanView.setHeadBean(headBean);
        packetBeanView.setBodyBeanView(bodyBeanView);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBeanView, stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "014");
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error(repairTaskView.getTaskNo() + "viewLoss返回为空");
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
        Map<String, String> bodyMap = (Map<String, String>) resultMap.get("BODY");
        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error(repairTaskView.getTaskNo() + "viewLoss返回:" + JSON.toJSONString(resultMap));
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "调用失败");
        }
        DefaultServiceRespDTO defaultServiceRespDTO = new DefaultServiceRespDTO();
        defaultServiceRespDTO.setData(bodyMap.get("URL"));
        return defaultServiceRespDTO;
    }

    /**
     * 跳过精友回调：将所有定损明细置为核损通过（check_state='01'）
     */
    @Override
    public DefaultServiceRespDTO evaluateLossSkipJingyou(Long id, HttpServletRequest request) {
        DefaultServiceRespDTO resp = new DefaultServiceRespDTO();

        // 登录用户信息
        ComModel comModel = ComUtil.getUserInfo(request);

        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if(null == repairTaskView) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "未找到维修任务");
        }
        String taskNo = repairTaskView.getTaskNo();

        try {
            // 批量更新为核损通过：check_state = '01'
            mtcLossFitInfoMapper.approveByTaskNo(taskNo);
            mtcLossRepairInfoMapper.approveByTaskNo(taskNo);
            mtcLossOuterRepairInfoMapper.approveByTaskNo(taskNo);
            mtcLossAssistInfoMapper.approveByTaskNo(taskNo);
            mtcLossRepairSumInfoMapper.approveByTaskNo(taskNo);
            mtcLossInfoMapper.updateAuditHandlerCode(taskNo);

            // 修改核损金额
            RepairTask repairTask = new RepairTask();
            repairTask.setTaskNo(taskNo);
            repairTask.setUpdateOperId(comModel.getUpdateOperId());
            repairTask.setUpdateOperName(comModel.getUserName());
            repairTask.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            repairTask.setVehicleRepairTotalAmount(new BigDecimal(repairTaskView.getRepairRepairTotalAmount()));
            repairTask.setVehicleReplaceTotalAmount(new BigDecimal(repairTaskView.getRepairReplaceTotalAmount()));
            repairTask.setVehicleInsuranceTotalAmount(new BigDecimal(repairTaskView.getRepairInsuranceTotalAmount()));
            repairTaskMapper.updateByIdSelective(repairTask);

            resp.setCode(Contants.RETURN_SUCCESS_CODE);
            resp.setMessage("已将任务所有明细标记为核损通过");
            return resp;
        } catch (Exception ex) {
            log.error("evaluateLossSkipJingyou 处理失败, taskNo=" + taskNo, ex);
            return new DefaultServiceRespDTO(-1, "处理失败：" + ex.getMessage());
        }
    }

    /**
     * 汇总接口：根据开关调用旧/新逻辑
     */
    @Override
    public DefaultServiceRespDTO evaluateLossWithSwitch(Long id, HttpServletRequest request) {
        if (isEvaluateLossSlipJingyou()) {
            return evaluateLossSkipJingyou(id, request);
        } else {
            return evaluateLoss(id, request);
        }
    }

    /**
     * 汇总接口：根据开关调用旧/新逻辑（带 parameterString）
     */
    @Override
    public DefaultServiceRespDTO evaluateLossWithSwitch(Long id, String parameterString, HttpServletRequest request) {
        if (isEvaluateLossSlipJingyou()) {
            return evaluateLossSkipJingyou(id, request);
        } else {
            return evaluateLoss(id, parameterString, request);
        }
    }

    /**
     * 通过 Apollo 读取功能开关（支持运行时动态生效）
     */
    private boolean isEvaluateLossSlipJingyou() {
        String v = apolloPropertyUtils.getString("feature.evaluateLoss.new");
        log.info("isEvaluateLossSlipJingyou, feature.evaluateLoss.new={}", v);
        return "true".equalsIgnoreCase(v) || "1".equals(v);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PacketResponse lossAssessmentBack(PacketBeanAssBack packetBeanAssBack) {
        // 返回数据
        PacketResponse packetResponse = new PacketResponse();
        HeadBeanResponse headBeanResponse = new HeadBeanResponse();
        packetResponse.setHeadBeanResponse(headBeanResponse);
        // 回调数据
        HeadBeanAssBack headBeanAssBack = packetBeanAssBack.getHeadBeanAssBack();
        BodyBeanAssBack bodyBeanAssBack = packetBeanAssBack.getBodyBeanAssBack();
        EvalLossInfoAssBack evalLossInfoAssBack = bodyBeanAssBack.getEvalLossInfoAssBack();
        // 接口校验
        if (!StringUtils.equals("jy", headBeanAssBack.getUserCode())
                || !StringUtils.equals("jy", headBeanAssBack.getPassword())) {
            headBeanResponse.setResponseCode("400");
            headBeanResponse.setErrorMessage("系统认证错误");
            return packetResponse;
        }
        if (StringUtils.isBlank(evalLossInfoAssBack.getLossNo())) {
            headBeanResponse.setResponseCode("402");
            headBeanResponse.setErrorMessage("字段：LossNo 是必传字段，不可为空;");
            return packetResponse;
        }
        if (!StringUtils.equals("009", headBeanAssBack.getRequestType())) {
            headBeanResponse.setResponseCode("403");
            headBeanResponse.setErrorMessage("请求类型错误");
            return packetResponse;
        }
        try {
            MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
            mtcLossInfoDTO.setTaskNo(evalLossInfoAssBack.getLossNo());
            List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
            if (CollectionUtils.isEmpty(mtcLossInfoList)) {
                // 第一次定损，新增
                saveLossAssBackData(packetBeanAssBack);
            } else if (mtcLossInfoList.size() == 1) {
                MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
                if (StringUtils.isNotBlank(mtcLossInfo.getAuditHandlerCode())) {
                    // 核损退回定损，修改第一次为无效，新增
                    updateLossAssBackData(evalLossInfoAssBack.getLossNo(), 1);
                } else {
                    // 第一次定损，删除后新增
                    updateLossAssBackData(evalLossInfoAssBack.getLossNo(), 2);
                }
                saveLossAssBackData(packetBeanAssBack);
            } else {
                // 第三次或三次以上定损，删除最新一次，新增
                updateLossAssBackData(evalLossInfoAssBack.getLossNo(), 2);
                saveLossAssBackData(packetBeanAssBack);
            }
            // 修改定损金额
            RepairTask repairTask = new RepairTask();
            repairTask.setTaskNo(evalLossInfoAssBack.getLossNo());
            repairTask.setUpdateOperId(-1L);
            repairTask.setUpdateOperName("定损系统");
            repairTask.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            repairTask.setRepairReplaceTotalAmount(evalLossInfoAssBack.getEvalPartSum());
            repairTask.setRepairRepairTotalAmount(evalLossInfoAssBack.getEvalRepairSum());
            repairTask.setRepairInsuranceTotalAmount(evalLossInfoAssBack.getSumLossAmount());
            RepairTask repairTaskBO = repairTaskMapper.selectByTaskNo(evalLossInfoAssBack.getLossNo());
            if (repairTaskBO != null && repairTaskBO.getOrigin() == 1
                    && StringUtils.isNotBlank(evalLossInfoAssBack.getTravelMileAges())) {
                if (!new BigDecimal(evalLossInfoAssBack.getTravelMileAges()).equals(BigDecimal.ZERO)) {
                    repairTask.setTotalMileage(new BigDecimal(evalLossInfoAssBack.getTravelMileAges()));
                }
            }
            repairTaskMapper.updateByIdSelective(repairTask);
            try {
                // 损失数据保存后 更新占据预算
                MtcBudgetManagement budgetManagement = mtcBudgetManagementMapper
                        .selectByOrgIdAndYear(repairTaskBO.getOrgId(), ComUtil.getCurrentYear());
                if (budgetManagement != null) {
                    if (evalLossInfoAssBack.getSumLossAmount() != null) {
                        // 如果原定损金额不为0或者null，需要减去原定损金额
                        if (repairTaskBO.getRepairInsuranceTotalAmount() != null) {
                            budgetManagement.setOccupyBudget(budgetManagement.getOccupyBudget()
                                    .subtract(repairTaskBO.getRepairInsuranceTotalAmount()));
                        }
                        // 占据金额加上当前保存的金额
                        budgetManagement.setOccupyBudget(
                                budgetManagement.getOccupyBudget().add(evalLossInfoAssBack.getSumLossAmount()));
                        if (budgetManagement.getOccupyBudget().doubleValue() < 0) {
                            budgetManagement.setOccupyBudget(new BigDecimal(0));
                        }
                        budgetManagement.setUpdateOperId(-1L);
                        budgetManagement.setUpdateOperName("定损系统");
                        budgetManagement.setUpdateTime(new Date());
                        mtcBudgetManagementMapper.updateByPrimaryKeySelective(budgetManagement);
                    }
                }
            } catch (Exception e) {
                log.error("定损更新占据预算异常", e);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("lossAssessmentBack error", e);
            headBeanResponse.setResponseCode("499");
            headBeanResponse.setErrorMessage("其它异常错误");
            return packetResponse;
        }
        log.warn("lossAssessmentBack return--------------------->" + JSON.toJSONString(packetResponse));
        return packetResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PacketResponse evaluateLossBack(PacketBeanEvaBack packetBeanEvaBack) {
        // 返回数据
        PacketResponse packetResponse = new PacketResponse();
        HeadBeanResponse headBeanResponse = new HeadBeanResponse();
        packetResponse.setHeadBeanResponse(headBeanResponse);
        // 回调数据
        HeadBeanEvaBack headBeanEvaBack = packetBeanEvaBack.getHeadBeanEvaBack();
        BodyBeanEvaBack bodyBeanEvaBack = packetBeanEvaBack.getBodyBeanEvaBack();
        EvalLossInfoEvaBack evalLossInfoEvaBack = bodyBeanEvaBack.getEvalLossInfoEvaBack();
        List<LossFitInfoEvaBack> lossFitInfoEvaBackList = bodyBeanEvaBack.getLossFitInfoEvaBackList();
        List<LossRepairInfoEvaBack> lossRepairInfoEvaBackList = bodyBeanEvaBack.getLossRepairInfoEvaBackList();
        List<LossOuterRepairInfoEvaBack> lossOuterRepairInfoEvaBackList = bodyBeanEvaBack
                .getLossOuterRepairInfoEvaBackList();
        List<LossRepairSumInfoEvaBack> lossRepairSumInfoEvaBackList = bodyBeanEvaBack.getLossRepairSumInfoEvaBackList();
        List<LossAssistInfoEvaBack> lossAssistInfoEvaBackList = bodyBeanEvaBack.getLossAssistInfoEvaBackList();
        // 接口校验
        if (!StringUtils.equals("jy", headBeanEvaBack.getUserCode())
                || !StringUtils.equals("jy", headBeanEvaBack.getPassword())) {
            headBeanResponse.setResponseCode("400");
            headBeanResponse.setErrorMessage("系统认证错误");
            return packetResponse;
        }
        if (StringUtils.isBlank(evalLossInfoEvaBack.getLossNo())) {
            headBeanResponse.setResponseCode("402");
            headBeanResponse.setErrorMessage("字段：LossNo 是必传字段，不可为空;");
            return packetResponse;
        }
        if (!StringUtils.equals("006", headBeanEvaBack.getRequestType())) {
            headBeanResponse.setResponseCode("403");
            headBeanResponse.setErrorMessage("请求类型错误");
            return packetResponse;
        }
        try {
            // 更新数据
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            MtcLossInfo mtcLossInfo = new MtcLossInfo();
            BeanCopyUtils.copyProperties(evalLossInfoEvaBack, mtcLossInfo);
            mtcLossInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
            mtcLossInfo.setAuditSelfPaySum(evalLossInfoEvaBack.getSelfPaySum());
            mtcLossInfo.setAuditOuterSum(evalLossInfoEvaBack.getOuterSum());
            mtcLossInfo.setAuditDerogationSum(evalLossInfoEvaBack.getDerogationSum());
            mtcLossInfo.setAuditHandlerCode(evalLossInfoEvaBack.getHandlerCode());
            mtcLossInfo.setAuditRemark(evalLossInfoEvaBack.getRemark());
            mtcLossInfo.setAuditAllLoseSum(evalLossInfoEvaBack.getAllLoseSum());
            mtcLossInfo.setAuditAllLoseRemainsSum(evalLossInfoEvaBack.getAllLoseRemainsSum());
            mtcLossInfo.setAuditAllLoseSalvSum(evalLossInfoEvaBack.getAllLoseSalvSum());
            mtcLossInfo.setAuditAllLoseTotalSum(evalLossInfoEvaBack.getAllLoseTotalSum());
            mtcLossInfo.setUpdateOperId(-1L);
            mtcLossInfo.setUpdateOperName("定损系统");
            mtcLossInfo.setUpdateTime(timestamp);
            mtcLossInfoMapper.updateByIdSelective(mtcLossInfo);
            for (LossFitInfoEvaBack lossFitInfoEvaBack : lossFitInfoEvaBackList) {
                MtcLossFitInfo mtcLossFitInfo = new MtcLossFitInfo();
                BeanCopyUtils.copyProperties(lossFitInfoEvaBack, mtcLossFitInfo);
                mtcLossFitInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossFitInfo.setAuditRemark(lossFitInfoEvaBack.getRemark());
                mtcLossFitInfo.setAuditRecheckFlag(lossFitInfoEvaBack.getRecheckFlag());
                mtcLossFitInfo.setUpdateOperId(-1L);
                mtcLossFitInfo.setUpdateOperName("定损系统");
                mtcLossFitInfo.setUpdateTime(timestamp);
                mtcLossFitInfoMapper.updateByIdSelective(mtcLossFitInfo);
            }
            for (LossRepairInfoEvaBack lossRepairInfoEvaBack : lossRepairInfoEvaBackList) {
                MtcLossRepairInfo mtcLossRepairInfo = new MtcLossRepairInfo();
                BeanCopyUtils.copyProperties(lossRepairInfoEvaBack, mtcLossRepairInfo);
                mtcLossRepairInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossRepairInfo.setAuditRemark(lossRepairInfoEvaBack.getRemark());
                mtcLossRepairInfo.setUpdateOperId(-1L);
                mtcLossRepairInfo.setUpdateOperName("定损系统");
                mtcLossRepairInfo.setUpdateTime(timestamp);
                mtcLossRepairInfoMapper.updateByIdSelective(mtcLossRepairInfo);
            }
            for (LossOuterRepairInfoEvaBack lossOuterRepairInfoEvaBack : lossOuterRepairInfoEvaBackList) {
                MtcLossOuterRepairInfo mtcLossOuterRepairInfo = new MtcLossOuterRepairInfo();
                BeanCopyUtils.copyProperties(lossOuterRepairInfoEvaBack, mtcLossOuterRepairInfo);
                mtcLossOuterRepairInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossOuterRepairInfo.setAuditRepairHandaddFlag(lossOuterRepairInfoEvaBack.getRepairHandaddFlag());
                mtcLossOuterRepairInfo.setAuditEvalOuterPirce(lossOuterRepairInfoEvaBack.getEvalOuterPirce());
                mtcLossOuterRepairInfo.setAuditDerogationPrice(lossOuterRepairInfoEvaBack.getDerogationPrice());
                mtcLossOuterRepairInfo.setAuditDerogationItemName(lossOuterRepairInfoEvaBack.getDerogationItemName());
                mtcLossOuterRepairInfo.setAuditDerogationItemCode(lossOuterRepairInfoEvaBack.getDerogationItemCode());
                mtcLossOuterRepairInfo.setAuditDerogationPriceType(lossOuterRepairInfoEvaBack.getDerogationPriceType());
                mtcLossOuterRepairInfo.setAuditPartPrice(lossOuterRepairInfoEvaBack.getPartPrice());
                mtcLossOuterRepairInfo.setAuditRepairFactoryId(lossOuterRepairInfoEvaBack.getRepairFactoryId());
                mtcLossOuterRepairInfo.setAuditRepairFactoryName(lossOuterRepairInfoEvaBack.getRepairFactoryName());
                mtcLossOuterRepairInfo.setAuditRepairFactoryCode(lossOuterRepairInfoEvaBack.getRepairFactoryCode());
                mtcLossOuterRepairInfo.setAuditItemCoverCode(lossOuterRepairInfoEvaBack.getItemCoverCode());
                mtcLossOuterRepairInfo.setAuditRemark(lossOuterRepairInfoEvaBack.getRemark());
                mtcLossOuterRepairInfo.setAuditRepairOuterSum(lossOuterRepairInfoEvaBack.getRepairOuterSum());
                mtcLossOuterRepairInfo.setAuditReferencePartPrice(lossOuterRepairInfoEvaBack.getReferencePartPrice());
                mtcLossOuterRepairInfo.setAuditOutItemAmount(lossOuterRepairInfoEvaBack.getOutItemAmount());
                mtcLossOuterRepairInfo.setUpdateOperId(-1L);
                mtcLossOuterRepairInfo.setUpdateOperName("定损系统");
                mtcLossOuterRepairInfo.setUpdateTime(timestamp);
                mtcLossOuterRepairInfoMapper.updateByIdSelective(mtcLossOuterRepairInfo);
            }
            for (LossRepairSumInfoEvaBack lossRepairSumInfoEvaBack : lossRepairSumInfoEvaBackList) {
                MtcLossRepairSumInfo mtcLossRepairSumInfo = new MtcLossRepairSumInfo();
                BeanCopyUtils.copyProperties(lossRepairSumInfoEvaBack, mtcLossRepairSumInfo);
                mtcLossRepairSumInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossRepairSumInfo.setAuditItemCount(lossRepairSumInfoEvaBack.getItemCount());
                mtcLossRepairSumInfo.setUpdateOperId(-1L);
                mtcLossRepairSumInfo.setUpdateOperName("定损系统");
                mtcLossRepairSumInfo.setUpdateTime(timestamp);
                mtcLossRepairSumInfoMapper.updateByIdSelective(mtcLossRepairSumInfo);
            }
            for (LossAssistInfoEvaBack lossAssistInfoEvaBack : lossAssistInfoEvaBackList) {
                MtcLossAssistInfo mtcLossAssistInfo = new MtcLossAssistInfo();
                BeanCopyUtils.copyProperties(lossAssistInfoEvaBack, mtcLossAssistInfo);
                mtcLossAssistInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossAssistInfo.setAuditRemark(lossAssistInfoEvaBack.getRemark());
                mtcLossAssistInfo.setUpdateOperId(-1L);
                mtcLossAssistInfo.setUpdateOperName("定损系统");
                mtcLossAssistInfo.setUpdateTime(timestamp);
                mtcLossAssistInfoMapper.updateByIdSelective(mtcLossAssistInfo);
            }
            // 修改核损金额
            RepairTask repairTask = new RepairTask();
            repairTask.setTaskNo(evalLossInfoEvaBack.getLossNo());
            repairTask.setUpdateOperId(-1L);
            repairTask.setUpdateOperName("定损系统");
            repairTask.setUpdateTime(timestamp);
            repairTask.setVehicleReplaceTotalAmount(evalLossInfoEvaBack.getAuditPartSum());
            repairTask.setVehicleRepairTotalAmount(evalLossInfoEvaBack.getAuditRepiarSum());
            repairTask.setVehicleInsuranceTotalAmount(evalLossInfoEvaBack.getTotalSum());
            repairTaskMapper.updateByIdSelective(repairTask);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("evaluateLossBack error", e);
            headBeanResponse.setResponseCode("499");
            headBeanResponse.setErrorMessage("其它异常错误");
            return packetResponse;
        }
        log.warn("evaluateLossBack return--------------------->" + JSON.toJSONString(packetResponse));
        return packetResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO openDoor(Long id, HttpServletRequest request) {
        // 获取任务详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if (repairTaskView == null) {
            return new DefaultServiceRespDTO(-1, "任务不存在，请刷新页面重试");
        }
        if (Integer.parseInt(repairTaskView.getVehicleTransferTaskSchedule()) != Contants.VEHICLE_TRANSFER_COMPLETED) {
            return new DefaultServiceRespDTO(-1, "任务未确认接车，不能开车门");
        }
        if (Integer.parseInt(repairTaskView.getVehicleRepairTaskSchedule()) == Contants.VEHICLE_REPAIR_COMPLETED) {
            return new DefaultServiceRespDTO(-1, "任务已申请验收，不能开车门");
        }
        // 获取登录用户
        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        // 调用开车门服务
        VehicleStaticInfo vehicleStaticInfo = vehicleService.getVehStaticByVin(repairTaskView.getVin());
        if (StringUtils.isNotBlank(vehicleStaticInfo.getTerminalId())) {
            log.info("unlock---->" + vehicleStaticInfo.getTerminalId());
            boolean result = tboxControlService.unlock(vehicleStaticInfo.getTerminalId(), null, null,
                    repairTaskView.getVin(), repairTaskView.getVehicleNo(), "evcard-mtc", comModel.getUserName(),
                    "mtc" + UUID.randomUUID());
            log.info("result---->" + result);
            if (!result) {
                return new DefaultServiceRespDTO(1, "开门失败");
            }
            // 开门成功记录日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark(getCurrentTache(repairTaskView.getCurrentTache().intValue()));
            mtcOperatorLog.setRecoderId(id.toString());
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("开车门");
            mtcOperatorLog.setCurrentTache(repairTaskView.getCurrentTache());
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
        }
        return DefaultServiceRespDTO.SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO closeDoor(Long id, HttpServletRequest request) {
        // 获取任务详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if (repairTaskView == null) {
            return new DefaultServiceRespDTO(-1, "任务不存在，请刷新页面重试");
        }
        if (Integer.parseInt(repairTaskView.getVehicleTransferTaskSchedule()) != Contants.VEHICLE_TRANSFER_COMPLETED) {
            return new DefaultServiceRespDTO(-1, "任务未确认接车，不能关车门");
        }
        if (Integer.parseInt(repairTaskView.getVehicleRepairTaskSchedule()) == Contants.VEHICLE_REPAIR_COMPLETED) {
            return new DefaultServiceRespDTO(-1, "任务已申请验收，不能关车门");
        }
        // 获取登录用户
        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        // 调用关车门服务
        VehicleStaticInfo vehicleStaticInfo = vehicleService.getVehStaticByVin(repairTaskView.getVin());
        if (StringUtils.isNotBlank(vehicleStaticInfo.getTerminalId())) {
            log.info("lock---->" + vehicleStaticInfo.getTerminalId());
            boolean result = tboxControlService.lock(vehicleStaticInfo.getTerminalId(), null, repairTaskView.getVin(),
                    repairTaskView.getVehicleNo(), "evcard-mtc", comModel.getUserName(), "mtc" + UUID.randomUUID());
            log.info("result---->" + result);
            if (!result) {
                return new DefaultServiceRespDTO(1, "关门失败");
            }
            // 关门成功记录日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark(getCurrentTache(repairTaskView.getCurrentTache().intValue()));
            mtcOperatorLog.setRecoderId(id.toString());
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("关车门");
            mtcOperatorLog.setCurrentTache(repairTaskView.getCurrentTache());
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
        }
        return DefaultServiceRespDTO.SUCCESS;
    }

    private void saveLossAssBackData(PacketBeanAssBack packetBeanAssBack) {
        ComModel comModel = new ComModel();
        comModel.setCreateTime(new Timestamp(System.currentTimeMillis()));
        comModel.setCreateOperId(-1L);
        comModel.setCreateOperName(StringUtils.EMPTY);
        BodyBeanAssBack bodyBeanAssBack = packetBeanAssBack.getBodyBeanAssBack();
        EvalLossInfoAssBack evalLossInfoAssBack = bodyBeanAssBack.getEvalLossInfoAssBack();
        VehicleInfoAssBack vehicleInfoAssBack = bodyBeanAssBack.getVehicleInfoAssBack();
        List<CollisionPartsAssBack> collisionPartsAssBackList = bodyBeanAssBack.getCollisionPartsAssBackList();
        List<LossFitInfoAssBack> lossFitInfoAssBackList = bodyBeanAssBack.getLossFitInfoAssBackList();
        List<LossRepairInfoAssBack> lossRepairInfoAssBackList = bodyBeanAssBack.getLossRepairInfoAssBackList();
        List<LossOuterRepairInfoAssBack> lossOuterRepairInfoAssBackList = bodyBeanAssBack
                .getLossOuterRepairInfoAssBackList();
        List<LossRepairSumInfoAssBack> lossRepairSumInfoAssBackList = bodyBeanAssBack.getLossRepairSumInfoAssBackList();
        List<LossAssistInfoAssBack> lossAssistInfoAssBackList = bodyBeanAssBack.getLossAssistInfoAssBackList();
        MtcLossInfo mtcLossInfo = new MtcLossInfo();
        BeanCopyUtils.copyProperties(evalLossInfoAssBack, mtcLossInfo);
        BeanCopyUtils.copyProperties(vehicleInfoAssBack, mtcLossInfo);
        mtcLossInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
        BeanCopyUtils.copyProperties(comModel, mtcLossInfo);
        mtcLossInfoMapper.save(mtcLossInfo);
        List<MtcCollisionParts> mtcCollisionPartsList = new ArrayList<>();
        for (CollisionPartsAssBack collisionPartsAssBack : collisionPartsAssBackList) {
            MtcCollisionParts mtcCollisionParts = new MtcCollisionParts();
            BeanCopyUtils.copyProperties(collisionPartsAssBack, mtcCollisionParts);
            mtcCollisionParts.setTaskNo(evalLossInfoAssBack.getLossNo());
            BeanCopyUtils.copyProperties(comModel, mtcCollisionParts);
            mtcCollisionPartsList.add(mtcCollisionParts);
        }
        if (CollectionUtils.isNotEmpty(mtcCollisionPartsList)) {
            mtcCollisionPartsMapper.batchInsert(mtcCollisionPartsList);
        }
        List<MtcLossFitInfo> mtcLossFitInfoList = new ArrayList<>();
        for (LossFitInfoAssBack lossFitInfoAssBack : lossFitInfoAssBackList) {
            MtcLossFitInfo mtcLossFitInfo = new MtcLossFitInfo();
            BeanCopyUtils.copyProperties(lossFitInfoAssBack, mtcLossFitInfo);
            mtcLossFitInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            BeanCopyUtils.copyProperties(comModel, mtcLossFitInfo);
            mtcLossFitInfoList.add(mtcLossFitInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossFitInfoList)) {
            mtcLossFitInfoMapper.batchInsert(mtcLossFitInfoList);
        }
        List<MtcLossRepairInfo> mtcLossRepairInfoList = new ArrayList<>();
        for (LossRepairInfoAssBack lossRepairInfoAssBack : lossRepairInfoAssBackList) {
            MtcLossRepairInfo mtcLossRepairInfo = new MtcLossRepairInfo();
            BeanCopyUtils.copyProperties(lossRepairInfoAssBack, mtcLossRepairInfo);
            mtcLossRepairInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            BeanCopyUtils.copyProperties(comModel, mtcLossRepairInfo);
            mtcLossRepairInfoList.add(mtcLossRepairInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossRepairInfoList)) {
            mtcLossRepairInfoMapper.batchInsert(mtcLossRepairInfoList);
        }
        List<MtcLossOuterRepairInfo> mtcLossOuterRepairInfoList = new ArrayList<>();
        for (LossOuterRepairInfoAssBack lossOuterRepairInfoAssBack : lossOuterRepairInfoAssBackList) {
            MtcLossOuterRepairInfo mtcLossOuterRepairInfo = new MtcLossOuterRepairInfo();
            BeanCopyUtils.copyProperties(lossOuterRepairInfoAssBack, mtcLossOuterRepairInfo);
            mtcLossOuterRepairInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            BeanCopyUtils.copyProperties(comModel, mtcLossOuterRepairInfo);
            mtcLossOuterRepairInfoList.add(mtcLossOuterRepairInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossOuterRepairInfoList)) {
            mtcLossOuterRepairInfoMapper.batchInsert(mtcLossOuterRepairInfoList);
        }
        List<MtcLossRepairSumInfo> mtcLossRepairSumInfoList = new ArrayList<>();
        for (LossRepairSumInfoAssBack lossRepairSumInfoAssBack : lossRepairSumInfoAssBackList) {
            MtcLossRepairSumInfo mtcLossRepairSumInfo = new MtcLossRepairSumInfo();
            BeanCopyUtils.copyProperties(lossRepairSumInfoAssBack, mtcLossRepairSumInfo);
            mtcLossRepairSumInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            BeanCopyUtils.copyProperties(comModel, mtcLossRepairSumInfo);
            mtcLossRepairSumInfoList.add(mtcLossRepairSumInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossRepairSumInfoList)) {
            mtcLossRepairSumInfoMapper.batchInsert(mtcLossRepairSumInfoList);
        }
        List<MtcLossAssistInfo> mtcLossAssistInfoList = new ArrayList<>();
        for (LossAssistInfoAssBack lossAssistInfoAssBack : lossAssistInfoAssBackList) {
            MtcLossAssistInfo mtcLossAssistInfo = new MtcLossAssistInfo();
            BeanCopyUtils.copyProperties(lossAssistInfoAssBack, mtcLossAssistInfo);
            mtcLossAssistInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            BeanCopyUtils.copyProperties(comModel, mtcLossAssistInfo);
            mtcLossAssistInfoList.add(mtcLossAssistInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossAssistInfoList)) {
            mtcLossAssistInfoMapper.batchInsert(mtcLossAssistInfoList);
        }
    }

    /**
     * @param taskNo 任务编号
     * @param type   操作类型（1：修改 2：删除）
     */
    private void updateLossAssBackData(String taskNo, Integer type) {
        if (type == 1) {
            mtcLossInfoMapper.updateStatus(taskNo);
            mtcCollisionPartsMapper.updateStatus(taskNo);
            mtcLossFitInfoMapper.updateStatus(taskNo);
            mtcLossRepairInfoMapper.updateStatus(taskNo);
            mtcLossOuterRepairInfoMapper.updateStatus(taskNo);
            mtcLossRepairSumInfoMapper.updateStatus(taskNo);
            mtcLossAssistInfoMapper.updateStatus(taskNo);
        } else {
            mtcLossInfoMapper.deleteByTaskNo(taskNo);
            mtcCollisionPartsMapper.deleteByTaskNo(taskNo);
            mtcLossFitInfoMapper.deleteByTaskNo(taskNo);
            mtcLossRepairInfoMapper.deleteByTaskNo(taskNo);
            mtcLossOuterRepairInfoMapper.deleteByTaskNo(taskNo);
            mtcLossRepairSumInfoMapper.deleteByTaskNo(taskNo);
            mtcLossAssistInfoMapper.deleteByTaskNo(taskNo);
        }
    }

    private String getCurrentTache(Integer currentTache) {
        switch (currentTache) {
            case 10:
                return "车辆交接";
            case 20:
                return "维修报价";
            case 30:
                return "核损核价";
            case 40:
                return "改派中";
            case 50:
                return "车辆维修";
            case 60:
                return "车辆验收";
            case 70:
                return "资料收集";
            case 80:
                return "损失登记";
            case 90:
                return "结算管理";
            default:
                return "";
        }
    }

    private String getToken(HttpServletRequest request) {
        String token = StringUtils.EMPTY;
        Cookie[] cookies = request.getCookies();
        int length = cookies.length;
        for (int i = 0; i <length; i++) {
            Cookie cookie = cookies[i];
            if (StringUtils.equals(cookie.getName(), "token")) {
                token = cookie.getValue();
                break;
            }
        }
        return token;
    }
}
