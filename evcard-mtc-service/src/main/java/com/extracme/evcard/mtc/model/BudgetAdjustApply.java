package com.extracme.evcard.mtc.model;

import java.util.Date;

public class BudgetAdjustApply {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.task_no
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private String taskNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.before_application_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private String beforeApplicationId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.budget_adjust_application_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private String budgetAdjustApplicationId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.request_no
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private String requestNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.sync_status
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private Integer syncStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.error_message
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private String errorMessage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.remark
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.create_time
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.create_oper_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column budget_adjust_apply.create_oper_name
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    private String createOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.id
     *
     * @return the value of budget_adjust_apply.id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.id
     *
     * @param id the value for budget_adjust_apply.id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.task_no
     *
     * @return the value of budget_adjust_apply.task_no
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public String getTaskNo() {
        return taskNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.task_no
     *
     * @param taskNo the value for budget_adjust_apply.task_no
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.before_application_id
     *
     * @return the value of budget_adjust_apply.before_application_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public String getBeforeApplicationId() {
        return beforeApplicationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.before_application_id
     *
     * @param beforeApplicationId the value for budget_adjust_apply.before_application_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setBeforeApplicationId(String beforeApplicationId) {
        this.beforeApplicationId = beforeApplicationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.budget_adjust_application_id
     *
     * @return the value of budget_adjust_apply.budget_adjust_application_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public String getBudgetAdjustApplicationId() {
        return budgetAdjustApplicationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.budget_adjust_application_id
     *
     * @param budgetAdjustApplicationId the value for budget_adjust_apply.budget_adjust_application_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setBudgetAdjustApplicationId(String budgetAdjustApplicationId) {
        this.budgetAdjustApplicationId = budgetAdjustApplicationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.request_no
     *
     * @return the value of budget_adjust_apply.request_no
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public String getRequestNo() {
        return requestNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.request_no
     *
     * @param requestNo the value for budget_adjust_apply.request_no
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.sync_status
     *
     * @return the value of budget_adjust_apply.sync_status
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public Integer getSyncStatus() {
        return syncStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.sync_status
     *
     * @param syncStatus the value for budget_adjust_apply.sync_status
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.error_message
     *
     * @return the value of budget_adjust_apply.error_message
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.error_message
     *
     * @param errorMessage the value for budget_adjust_apply.error_message
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.remark
     *
     * @return the value of budget_adjust_apply.remark
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.remark
     *
     * @param remark the value for budget_adjust_apply.remark
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.create_time
     *
     * @return the value of budget_adjust_apply.create_time
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.create_time
     *
     * @param createTime the value for budget_adjust_apply.create_time
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.create_oper_id
     *
     * @return the value of budget_adjust_apply.create_oper_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.create_oper_id
     *
     * @param createOperId the value for budget_adjust_apply.create_oper_id
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column budget_adjust_apply.create_oper_name
     *
     * @return the value of budget_adjust_apply.create_oper_name
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column budget_adjust_apply.create_oper_name
     *
     * @param createOperName the value for budget_adjust_apply.create_oper_name
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }
}