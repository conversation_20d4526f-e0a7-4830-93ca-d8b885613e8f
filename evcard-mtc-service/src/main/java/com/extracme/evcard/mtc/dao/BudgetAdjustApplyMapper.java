package com.extracme.evcard.mtc.dao;

import com.extracme.evcard.mtc.model.BudgetAdjustApply;
import com.extracme.evcard.mtc.model.BudgetAdjustApplyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BudgetAdjustApplyMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int countByExample(BudgetAdjustApplyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int deleteByExample(BudgetAdjustApplyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int insert(BudgetAdjustApply record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int insertSelective(BudgetAdjustApply record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    List<BudgetAdjustApply> selectByExample(BudgetAdjustApplyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    BudgetAdjustApply selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int updateByExampleSelective(@Param("record") BudgetAdjustApply record, @Param("example") BudgetAdjustApplyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int updateByExample(@Param("record") BudgetAdjustApply record, @Param("example") BudgetAdjustApplyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int updateByPrimaryKeySelective(BudgetAdjustApply record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table budget_adjust_apply
     *
     * @mbggenerated Tue Aug 05 15:24:29 CST 2025
     */
    int updateByPrimaryKey(BudgetAdjustApply record);
}