package com.extracme.evcard.mtc.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.imap.base64.Base64;
import com.evcard.ids.provider.api.request.CloseTaskForMtcRequest;
import com.evcard.ids.provider.api.response.ApiBaseResponse;
import com.evcard.ids.provider.api.service.IdsDispatchTaskServiceProvider;
import com.evcard.mtc.provider.dto.RepairTaskViewDTO;
import com.evcard.mtc.provider.service.IMtcRepairTaskService;
import com.extracme.evcard.authority.exception.WarnException;
import com.extracme.evcard.bdp.dto.BdpManageWorkDTO;
import com.extracme.evcard.bdp.dto.BdpMtcTaskInfoDTO;
import com.extracme.evcard.bdp.dto.RentMtcTaskRelationDTO;
import com.extracme.evcard.bdp.dto.VehicleOperateDTO;
import com.extracme.evcard.bdp.service.IBdpMtcTaskInfoService;
import com.extracme.evcard.bdp.service.ILongRentReplaceVehicleService;
import com.extracme.evcard.mtc.bean.BodyBeanSynchronize;
import com.extracme.evcard.mtc.bean.EvalLossInfoSynchronize;
import com.extracme.evcard.mtc.bean.HeadBean;
import com.extracme.evcard.mtc.bean.PacketBeanSynchronize;
import com.extracme.evcard.mtc.bo.*;
import com.extracme.evcard.mtc.common.*;
import com.extracme.evcard.mtc.configuration.MtcSystemConfig;
import com.extracme.evcard.mtc.dao.*;
import com.extracme.evcard.mtc.dto.*;
import com.extracme.evcard.mtc.dto.order.AbstractOrderInfoDTO;
import com.extracme.evcard.mtc.dto.order.LongRentOrderInfoDTO;
import com.extracme.evcard.mtc.enums.ConfirmCarDamageTypeEnum;
import com.extracme.evcard.mtc.enums.CurrentTacheEnum;
import com.extracme.evcard.mtc.listener.event.BaseRepairEvent;
import com.extracme.evcard.mtc.listener.event.VehicleMaintenanceLossAuditEvent;
import com.extracme.evcard.mtc.listener.subject.VehicleMaintenanceLossAuditSubject;
import com.extracme.evcard.mtc.model.*;
import com.extracme.evcard.mtc.service.*;
import com.extracme.evcard.mtc.util.ApolloPropertyUtils;
import com.extracme.evcard.mtc.util.HttpClientUtils;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.rent.dto.IrtRentReplaceVehicleTask;
import com.extracme.evcard.rpc.rent.entity.IrtRentReplaceVehicleTaskBo;
import com.extracme.evcard.rpc.rent.service.IIrtRentReplaceVehicleTaskService;
import com.extracme.evcard.sso.dto.SsoUserBaseInfoDto;
import com.extracme.evcard.sso.service.SsoUserService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXB;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import static com.extracme.evcard.mtc.common.ComUtil.REMARK_REMARK_REDIS_KEY;

@Service
public class VehicleMaintenanceLossAuditPriceServiceImpl implements VehicleMaintenanceLossAuditPriceService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApolloPropertyUtils apolloPropertyUtils;

    @Autowired
    private MtcSystemConfig mtcSystemConfig;

    @Resource
    RepairTaskMapper repairTaskMapper;

    @Resource
    MtcUserMapper mtcUserMapper;

    @Resource
    VehicleRepairPicMapper vehicleRepairPicMapper;

    @Resource
    RepairItemDetailMapper repairItemDetailMapper;

    @Resource
    ReplaceItemDetailMapper replaceItemDetailMapper;

    @Resource
    MtcOperatorLogMapper mtcOperatorLogMapper;

    @Resource
    RepairRemarkMapper repareRemarkMapper;

    @Resource
    RepairDepotInfoMapper repairDepotInfoMapper;

    @Resource
    MtcVerificationLossConfigureMapper mtcVerificationLossConfigureMapper;

    @Resource
    MtcCostRecourseTaskMapper mtcCostRecourseTaskMapper;

    @Resource
    VehicleAdvanceCheckTaskMapper vehicleAdvanceCheckTaskMapper;

    @Resource
    RepairRemarkMapper repairRemarkMapper;

    @Resource
    private OrgInfoMapper orgInfoMapper;

    @Autowired
    private IBdpMtcTaskInfoService bdpMtcTaskInfoService;

    @Resource
    private MtcLossInfoMapper mtcLossInfoMapper;
    @Resource
    private MtcCollisionPartsMapper mtcCollisionPartsMapper;
    @Resource
    private MtcLossFitInfoMapper mtcLossFitInfoMapper;
    @Resource
    private MtcLossRepairInfoMapper mtcLossRepairInfoMapper;
    @Resource
    private MtcLossOuterRepairInfoMapper mtcLossOuterRepairInfoMapper;
    @Resource
    private MtcLossRepairSumInfoMapper mtcLossRepairSumInfoMapper;
    @Resource
    private MtcLossAssistInfoMapper mtcLossAssistInfoMapper;
    @Resource
    private MtcManualTaskMapper mtcManualTaskMapper;

    @Autowired
    private IdsDispatchTaskServiceProvider idsDispatchTaskServiceProvider;
    @Resource
    private VehicleRepairMapper vehicleRepairMapper;
    @Resource
    private MtcBudgetManagementMapper mtcBudgetManagementMapper;

    @Resource
    private MtcApprovalConfigMapper mtcApprovalConfigMapper;

    @Resource
    private IBfcCostService bfcCostService;

    @Resource
    private IMtcRepairItemCheckInfoService mtcRepairItemCheckInfoService;

    @Autowired
    private VehicleMaintenanceLossAuditSubject vehicleMaintenanceLossAuditSubject;

    @Autowired
    private ILongRentReplaceVehicleService rentReplaceVehicleService;

    @Autowired
    private IIrtRentReplaceVehicleTaskService iIrtRentReplaceVehicleTaskService;

    @Resource
    private IMtcRepairTaskService mtcRepairTaskService;

    @Autowired
    private IMessagepushServ iMessagepushServ;

    @Autowired
    private ManualUtils manualUtils;

    @Autowired
    private ReplaceVehicleUtils replaceVehicleUtils;

    @Autowired
    private JedisUtils jedisUtils;

    @Resource
    private RepairTaskService repairTaskService;

    @Resource
    private SsoUserService ssoUserService;

    @Autowired
    private VehicleLeavingFactoryService vehicleLeavingFactoryService;

    @Autowired
    private NewAndOldOrderConversionService newAndOldOrderConversionService;
    
    @Override
    public MaintenanceLossAuditPriceWithCountBO operateMaintenanceLossAuditPrice(Integer pageNum, Integer pageSize,
                                                                                 String orgId, String vehicleNo, String taskNo, String vin, Long vehicleModelSeq, String repairDepotOrgName,
                                                                                 Long repairTypeId, String createTimeStart, String createTimeEnd, String vehicleReciveTimeStart,
                                                                                 String vehicleReciveTimeEnd, Integer resurveyFlag, Integer verificationLossTaskSchedule,
                                                                                 Integer subordinate, String advancedAuditLeve, Integer renttype, List<Integer> renttypeList,
                                                                                 Integer factOperateTag, String accidentNo, Integer reviewToSelFeeFlag, HttpServletRequest request) {

        ComModel comModel = ComUtil.getUserInfo(request);

        // 取得用户名称
        String userName = comModel.getUserName();
        Long updateOperId = comModel.getCreateOperId();

        if (StringUtils.isNotBlank(createTimeStart)) {
            createTimeStart += " 00:00:00";
        }

        if (StringUtils.isNotBlank(createTimeEnd)) {
            createTimeEnd += " 23:59:59";
        }

        if (StringUtils.isNotBlank(vehicleReciveTimeStart)) {
            vehicleReciveTimeStart += " 00:00:00";
        }

        if (StringUtils.isNotBlank(vehicleReciveTimeEnd)) {
            vehicleReciveTimeEnd += " 23:59:59";
        }

        if (StringUtils.isNotBlank(repairDepotOrgName)) {
            repairDepotOrgName = repairDepotOrgName.trim();
        }

        if (StringUtils.isNotBlank(vin)) {
            vin = vin.trim();
        }

        if (StringUtils.isNotBlank(taskNo)) {
            taskNo = taskNo.trim();
        }

        if (StringUtils.isNotBlank(vehicleNo)) {
            vehicleNo = vehicleNo.trim();
        }

        if (StringUtils.isBlank(orgId)) {
            orgId = comModel.getOrgId();
        }

        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId()) == null ? 0
                : mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId());

        subordinate = subordinate == null ? 0 : subordinate;

        PageHelper.startPage(pageNum, pageSize);
        List<MaintenanceLossAuditPriceBO> maintenanceLossAuditPriceList = repairTaskMapper.allMaintenanceLossAuditPrice(
                orgId, vehicleNo, taskNo, vin, vehicleModelSeq, repairDepotOrgName, repairTypeId, createTimeStart,
                createTimeEnd, vehicleReciveTimeStart, vehicleReciveTimeEnd, resurveyFlag, verificationLossTaskSchedule,
                LAPricelevel, updateOperId, subordinate, advancedAuditLeve, renttype, renttypeList, factOperateTag, accidentNo, reviewToSelFeeFlag);

        // 加入翻页信息
        PageInfo<MaintenanceLossAuditPriceBO> maintenanceLossAuditPriceListPage = new PageInfo<>(
                maintenanceLossAuditPriceList);

        // 加入统计信息
        MaintenanceLossAuditPriceCountBO maintenanceLossAuditPriceCountList = repairTaskMapper
                .getMaintenanceLossAuditPriceCount(orgId, vehicleNo, taskNo, vin, vehicleModelSeq, repairDepotOrgName,
                        repairTypeId, createTimeStart, createTimeEnd, vehicleReciveTimeStart, vehicleReciveTimeEnd,
                        resurveyFlag, verificationLossTaskSchedule, LAPricelevel, updateOperId, subordinate,
                        advancedAuditLeve, renttype, renttypeList, factOperateTag, accidentNo, reviewToSelFeeFlag);

        MaintenanceLossAuditPriceWithCountBO maintenanceLossAuditPriceWithCount = new MaintenanceLossAuditPriceWithCountBO();
        maintenanceLossAuditPriceWithCount.setMaintenanceLossAuditPriceListPage(maintenanceLossAuditPriceListPage);
        maintenanceLossAuditPriceWithCount.setUntreate(maintenanceLossAuditPriceCountList.getUntreate());
        maintenanceLossAuditPriceWithCount.setProcessing(maintenanceLossAuditPriceCountList.getProcessing());
        maintenanceLossAuditPriceWithCount.setBack(maintenanceLossAuditPriceCountList.getBack());
        maintenanceLossAuditPriceWithCount.setCompleted(maintenanceLossAuditPriceCountList.getCompleted());
        maintenanceLossAuditPriceWithCount.setSubmit(maintenanceLossAuditPriceCountList.getSubmit());
        maintenanceLossAuditPriceWithCount.setClosed(maintenanceLossAuditPriceCountList.getClosed());
        maintenanceLossAuditPriceWithCount.setVerificationLevel(LAPricelevel);

        if (LAPricelevel >= 2) {
            maintenanceLossAuditPriceWithCount.setTaskPool("2");
        } else {
            maintenanceLossAuditPriceWithCount.setTaskPool("1");
        }
        return maintenanceLossAuditPriceWithCount;
    }

    /**
     * 任务详情一览
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO operateRepairTaskView(Long id, Integer type, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        // 取得换件详细
        List<ViewReplaceItemDetailBO> replaceItemDetailList = repairTaskMapper
                .getReplaceItemDetailList(repairTaskView.getTaskNo());

        // 取得修理详情
        List<ViewRepairItemDetailBO> repairItemDetailList = repairTaskMapper
                .getViewReplaceItemDetailList(repairTaskView.getTaskNo());

        repairTaskView.setReplaceItemDetail(replaceItemDetailList);
        repairTaskView.setRepairItemDetail(repairItemDetailList);

        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        String userName = comModel.getUserName();
        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId()) == null ? 0
                : mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId());
        repairTaskView.setVerificationLevel(LAPricelevel);

        if (repairTaskView.getCurrentTache() == Contants.CURRENT_TACHE_VERIFICATION_LOSS) {
            if (Contants.ZERO_LEVEL.equals(LAPricelevel)) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("您没有核价权限，不可进行核损核价");
                return vo;

            }
            String advancedAuditLeve = repairTaskView.getAdvancedAuditLeve();

            if (type == 1) {
                // 核损级别限制
                if (!StringUtils.equals(advancedAuditLeve, LAPricelevel + StringUtils.EMPTY)) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("现阶段您无法进行核损核价操作");
                    return vo;
                }

                // 任务占据
                if (repairTaskView.getVerificationLossTaskOperId() == null) {
                    RepairTask repairTask = new RepairTask();
                    repairTask.setVerificationLossTaskOperId(operId);
                    repairTask.setUpdateOperId(operId);
                    repairTask.setUpdateOperName(operName);
                    // 任务占据时间不修改
                    // repairTask.setUpdateTime(time);
                    repairTask.setId(id);
                    // 更新任务状态为处理中
                    if (Contants.VERIFICATION_LOSS_UNTREATED == Integer.parseInt(repairTaskView.getVerificationLossTaskSchedule())) {
                        repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.VERIFICATION_LOSS_PROCESSING));
                    }

                    // 保存
                    repairTaskMapper.updatetaskOperId(repairTask);

                    MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
                    mtcProcessLog.setUpdateOperId(operId);
                    mtcProcessLog.setUpdateOperName(operName);
                    mtcProcessLog.setUpdateTime(time);
                    mtcProcessLog.setCreateOperId(operId);
                    mtcProcessLog.setCreateOperName(operName);
                    mtcProcessLog.setCreateTime(time);
                    mtcProcessLog.setRemark("核损核价");
                    mtcProcessLog.setRecoderId(id + StringUtils.EMPTY);
                    mtcProcessLog.setStatus(0);
                    mtcProcessLog.setOpeContent("核损中");
                    mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
                    mtcProcessLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));
                    // 添加流程log
                    mtcOperatorLogMapper.saveSelective(mtcProcessLog);
                } else {
                    if (!operId.equals(repairTaskView.getVerificationLossTaskOperId())) {

                        // 通过id取得用户名称
                        String taskOperName = mtcUserMapper
                                .taskOperName(repairTaskView.getVerificationLossTaskOperId());

                        if (StringUtils.isBlank(taskOperName)) {
                            taskOperName = "其他用户";
                        }

                        vo.setCode(Contants.RETURN_ERROR_CODE);
                        vo.setMessage("当前核损核价任务已被" + taskOperName + "占据");
                        return vo;
                    }

                }

            }
            repairTaskView.setMtcLossFitInfoBOList(mtcLossFitInfoMapper.queryMtcLossFitItemList(repairTaskView.getTaskNo()));
            repairTaskView.setMtcLossRepairInfoBOList(mtcLossRepairInfoMapper.queryMtcLossRepairItemList(repairTaskView.getTaskNo()));
		    repairTaskView.setMtcLossAssistInfoBOList(mtcLossAssistInfoMapper.queryMtcLossAssistItemList(repairTaskView.getTaskNo()));
            repairTaskView.setMtcRepairItemCheckInfoDTOList(mtcRepairItemCheckInfoService.queryCheckListByTaskNo(repairTaskView.getTaskNo()));
            repairTaskView.setReviewRepairItemCheckInfoDTOList(mtcRepairItemCheckInfoService.queryCheckListByTaskNoAndInsurancePreReviewStatus(repairTaskView.getTaskNo(), 1));
            // 查询判断风险提示
            String riskTip = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(repairTaskView.getVin())) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(repairTaskView.getTotalMileage())){
                    BigDecimal totalMileage = new BigDecimal(repairTaskView.getTotalMileage());
                    BigDecimal minTotalMileage = totalMileage.subtract(BigDecimal.valueOf(10000));
                    log.info("id :{}",repairTaskView.getId());
                    log.info("vin :{}",repairTaskView.getVin());
                    log.info("totalMileage :{}",totalMileage);
                    log.info("minTotalMileage :{}",minTotalMileage);
                    // 查询当前车辆任务前一万公里内的任务记录
                    List<RepairTaskViewBO> list = repairTaskMapper.getRepairTaskViewList(Long.valueOf(repairTaskView.getId()),repairTaskView.getVin(),minTotalMileage,totalMileage);
                    boolean repeat = false;
                    if (CollectionUtils.isNotEmpty(list)){
                        List<SettlementReportBO> settlementReportList = new ArrayList<>();
                        SettlementReportBO settlementReportBO;
                        List<String> taskNoList = new ArrayList<>();
                        for (RepairTaskViewBO bo : list) {
                            String taskNo = bo.getTaskNo();
                            settlementReportBO = new SettlementReportBO();
                            settlementReportBO.setTaskNo(taskNo);
                            settlementReportList.add(settlementReportBO);
                            taskNoList.add(taskNo);
                        }

                        List<MtcLossFitInfoBO> mtcLossFitInfoBOList = repairTaskView.getMtcLossFitInfoBOList();
                        if (CollectionUtils.isNotEmpty(mtcLossFitInfoBOList) && !repeat){
                            List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper.queryReplaceItemList(settlementReportList);
                            first:
                            for (MtcLossFitInfo info : mtcLossFitInfoList) {
                                String itemName = info.getItemName();
                                for (MtcLossFitInfoBO mtcLossFitInfo : mtcLossFitInfoBOList) {
                                    if (itemName.equals(mtcLossFitInfo.getItemName())){
                                        log.info("1重复的任务编号 :{},更换项目名称：{}",info.getTaskNo(),itemName);
                                        log.info("itemName11 :{}",itemName);
                                        log.info("itemName12 :{}",mtcLossFitInfo.getItemName());
                                        repeat = true;
                                        break first;
                                    }
                                }
                            }
                        }
                        List<MtcLossRepairInfoBO> mtcLossRepairInfoBOList = repairTaskView.getMtcLossRepairInfoBOList();
                        if (CollectionUtils.isNotEmpty(mtcLossRepairInfoBOList) && !repeat){
                            List<MtcLossRepairInfoBO> mtcLossRepairInfoList = mtcLossRepairInfoMapper.queryRepairItemList(settlementReportList);
                            first:
                            for (MtcLossRepairInfoBO info : mtcLossRepairInfoList) {
                                String itemName = info.getItemName();
                                for (MtcLossRepairInfoBO repairInfo : mtcLossRepairInfoList) {
                                    if (itemName.equals(repairInfo.getItemName())){
                                        log.info("2重复的任务编号 :{},更换项目名称：{}",info.getTaskNo(),itemName);
                                        log.info("itemName21 :{}",itemName);
                                        log.info("itemName22 :{}",repairInfo.getItemName());
                                        repeat = true;
                                        break first;
                                    }
                                }
                            }
                        }
                        List<MtcRepairItemCheckInfoDTO> mtcRepairItemCheckInfoDTOList = repairTaskView.getMtcRepairItemCheckInfoDTOList();
                        if (CollectionUtils.isNotEmpty(mtcRepairItemCheckInfoDTOList) && !repeat){
                            List<MtcRepairItemCheckInfoDTO> mtcRepairItemCheckInfoList = mtcRepairItemCheckInfoService.queryCheckListByTaskNoList(taskNoList);
                            first:
                            for (MtcRepairItemCheckInfoDTO dto : mtcRepairItemCheckInfoList) {
                                String itemName = dto.getItemName();
                                for (MtcRepairItemCheckInfoDTO mtcRepairItemCheckInfo : mtcRepairItemCheckInfoDTOList) {
                                    if (itemName.equals(mtcRepairItemCheckInfo.getItemName())){
                                        log.info("3重复的任务编号 :{},更换项目名称：{}",dto.getTaskNo(),itemName);
                                        log.info("itemName31 :{}",itemName);
                                        log.info("itemName32 :{}",mtcRepairItemCheckInfo.getItemName());
                                        repeat = true;
                                        break first;
                                    }
                                }
                            }
                        }
                        if (repeat){
                            riskTip = "该任务存在车辆一万公里内重复维修项目";
                        }
                    }
                }

                if (StringUtils.isBlank(riskTip)){
                    VehicleRepairRecordQueryBO bo = new VehicleRepairRecordQueryBO();
                    bo.setVin(repairTaskView.getVin());
                    bo.setId(id);
                    List<VehicleRepairRecordBO> recordList = vehicleRepairMapper.queryVehicleRepairRecord(bo);
                    int repairCount = 0;
                    int amountCount = 0;
                    boolean repeat = false;
                    // 历史车辆维修数大于1
                    if (CollectionUtils.isNotEmpty(recordList) && recordList.size() > 0) {
                        if (recordList.size() > 1) {
                            first:
                            for (int i = 0; i < recordList.size() - 1; i++) {
                                VehicleRepairRecordBO record = recordList.get(i);
                                Date taskInflowTime = record.getTaskInflowTime();
                                Date vehicleCheckTime = record.getVehicleCheckTime();
                                for (int j = i + 1; j < recordList.size(); j++) {
                                    VehicleRepairRecordBO temp = recordList.get(j);
                                    if (vehicleCheckTime == null || temp.getVehicleCheckTime() == null) {
                                        repeat = true;
                                        break first;
                                    }
                                    if (!record.getId().equals(temp.getId())
                                            && taskInflowTime.getTime() <= temp.getVehicleCheckTime().getTime()
                                            && vehicleCheckTime.getTime() >= temp.getTaskInflowTime().getTime()) {
                                        repeat = true;
                                        break first;
                                    }
                                }
                            }
                        }
                        if (repeat) {
                            riskTip = "该车辆存在重复维修的任务！";
                        } else {
                            for (int i = 0; i < recordList.size(); i++) {
                                // 维修金额大于等于2000
                                if (recordList.get(i).getVehicleRepairTotalAmount().doubleValue() >= 2000) {
                                    amountCount += 1;
                                }
                                // 30天内
                                Date now = new Date();
                                if (now.getTime() - recordList.get(i).getTaskInflowTime().getTime() <= 30 * 24 * 60 * 60 * 1000) {
                                    repairCount += 1;
                                }
                            }
                            if (amountCount >= 2) {
                                riskTip = String.format("该车辆已经累计%S次维修金额超出2000元！", amountCount);
                            } else if (repairCount > 0) {
                                riskTip = String.format("该车辆在30天内已经维修了%S次！", repairCount);
                            }
                        }
                    }
                }
            }
            repairTaskView.setRiskTip(riskTip);
        }

        String repairOtherTotalAmount = null;
        String vehicleOtherTotalAmount = null;
        if (StringUtils.isNotBlank(repairTaskView.getRepairInsuranceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getRepairReplaceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getRepairRepairTotalAmount())) {
            repairOtherTotalAmount = new BigDecimal(repairTaskView.getRepairInsuranceTotalAmount())
                    .subtract(new BigDecimal(repairTaskView.getRepairReplaceTotalAmount()))
                    .subtract(new BigDecimal(repairTaskView.getRepairRepairTotalAmount())).toString();
        }
        if (StringUtils.isNotBlank(repairTaskView.getVehicleInsuranceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getVehicleReplaceTotalAmount())
                && StringUtils.isNotBlank(repairTaskView.getVehicleRepairTotalAmount())) {
            vehicleOtherTotalAmount = new BigDecimal(repairTaskView.getVehicleInsuranceTotalAmount())
                    .subtract(new BigDecimal(repairTaskView.getVehicleReplaceTotalAmount()))
                    .subtract(new BigDecimal(repairTaskView.getVehicleRepairTotalAmount())).toString();
        }
        repairTaskView.setRepairOtherTotalAmount(repairOtherTotalAmount);
        repairTaskView.setVehicleOtherTotalAmount(vehicleOtherTotalAmount);

        // 查询车辆残值时间
        // 当前时间（默认）
        String queryVehicleScrapeValueTimeString = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DATE_TYPE2));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(repairTaskView.getVerificationLossCheckTime())) {
            // 核损通过时间（存在核损通过时间）
            queryVehicleScrapeValueTimeString = repairTaskView.getVerificationLossCheckTime();
        } else {
            repairTaskView.setVehicleScrapeValue(this.queryCurrentVehicleScrapeValue(repairTaskView.getVin()).toString());
        }
        repairTaskView.setQueryVehicleScrapeValueTime(DateUtil.getFormatDate(queryVehicleScrapeValueTimeString, DateUtil.DATE_TYPE2, DateUtil.DATE_TYPE5));

        if (StringUtils.isNotBlank(repairTaskView.getAccidentNo())) {
            // 事故理赔金额
            AccidentDamageDTO accidentDamageDTO = manualUtils.getAccidentDamage(repairTaskView.getAccidentNo(), comModel.getToken());
            repairTaskView.setAccidentDamage(accidentDamageDTO);
            // 事故任务详情
            AccidentInfoDetailDTO accidentInfoDetailDTO = manualUtils.getAccidentInfoDetail(repairTaskView.getAccidentNo(), comModel.getToken());
            if (accidentInfoDetailDTO != null && CollectionUtils.isNotEmpty(accidentInfoDetailDTO.getAccidentFileList())){
                List<AccidentFileDataDTO> accidentFileList = accidentInfoDetailDTO.getAccidentFileList();
                Map<String, List<AccidentFileDataDTO>> map = accidentFileList.stream().collect(Collectors.groupingBy(x -> x.getAccidentNo()));
                List<AccidentCaseFileDataDTO> accidentCaseFileDataList = Lists.newArrayList();
                map.forEach((caseNo,list)->{
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(caseNo) && caseNo.startsWith("BA")){
                        AccidentCaseFileDataDTO fileDataDTO = new AccidentCaseFileDataDTO();
                        fileDataDTO.setCaseNo(caseNo);
                        fileDataDTO.setAccidentFileList(list);
                        accidentCaseFileDataList.add(fileDataDTO);
                    }
                    // 事故照片
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(caseNo) && caseNo.startsWith("SG")){
                        // 设置下载路径
                        manualUtils.setInsurDownloadPath(list);
                        accidentInfoDetailDTO.setAccidentPicList(list);
                    }
                });
                accidentInfoDetailDTO.setAccidentCaseFileDataList(accidentCaseFileDataList);
            }
            repairTaskView.setAccidentInfoDetail(accidentInfoDetailDTO);

            MtcManualTaskDTO mtcManualTaskDTO = mtcManualTaskMapper.selectByManualNo(repairTaskView.getAccidentNo());
            if (null != mtcManualTaskDTO && mtcManualTaskDTO.getClaimAmount().compareTo(BigDecimal.ZERO) > 0) {
                repairTaskView.setManualTaskClaimAmount(mtcManualTaskDTO.getClaimAmount().toString());
            }
        }

        // 查询保单信息
        String sendRepairTime = repairTaskView.getSendRepairTime();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sendRepairTime)){
            String sendRepairTimeFormat = sendRepairTime.substring(0, 10);
            repairTaskView.setInsuranceInfoList(manualUtils.getInsuranceInfo(repairTaskView.getVin(), sendRepairTimeFormat));
            repairTaskView.setSendRepairTime(sendRepairTimeFormat);
        }

        // 查询梧桐维修照片和情况说明
        WtRepairFileBo wtRepairFile = manualUtils.getWtRepairFileList(repairTaskView.getTaskNo());
        if (wtRepairFile != null){
            repairTaskView.setWtRepairFileList(wtRepairFile.getRepairFiles());
            repairTaskView.setSituationDesc(wtRepairFile.getSituationDesc());
        }

        // 确认车损类型-设置默认值
        if (repairTaskView.getConfirmCarDamageType() != null && repairTaskView.getConfirmCarDamageType() == 0) {
            AbstractOrderInfoDTO abstractOrderInfoDTO = newAndOldOrderConversionService.getOrderInfoById(repairTaskView.getAssociatedOrder(), repairTaskView.getVin());
            if (ObjectUtil.isNotEmpty(abstractOrderInfoDTO) && abstractOrderInfoDTO.getOrderType() == 1) {
                LongRentOrderInfoDTO longRentOrderInfoDTO = (LongRentOrderInfoDTO) abstractOrderInfoDTO;
                if (repairTaskView.getRepairTypeId().equals("1") || repairTaskView.getRepairTypeId().equals("2")) {
                    if (null != longRentOrderInfoDTO.getVehicleClientMaintenanceFee() && longRentOrderInfoDTO.getVehicleClientMaintenanceFee() == 1) {
                        repairTaskView.setConfirmCarDamageType(2);
                    }
                } else if (repairTaskView.getRepairTypeId().equals("3")) {
                    if (null != longRentOrderInfoDTO.getClientUpkeepTag() && longRentOrderInfoDTO.getClientUpkeepTag() == 1) {
                        repairTaskView.setConfirmCarDamageType(2);
                    }
                }
            }
        }

        String result = JSONObject.toJSONString(repairTaskView);
        vo.setData(result);
        return vo;
    }

    /**
     * 维修报价||核损核价 维修任务保存
     *
     * @param id 任务id
     * @param comModel
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updateRepairTask(Long id, UpdateRepairTaskBO updateRepairTaskBO, ComModel comModel) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        vo = updateRepairTaskCheck(updateRepairTaskBO);

        if (vo.getCode() != 0) {
            return vo;
        }

        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();


        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        if (null != updateRepairTaskBO.getCurrentTache() && !Objects.equals(repairTaskView.getCurrentTache(), updateRepairTaskBO.getCurrentTache())) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务节点已变更，请刷新后重新操作！");
        }

        RepairTask repairTask = new RepairTask();
        BeanCopyUtils.copyProperties(repairTaskView, repairTask);
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);
        repairTask.setVehicleInsuranceTotalAmount(new BigDecimal(repairTaskView.getVehicleInsuranceTotalAmount()));
        // 不更新当前环节
        repairTask.setCurrentTache(null);

        // 事故报案号
        if (StringUtils.isNotBlank(updateRepairTaskBO.getAccidentReportNumber())) {
            repairTask.setAccidentReportNumber(updateRepairTaskBO.getAccidentReportNumber());
        }

        // 预计修理天数
        if (StringUtils.isNotBlank(updateRepairTaskBO.getExpectedRepairDays())) {
            repairTask.setExpectedRepairDays(Long.valueOf(updateRepairTaskBO.getExpectedRepairDays()));

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date;
            try {
                date = sdf.parse(repairTaskView.getVehicleReciveTime());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, Integer.valueOf(updateRepairTaskBO.getExpectedRepairDays()));

                // 车辆预计完成时间
                repairTask.setExpectedRepairComplete(Timestamp.valueOf(sdf.format(calendar.getTime())));
            } catch (ParseException e) {
                log.error("预计修理完成时间计算错误");
                e.printStackTrace();
            }

        }

        // 修理厂换件金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairReplaceTotalAmount())) {
            repairTask.setRepairReplaceTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairReplaceTotalAmount()));
        }

        // 修理厂修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairRepairTotalAmount())) {
            repairTask.setRepairRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairRepairTotalAmount()));
        }

        // 修理厂定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairInsuranceTotalAmount())) {
            repairTask
                    .setRepairInsuranceTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairInsuranceTotalAmount()));
        }

        // 车管换件金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleReplaceTotalAmount())) {
            repairTask.setVehicleReplaceTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleReplaceTotalAmount()));
        }

        // 车管修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleRepairTotalAmount())) {
            repairTask.setVehicleRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleRepairTotalAmount()));
        }

        // 车管定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleInsuranceTotalAmount())) {
            repairTask.setVehicleInsuranceTotalAmount(
                    new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount()));
        }

        // 是否需要复勘
        if (StringUtils.isNotBlank(updateRepairTaskBO.getResurveyFlag())) {
            repairTask.setResurveyFlag(new BigDecimal(updateRepairTaskBO.getResurveyFlag()));
        }

        // 复勘部位
        repairTask.setResurveyPart(updateRepairTaskBO.getResurveyPart());

        // 责任情况
        if (StringUtils.isNotBlank(updateRepairTaskBO.getDutySituation())) {
            repairTask.setDutySituation(new BigDecimal(updateRepairTaskBO.getDutySituation()));
        }

        // 向用户追偿费用
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRecoveryAmount())) {
            repairTask.setRecoveryAmount(new BigDecimal(updateRepairTaskBO.getRecoveryAmount()));
        }

        // 保险上付费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getInsuranceAmount())) {
            repairTask.setInsuranceAmount(new BigDecimal(updateRepairTaskBO.getInsuranceAmount()));
        }

        // 加速折旧费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getAccDepAmount())) {
            repairTask.setAccDepAmount(new BigDecimal(updateRepairTaskBO.getAccDepAmount()));
        }

        // 停运损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getOutageLossAmount())) {
            repairTask.setOutageLossAmount(new BigDecimal(updateRepairTaskBO.getOutageLossAmount()));
        }

        // 车辆损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleLossAmount())) {
            repairTask.setVehicleLossAmount(new BigDecimal(updateRepairTaskBO.getVehicleLossAmount()));
        }

        // 拖车救援费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getTrailerRescueAmount())) {
            repairTask.setTrailerRescueAmount(new BigDecimal(updateRepairTaskBO.getTrailerRescueAmount()));
        }

        // 保养费用
        if (StringUtils.isNotBlank(updateRepairTaskBO.getMaintainAmount())) {
            repairTask.setMaintainAmount(new BigDecimal(updateRepairTaskBO.getMaintainAmount()));
        }

        // 超时原因
        repairTask.setOverTimeReasons(updateRepairTaskBO.getOverTimeReasons());

        if (updateRepairTaskBO.getConfirmType() != null) {
            repairTask.setConfirmType(updateRepairTaskBO.getConfirmType());
        }
        // 预估理赔金额
        if (updateRepairTaskBO.getEstimatedClaimAmount() != null) {
            repairTask.setEstimatedClaimAmount(updateRepairTaskBO.getEstimatedClaimAmount());
            if (repairTask.getEstimatedClaimAmount().compareTo(repairTaskView.getEstimatedClaimAmount()) != 0) {
                // 修改预估理赔金额日志
                String updateLog = "修改预估理赔金额：" + repairTask.getEstimatedClaimAmount();
                LogUtil.saveMtcOperatorLog(mtcOperatorLogMapper, id, updateLog, CurrentTacheEnum.getEnumDesc(repairTaskView.getCurrentTache()), comModel, time, repairTaskView.getCurrentTache(), Contants.ONE);
            }
        }
        // 定损单金额
        if (updateRepairTaskBO.getLossOrderAmount() != null) {
            repairTask.setLossOrderAmount(new BigDecimal(updateRepairTaskBO.getLossOrderAmount()));
        }
        // 确认车损状态
        if (updateRepairTaskBO.getConfirmCarDamageType() != null) {
            repairTask.setConfirmCarDamageType(updateRepairTaskBO.getConfirmCarDamageType());
        }
        // 是否使用小程序 0-否 1-是
        if (StringUtils.isNotBlank(updateRepairTaskBO.getIsUsedApplets())) {
            repairTask.setIsUsedApplets(updateRepairTaskBO.getIsUsedApplets());
        }
        // 是否客户直付 1-是 2-否
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustPaysDirect())) {
            repairTask.setCustPaysDirect(updateRepairTaskBO.getCustPaysDirect());
        }
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
            repairTask.setCustAmount(updateRepairTaskBO.getCustAmount());
        }
        // 客户直付金额 -> 影响用户承担金额
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
            if (repairTaskView.getCustAmount().compareTo(updateRepairTaskBO.getCustAmount()) != 0 || !repairTaskView.getCustPaysDirect().equals(updateRepairTaskBO.getCustPaysDirect())) {
                // 单独修改客户直付金额逻辑（用户承担=客户直付金额）
                log.info("任务【{}】单独修改客户直付金额", repairTask.getTaskNo());
                repairTask.setCustAmount(updateRepairTaskBO.getCustAmount());
                repairTask.setUserAssumedAmount(updateRepairTaskBO.getCustAmount());
                repairTask.setNotUserAssumedAmount(BigDecimal.ZERO);
            }
        }

        // 重新计算自费金额
        RepairAmountDTO repairAmountDTO = new RepairAmountDTO();
        BeanCopyUtils.copyProperties(repairTask, repairAmountDTO);
        BigDecimal selfFundedAmount = repairTaskService.getSelfFundedAmount(repairAmountDTO, manualUtils.getAccidentDamage(repairTask.getAccidentNo(), comModel.getToken()));
        repairTask.setSelfFundedAmount(selfFundedAmount);

        // 更新状况卡控用当前环节(核损核价)
        // 2018/6/4 调查结果 因为定损，核损，维修，车辆交接都用了此方法，就不能限制此状态了
        // repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));
        // repairTask.setVerificationLossTaskOperId(repairTaskView.getVerificationLossTaskOperId());

        // 保存
        Integer count = repairTaskMapper.updateRepairTaskInfo(repairTask);

        if (count > 0) {
            List<VehicleRepairPic> allVehicleRepairPics = vehicleRepairPicMapper.getAllPicListByTaskNo(repairTaskView.getTaskNo());
            List<VehicleRepairPic> insertPicList;
            List<VehicleRepairPic> deletePicList;
            try {
                // 损坏部位图片
                insertPicList = new ArrayList<>(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)),
                        BigDecimal.valueOf(1),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList = new ArrayList<>(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)));
                // 维修图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getRepairPicture(), BigDecimal.valueOf(2)),
                        BigDecimal.valueOf(2),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getRepairPicture(), BigDecimal.valueOf(2)));
                // 验收视频
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getCheckVideo(), BigDecimal.valueOf(17)),
                        BigDecimal.valueOf(17),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getCheckVideo(), BigDecimal.valueOf(17)));
                // 事故责任认定书图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getAccidentLiabilityConfirmationPicture(), BigDecimal.valueOf(19)),
                        BigDecimal.valueOf(19),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getAccidentLiabilityConfirmationPicture(), BigDecimal.valueOf(19)));
                // 保司定损单图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getInsuranceCompanyLossOrderPicture(), BigDecimal.valueOf(20)),
                        BigDecimal.valueOf(20),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getInsuranceCompanyLossOrderPicture(), BigDecimal.valueOf(20)));
                // 我方驾驶证图片
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getOurDriverLicensePicture(), BigDecimal.valueOf(21)),
                        BigDecimal.valueOf(21),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getOurDriverLicensePicture(), BigDecimal.valueOf(21)));
                // 客户直付凭证
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)),
                        BigDecimal.valueOf(22),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)));
                // 损坏部位视频
                insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                        RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)),
                        BigDecimal.valueOf(23),
                        repairTaskView.getTaskNo(),
                        comModel));
                deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)));
            } catch (MtcBusinessRuntimeException e) {
                log.error("维修任务保存，包装图片异常！");
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage(e.getMessage());
                return vo;
            }
            if (CollectionUtils.isNotEmpty(deletePicList)) {
                // 批量删除
                vehicleRepairPicMapper.delMaterialPic(deletePicList.stream().map(VehicleRepairPic::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(insertPicList)) {
                // 批量插入
                vehicleRepairPicMapper.batchInsert(insertPicList);
                if ((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS == repairTaskView.getCurrentTache()) {
                    String insertRepairPicLog = "";
                    if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(1)))) {
                        insertRepairPicLog += "新增上传损坏部位图片；";
                    }
                    if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(23)))) {
                        insertRepairPicLog += "新增上传损坏部位视频；";
                    }
                    if (StringUtils.isNotBlank(insertRepairPicLog)) {
                        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
                        mtcOperatorLog.setUpdateOperId(operId);
                        mtcOperatorLog.setUpdateOperName(operName);
                        mtcOperatorLog.setUpdateTime(time);
                        mtcOperatorLog.setCreateOperId(operId);
                        mtcOperatorLog.setCreateOperName(operName);
                        mtcOperatorLog.setCreateTime(time);
                        mtcOperatorLog.setRemark("核损核价");
                        mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
                        mtcOperatorLog.setStatus(Contants.ONE);
                        mtcOperatorLog.setOpeContent(insertRepairPicLog);
                        mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
                        mtcOperatorLog.setCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
                        // 添加log
                        mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
                    }
                }
            }

            // 保存修理项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getRepairItemDetail())) {
                for (ViewRepairItemDetailBO viewRepairItemDetail : updateRepairTaskBO.getRepairItemDetail()) {
                    RepairItemDetail repairItemDetail = new RepairItemDetail();
                    repairItemDetail.setId(Long.valueOf(viewRepairItemDetail.getId()));
                    repairItemDetail.setRemark(viewRepairItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewRepairItemDetail.getViewAmount())) {
                        repairItemDetail.setViewAmount(new BigDecimal(viewRepairItemDetail.getViewAmount()));
                    }
                    repairItemDetail.setUpdateOperId(operId);
                    repairItemDetail.setUpdateOperName(operName);
                    repairItemDetail.setUpdateTime(time);

                    // 更新
                    repairItemDetailMapper.updateByIdSelective(repairItemDetail);

                }

            }

            // 保存换件项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getReplaceItemDetail())) {
                for (ViewReplaceItemDetailBO viewReplaceItemDetail : updateRepairTaskBO.getReplaceItemDetail()) {
                    ReplaceItemDetail replaceItemDetail = new ReplaceItemDetail();
                    replaceItemDetail.setId(Long.valueOf(viewReplaceItemDetail.getId()));
                    replaceItemDetail.setRemark(viewReplaceItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewReplaceItemDetail.getViewAmount())) {
                        replaceItemDetail.setViewAmount(new BigDecimal(viewReplaceItemDetail.getViewAmount()));
                    }
                    replaceItemDetail.setUpdateOperId(operId);
                    replaceItemDetail.setUpdateOperName(operName);
                    replaceItemDetail.setUpdateTime(time);

                    // 更新
                    replaceItemDetailMapper.updateByIdSelective(replaceItemDetail);
                }
            }

            // 更新占据预算
            MtcBudgetManagement budgetManagement = mtcBudgetManagementMapper
                    .selectByOrgIdAndYear(repairTaskView.getOrgId(), ComUtil.getCurrentYear());
            if (budgetManagement != null) {
                if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairInsuranceTotalAmount())) {
                    // 如果原定损金额不为0或者null，需要减去原定损金额
                    if (repairTaskView.getRepairInsuranceTotalAmount() != null) {
                        budgetManagement.setOccupyBudget(budgetManagement.getOccupyBudget()
                                .subtract(new BigDecimal(repairTaskView.getRepairInsuranceTotalAmount())));
                    }
                    // 占据金额加上当前保存的金额
                    budgetManagement.setOccupyBudget(budgetManagement.getOccupyBudget()
                            .add(new BigDecimal(updateRepairTaskBO.getRepairInsuranceTotalAmount())));
                    if (budgetManagement.getOccupyBudget().doubleValue() < 0) {
                        budgetManagement.setOccupyBudget(new BigDecimal(0));
                    }
                    budgetManagement.setUpdateOperId(comModel.getUpdateOperId());
                    budgetManagement.setUpdateOperName(comModel.getUpdateOperName());
                    budgetManagement.setUpdateTime(new Date());
                    mtcBudgetManagementMapper.updateByPrimaryKeySelective(budgetManagement);
                }
            }
        }

        return vo;
    }
    
    /**
     * 用户追偿费用计算
     * @param dutySituation
     * @param insuranceAmount
     * @param accDepAmount
     * @param outageLossAmount
     * @param vehicleLossAmount
     * @param trailerRescueAmount
     * @return
     */
    @Override
    public Double recourseCostCalculation(Integer dutySituation, Double insuranceAmount, Double accDepAmount,
                                          Double outageLossAmount, Double vehicleLossAmount, Double trailerRescueAmount) {

        Double recoveryAmount = new Double(0);

        recoveryAmount = insuranceAmount + accDepAmount + outageLossAmount + vehicleLossAmount + trailerRescueAmount;

        return recoveryAmount;
    }
    
    /**
     * 加速折旧费，保险上付费，车辆损失费计算
     * @param noDeductiblesFlag
     * @param claimsFlag
     * @param vehicleInsuranceTotalAmount
     * @param orgId
     * @return
     */
    @Override
    public Map<String, String> depreciationExpenseCalculation(Integer noDeductiblesFlag, Integer claimsFlag,
                                                              Double vehicleInsuranceTotalAmount, String orgId) {

        // 加速折旧费
        Double accDepAmount = 0d;
        // 车辆损失费
        Double vehicleLossAmount = 0d;
        // 加速折旧费比例
        Double acceleratedDepreciationRatio = 0d;

        // 取得分子公司参数配置
        OrgCostCofig orgCostCofig = mtcVerificationLossConfigureMapper.getCostCofig(orgId);

        if (orgCostCofig != null) {

            // 保险上浮
            BigDecimal insuranceAmount = orgCostCofig.getInsuranceFloatation();

            // 折旧比例
            Double acceleratedDepreciationBase = orgCostCofig.getAcceleratedDepreciationBase().doubleValue();
            acceleratedDepreciationRatio = orgCostCofig.getAcceleratedDepreciationRatio().doubleValue();
            // 是否需要理赔
            if (claimsFlag == 0) {

                // 是否大于配置临界值
                if (vehicleInsuranceTotalAmount - acceleratedDepreciationBase > 0) {
                    accDepAmount = (vehicleInsuranceTotalAmount - acceleratedDepreciationBase)
                            * acceleratedDepreciationRatio / 100;
                    vehicleLossAmount = acceleratedDepreciationBase;
                    insuranceAmount = BigDecimal.ZERO;
                } else {
                    vehicleLossAmount = vehicleInsuranceTotalAmount;
                    insuranceAmount = BigDecimal.ZERO;
                }

            } else {
                if ((orgCostCofig.getAcceleratedDepreciationRatio().compareTo(BigDecimal.ZERO)) > 0) {
                    acceleratedDepreciationRatio = orgCostCofig.getAcceleratedDepreciationRatio().doubleValue() / 100;
                }

                // 为空
                if (vehicleInsuranceTotalAmount == null) {

                    Map<String, String> map = new HashMap<String, String>();
                    map.put("accDepAmount", "0");
                    map.put("vehicleLossAmount", "0");
                    map.put("insuranceAmount", "0");

                    return map;
                }

                // 是否购买不计免赔
                if (Contants.HAS_DEDUCTIBLES_FLAG.equals(noDeductiblesFlag)) {
                    // 是否大于临界值
                    if (vehicleInsuranceTotalAmount - acceleratedDepreciationBase > 0) {
                        accDepAmount = (vehicleInsuranceTotalAmount - acceleratedDepreciationBase)
                                * acceleratedDepreciationRatio;
                    } else {
                        insuranceAmount = BigDecimal.ZERO;
                    }

                } else {
                    // 是否大于临界值
                    if (vehicleInsuranceTotalAmount - acceleratedDepreciationBase > 0) {
                        accDepAmount = (vehicleInsuranceTotalAmount - acceleratedDepreciationBase)
                                * acceleratedDepreciationRatio;
                        vehicleLossAmount = acceleratedDepreciationBase;
                    } else {
                        vehicleLossAmount = vehicleInsuranceTotalAmount;
                        insuranceAmount = BigDecimal.ZERO;
                    }
                }

            }

            BigDecimal accDepAmountDecimal = BigDecimal.valueOf(accDepAmount).setScale(2, BigDecimal.ROUND_HALF_UP);

            Map<String, String> map = new HashMap<String, String>();
            map.put("accDepAmount", ComUtil.rvZeroAndDot(accDepAmountDecimal.toString()));
            map.put("vehicleLossAmount", ComUtil.rvZeroAndDot(vehicleLossAmount.toString()));
            map.put("insuranceAmount", ComUtil.rvZeroAndDot(insuranceAmount.toString()));

            return map;
        } else {
            return null;
        }

    }

    /**
     * 核损核价事故维修转转自费维修
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updateSelfFeeMaintenance(Long id, String taskNo, String updateEndTime,
                                                          HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(taskNo);

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        if (taskSchedule.getVerificationLossTaskSchedule() == Contants.VERIFICATION_LOSS_CLOSED) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务已关闭，请重新刷新一览页面");
            return vo;
        }

        // 更新任务状态
        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        BigDecimal initialzero = new BigDecimal(0);
        RepairTask repairTask = new RepairTask();
        repairTask.setRepairTypeId(new BigDecimal(Contants.REPAIR_TYPE_SELF_COST));
        repairTask.setRepairTypeName("自费维修");
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);
        repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.TASK_SCHEDULE_DEFAULT));
        repairTask.setInsuranceQuoteTaskSchedule(Long.valueOf(Contants.INSURANCE_QUOTE_UNTREATED));
        repairTask.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_INSURANCE_QUOTE));
        repairTask.setResurveyFlag(initialzero);
        repairTask.setRecoveryFlag(initialzero);
        repairTask.setRecoveryAmount(initialzero);
        repairTask.setInsuranceAmount(initialzero);
        repairTask.setAccDepAmount(initialzero);
        repairTask.setOutageLossAmount(initialzero);
        repairTask.setVehicleLossAmount(initialzero);
        repairTask.setTrailerRescueAmount(initialzero);
        repairTask.setDutySituation(initialzero);
        repairTask.setRepairRepairTotalAmount(initialzero);
        repairTask.setRepairReplaceTotalAmount(initialzero);
        repairTask.setRepairInsuranceTotalAmount(initialzero);
        repairTask.setVehicleReplaceTotalAmount(initialzero);
        repairTask.setVehicleRepairTotalAmount(initialzero);
        repairTask.setVehicleInsuranceTotalAmount(initialzero);
        repairTask.setResurveyPart("");
        repairTask.setUpdateEndTime(updateEndTime);
        // 更新状况卡控用当前环节(核损核价)
        repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

        // 保存
        Integer count = repairTaskMapper.updateTransferTask(repairTask);

        if (count > 0) {
            // 删除定损内容
            repairItemDetailMapper.deleteBytaskNo(taskNo);
            replaceItemDetailMapper.deleteBytaskNo(taskNo);

            // 删除精友定损内容
            mtcLossInfoMapper.deleteByTaskNo(taskNo);
            mtcCollisionPartsMapper.deleteByTaskNo(taskNo);
            mtcLossFitInfoMapper.deleteByTaskNo(taskNo);
            mtcLossRepairInfoMapper.deleteByTaskNo(taskNo);
            mtcLossOuterRepairInfoMapper.deleteByTaskNo(taskNo);
            mtcLossRepairSumInfoMapper.deleteByTaskNo(taskNo);
            mtcLossAssistInfoMapper.deleteByTaskNo(taskNo);

            // 取得详情
            RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

            // 通知车管系统
            if (repairTaskView.getOrigin() == 1) {
                BdpMtcTaskInfoDTO bdpMtcTaskInfoDTO = new BdpMtcTaskInfoDTO();
                bdpMtcTaskInfoDTO.setTaskNo(taskNo);
                bdpMtcTaskInfoDTO.setTaskStatus(1);
                bdpMtcTaskInfoDTO.setTaskType(1);
                DefaultServiceRespDTO defaultServiceRespDTO = bdpMtcTaskInfoService
                        .updateBdpTaskInfoType(bdpMtcTaskInfoDTO, comModel.getUserName());
                if (defaultServiceRespDTO.getCode() != 0) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("调用车管系统失败");
                    return vo;
                }
            }

            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("转自费维修，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            // 添加log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);

            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setUpdateOperId(operId);
            mtcProcessLog.setUpdateOperName(operName);
            mtcProcessLog.setUpdateTime(time);
            mtcProcessLog.setCreateOperId(operId);
            mtcProcessLog.setCreateOperName(operName);
            mtcProcessLog.setCreateTime(time);
            mtcProcessLog.setRemark("维修报价");
            mtcProcessLog.setRecoderId(id + StringUtils.EMPTY);
            mtcProcessLog.setStatus(0);
            mtcProcessLog.setOpeContent("转自费维修");
            mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcProcessLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_INSURANCE_QUOTE));
            // 添加流程log
            mtcOperatorLogMapper.saveSelective(mtcProcessLog);
            // 记录任务状态持续时间
            vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));

            return vo;
        } else {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            return vo;
        }

    }

    /**
     * 核损核价事故维修退回
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updateMainaintenancee(Long id, RejctBO rejctBO, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(Long.valueOf(id));
        // 记录驳回时等级
        String advancedAuditLeve = repairTaskNew.getAdvancedAuditLeve();
        log.info("被驳回时等级advancedAuditLeve:" + advancedAuditLeve);

//        // 检查是否存在定损数据
//        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
//        mtcLossInfoDTO.setTaskNo(repairTaskNew.getTaskNo());
//        mtcLossInfoDTO.setStatus(1);
//        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
//        if (CollectionUtils.isNotEmpty(mtcLossInfoList)) {
//            MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
//            if (StringUtils.isNotBlank(mtcLossInfo.getAuditHandlerCode())
//                    && mtcLossInfo.getSumLossAmount().doubleValue() > 0) {
//                // 核价项目全部通过，不可退回
//                List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper
//                        .queryMtcLossFitList(repairTaskNew.getTaskNo());
//                List<MtcLossRepairInfo> mtcLossRepairInfoList = mtcLossRepairInfoMapper
//                        .queryMtcLossRepairList(repairTaskNew.getTaskNo());
//                List<MtcLossOuterRepairInfo> mtcLossOuterRepairInfoList = mtcLossOuterRepairInfoMapper
//                        .queryMtcLossOuterRepairList(repairTaskNew.getTaskNo());
//                List<MtcLossAssistInfo> mtcLossAssistInfoList = mtcLossAssistInfoMapper
//                        .queryMtcLossAssistList(repairTaskNew.getTaskNo());
//                if (CollectionUtils.isEmpty(mtcLossFitInfoList) && CollectionUtils.isEmpty(mtcLossRepairInfoList)
//                        && CollectionUtils.isEmpty(mtcLossOuterRepairInfoList)
//                        && CollectionUtils.isEmpty(mtcLossAssistInfoList)) {
//                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "核价项目全部通过，不可退回");
//                }
//            }
//        }

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        if (taskSchedule.getVerificationLossTaskSchedule() == Contants.VERIFICATION_LOSS_CLOSED) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务已关闭，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);

        // 取得详情
        RepairTaskViewBO repairTaskViewOD = repairTaskMapper.getRepairTaskView(id);

        if (Contants.VEHICLE_MANAGE_AGREE.equals(rejctBO.getVehicleManageViewFlag())
                && Contants.REPAIR_TYPE_ID_TWO == rejctBO.getRepairTypeId()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("同意报价,不可退回");
            return vo;
        }

        if (StringUtils.equals("其他", rejctBO.getVerificationRejectReasons())) {

            if (StringUtils.isBlank(rejctBO.getVerificationRejectReasonsDetail())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("选择其他时，退回原因必填");
                return vo;
            }
        }

        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        BigDecimal initialzero = new BigDecimal(0);
        RepairTask repairTask = new RepairTask();
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);
        repairTask.setUpdateEndTime(rejctBO.getUpdateEndTime());

        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        repairTask.setVerificationLossTaskOperId(null);

        repairTask.setAdvancedAuditLeve("1");
        repairTask.setInsuranceQuoteTaskSchedule((long) Contants.INSURANCE_QUOTE_REJECT);
        repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.TASK_SCHEDULE_DEFAULT));
        repairTask.setVerificationRejectReasons(rejctBO.getVerificationRejectReasons());
        repairTask.setVerificationRejectReasonsDetail(rejctBO.getVerificationRejectReasonsDetail());
        repairTask.setCurrentTache((long) Contants.CURRENT_TACHE_INSURANCE_QUOTE);
        repairTask.setResurveyFlag(initialzero);
        repairTask.setRecoveryFlag(initialzero);
        repairTask.setRecoveryAmount(initialzero);
        repairTask.setInsuranceAmount(initialzero);
        repairTask.setAccDepAmount(initialzero);
        repairTask.setOutageLossAmount(initialzero);
        repairTask.setVehicleLossAmount(initialzero);
        repairTask.setTrailerRescueAmount(initialzero);
        repairTask.setDutySituation(initialzero);
        repairTask.setNuclearLossReversionFlag(BigDecimal.ONE);
        repairTask.setExamineLevel(BigDecimal.ZERO);
        repairTask.setVerificationRejectLevel(advancedAuditLeve);

        Integer count = repairTaskMapper.updateTransferTask(repairTask);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                ThreadPoolUtils.EXECUTOR.submit(() -> {
                    // 业财对接-同步任务全部账单
                    bfcCostService.closeRepairBill(repairTaskView.getTaskNo(), comModel.getToken());
                });
            }
        });

        if (count > 0) {
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("退回至修理厂定损，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
            // 添加基础log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
            //核损核价被退回添加维修报价被驳回通知
            log.info("核损核价1级未通过，退回至修理厂" + repairTaskNew.getTaskNo());
            vehicleMaintenanceLossAuditSubject.notifyAuditReject(new BaseRepairEvent(new Date(),repairTaskNew.getTaskNo()));
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setUpdateOperId(operId);
            mtcProcessLog.setUpdateOperName(operName);
            mtcProcessLog.setUpdateTime(time);
            mtcProcessLog.setCreateOperId(operId);
            mtcProcessLog.setCreateOperName(operName);
            mtcProcessLog.setCreateTime(time);
            mtcProcessLog.setRemark("维修报价");
            mtcProcessLog.setRecoderId(id + StringUtils.EMPTY);
            mtcProcessLog.setStatus(0);
            mtcProcessLog.setOpeContent("被退回");
            mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcProcessLog.setCurrentTache((long) Contants.CURRENT_TACHE_INSURANCE_QUOTE);
            // 添加流程log
            mtcOperatorLogMapper.saveSelective(mtcProcessLog);

            // 记录任务状态持续时间
            vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));

            if(!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskNew.getRepairTypeId()), repairTaskNew.getReviewToSelFeeFlag())) {
                // 任务状态同步（请求定损系统）
                String orgId = comModel.getOrgId();
                String orgName = orgInfoMapper.getOrgName(orgId);
                // PACKET > HEAD
                HeadBean headBean = new HeadBean();
                headBean.setRequestType("007");
                headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
                // PACKET > BODY >EvalLossInfo
                EvalLossInfoSynchronize evalLossInfoSynchronize = new EvalLossInfoSynchronize();
                evalLossInfoSynchronize.setLossNo(repairTaskView.getTaskNo());
                evalLossInfoSynchronize.setReportCode(repairTaskView.getTaskNo());
                evalLossInfoSynchronize.setDmgVhclId(repairTaskView.getTaskNo());
                evalLossInfoSynchronize.setStatusCode("014");
                evalLossInfoSynchronize.setStatusName("核损退回到定损");
                evalLossInfoSynchronize.setComCode(orgId);
                evalLossInfoSynchronize.setCompany(orgName);
                evalLossInfoSynchronize.setBranchComCode(orgId);
                evalLossInfoSynchronize.setBranchComName(orgName);
                evalLossInfoSynchronize.setHandlerCode(String.valueOf(operId));
                evalLossInfoSynchronize.setHandlerName(operName);
                evalLossInfoSynchronize.setOperationLink("05");
                evalLossInfoSynchronize.setOperationResults("核损退回到定损");
                // PACKET > BODY
                BodyBeanSynchronize bodyBeanSynchronize = new BodyBeanSynchronize();
                bodyBeanSynchronize.setEvalLossInfoSynchronize(evalLossInfoSynchronize);
                // PACKET
                PacketBeanSynchronize packetBeanSynchronize = new PacketBeanSynchronize();
                packetBeanSynchronize.setHeadBean(headBean);
                packetBeanSynchronize.setBodyBeanSynchronize(bodyBeanSynchronize);
                StringWriter stringWriter = new StringWriter();
                JAXB.marshal(packetBeanSynchronize, stringWriter);
                String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "007");
                Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
                if (MapUtils.isEmpty(resultMap)) {
                    log.error("任务状态同步失败：-------------->" + JSON.toJSONString(resultMap));
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "配件库切换中，请继续操作！");
                }
                Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
                if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
                    log.error("任务状态同步失败：-------------->" + JSON.toJSONString(resultMap));
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "配件库切换中，请继续操作！");
                }
            }

            return vo;
        } else {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            return vo;
        }
    }

    /**
     * 核损核价事故维修同意退回
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    @SuppressWarnings({"unchecked"})
    public DefaultServiceRespDTO accpMainaintenancee(Long id, String updateEndTime, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        // 取得用户名称
        String userName = comModel.getUserName();

        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId()) == null ? 0
                : mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId());
        if (LAPricelevel != 2) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("退回需要运营经理同意，请切换相应权限账号");
            return vo;
        }

        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        BigDecimal initialzero = new BigDecimal(0);
        RepairTask repairTask = new RepairTask();
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);

        repairTask.setAdvancedAuditLeve("1");

        repairTask.setInsuranceQuoteTaskSchedule(Long.valueOf(Contants.INSURANCE_QUOTE_REJECT));
        repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.TASK_SCHEDULE_DEFAULT));
        repairTask.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_INSURANCE_QUOTE));
        repairTask.setResurveyFlag(initialzero);
        repairTask.setRecoveryFlag(initialzero);
        repairTask.setRecoveryAmount(initialzero);
        repairTask.setInsuranceAmount(initialzero);
        repairTask.setAccDepAmount(initialzero);
        repairTask.setOutageLossAmount(initialzero);
        repairTask.setVehicleLossAmount(initialzero);
        repairTask.setTrailerRescueAmount(initialzero);
        repairTask.setDutySituation(initialzero);
        repairTask.setNuclearLossReversionFlag(BigDecimal.ONE);
        repairTask.setExamineLevel(BigDecimal.ZERO);
        repairTask.setUpdateEndTime(updateEndTime);
        // 更新状况卡控用当前环节(核损核价)
        repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

        // 保存
        Integer count = repairTaskMapper.updateTransferTask(repairTask);

        if (count > 0) {

            // 取得详情
            RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("退回，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));
            // 添加基础log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
            // 记录任务状态持续时间
            vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));
            return vo;
        } else {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            return vo;
        }

    }

    /**
     * 核损核价事故维修退回异议
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO rejectMainaintenancee(Long id, RemarkListBO remarkListBO, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(Long.valueOf(id));

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        // 取得用户名称
        String userName = comModel.getUserName();
        // 取得详情
        RepairTaskViewBO repairTaskViewOD = repairTaskMapper.getRepairTaskView(id);

        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId()) == null ? 0
                : mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId());
        if (LAPricelevel != 2) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("退回异议需要运营经理级别，请切换相应权限账号");
            return vo;
        }

        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        RepairTask repairTask = new RepairTask();
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);
        if (repairTaskViewOD.getAdvancedAuditLeve().equals("5")) {
            repairTask.setAdvancedAuditLeve("3");
            repairTask.setVerificationLossTaskSchedule(300L);
        } else if (repairTaskViewOD.getAdvancedAuditLeve().equals("6")) {
            repairTask.setAdvancedAuditLeve("4");
            repairTask.setVerificationLossTaskSchedule(300L);
        }

        repairTask.setVerificationLossTaskSchedule(300L);
        repairTask.setVerificationLossTaskOperId(null);
        repairTask.setUpdateEndTime(remarkListBO.getUpdateEndTime());

        // 更新状况卡控用当前环节(核损核价)
        repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

        // 保存
        Integer count = repairTaskMapper.updateTransferTask(repairTask);

        if (count > 0) {
            List<RepairRemark> remarks = new ArrayList<>();
            for (RepairRemarkBO repairRemarkBO : remarkListBO.getRemarklist()) {
                RepairRemark repairRemark = new RepairRemark();
                repairRemark.setCreateOperId(operId);
                repairRemark.setCreateOperName(operName);
                repairRemark.setCreateTime(time);
                repairRemark.setUpdateOperId(operId);
                repairRemark.setUpdateOperName(operName);
                repairRemark.setUpdateTime(time);
                repairRemark.setRepairStage(new BigDecimal(2));
                repairRemark.setTaskNo(repairRemarkBO.getTaskNo());
                repairRemark.setRemark(repairRemarkBO.getRemark());
                remarks.add(repairRemark);
            }
            if (CollectionUtils.isNotEmpty(remarks)) {
                repareRemarkMapper.batchInsert(remarks);
            }
            // 取得详情
            RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("运营经理退回异议，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));
            // 添加基础log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
            // 记录任务状态持续时间
            vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));
            return vo;
        } else {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            return vo;
        }

    }

    /**
     * 核损核价审核通过
     *
     * @param id 任务id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @SuppressWarnings({"unchecked"})
    public DefaultServiceRespDTO updateReviewPassTask(Long id, UpdateRepairTaskBO updateRepairTaskBO, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 触发审核通过时检查车辆运营单位
        // 江苏大区、昆明公司、海口公司、三亚公司、天津公司
        /*
         * 008P 环球车享江苏事业部 008P01 环球车享（苏州）汽车租赁有限公司 008P02 无锡赛可汽车租赁有限公司 008P03
         * 环球车享（常州）汽车租赁有限公司 008P04 环球车享镇江汽车租赁有限公司 008P05 环球车享汽车租赁南通有限公司 008P06
         * 环球车享扬州汽车租赁有限公司 008P07 环球车享盐城汽车租赁有限公司 005T 环球车享（昆明）汽车租赁有限公司 005F
         * 环球车享（海口）汽车租赁有限公司 008L 环球车享（三亚）汽车租赁有限公司 008F 环球车享（天津）汽车租赁有限公司
         */

        // 判断是否有提前提车 若有则必须提车已完成才能审核通过
        int advanceCount = vehicleAdvanceCheckTaskMapper.getadvCheck(updateRepairTaskBO.getTaskNo());
        if (advanceCount > 0) {
            if (!vehicleAdvanceCheckTaskMapper.getSchedule(updateRepairTaskBO.getTaskNo()).equals("3")) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("当前任务有提前提车申请未处理，请优先处理!");
                return vo;
            }
        }

        // 获取登陆用户信息
        ComModel comModel = ComUtil.getUserInfo(request);
        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(id);
        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if ("1".equals(repairTaskNew.getAdvancedAuditLeve())) {
            // 查询预算
            BudgetInfoDTO budgetInfoDTO = repairTaskService.getBudgetInfoByTaskNo(repairTaskNew.getTaskNo(), request);
            if (null == budgetInfoDTO) {
                log.info("【{}】1级核损核价，未查询到预算！", repairTaskNew.getTaskNo());
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("预算不足！请先联系财务调整预算！");
                return vo;
            }
            // 计算自费金额
            BigDecimal selfFundedAmount = BigDecimal.ZERO;
            AccidentDamageDTO accidentDamageDTO = manualUtils.getAccidentDamage(repairTaskNew.getAccidentNo(), comModel.getToken());
            if (null == accidentDamageDTO || null == accidentDamageDTO.getVehicleTotalLossDisposalFlag() || accidentDamageDTO.getVehicleTotalLossDisposalFlag() != 1) {
                // 【自费金额】=【维修总金额】-【预估保险理赔金额】-【非用户承担金额】
                BigDecimal estimatedClaimAmount = null != updateRepairTaskBO.getEstimatedClaimAmount() ? updateRepairTaskBO.getEstimatedClaimAmount() : repairTaskNew.getEstimatedClaimAmount();
                selfFundedAmount = new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount()).subtract(estimatedClaimAmount).subtract(repairTaskNew.getUserAssumedAmount());
                log.info("【{}】自费金额计算：{}-{}-{}", repairTaskNew.getTaskNo(), updateRepairTaskBO.getVehicleInsuranceTotalAmount(), estimatedClaimAmount, repairTaskNew.getUserAssumedAmount());
            }
            log.info("【{}】1级核损核价自费金额：{}，参与预算对比", repairTaskNew.getTaskNo(), selfFundedAmount);
            if (new BigDecimal(budgetInfoDTO.getAvailableBalance()).compareTo(selfFundedAmount) < 0) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("预算不足！请先联系财务调整预算！");
                return vo;
            }
        }

        // 检查是否存在定损数据
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskNew.getTaskNo());
        mtcLossInfoDTO.setStatus(1);
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        List<MtcRepairItemCheckInfoDTO> checkInfoList = mtcRepairItemCheckInfoService.queryCheckListByTaskNo(repairTaskNew.getTaskNo());
        if (CollectionUtils.isEmpty(mtcLossInfoList) && CollectionUtils.isEmpty(checkInfoList)) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "未经过定损系统维修报价，请退回后重新定损");
        } else {
            if (!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskNew.getRepairTypeId()), repairTaskNew.getReviewToSelFeeFlag())) {
                if (CollectionUtils.isEmpty(mtcLossInfoList)) {
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "配件库切换中，请退回至维修报价重新操作！");
                }
                MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
                if (StringUtils.isBlank(mtcLossInfo.getAuditHandlerCode())) {
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "请先进行核损核价");
                }
            }
        }
        // 核价项目未全部通过，不可审核通过
        if (!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskNew.getRepairTypeId()), repairTaskNew.getReviewToSelFeeFlag())) {
            List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper.queryMtcLossFitList(repairTaskNew.getTaskNo());
            List<MtcLossRepairInfo> mtcLossRepairInfoList = mtcLossRepairInfoMapper
                    .queryMtcLossRepairList(repairTaskNew.getTaskNo());
            List<MtcLossOuterRepairInfo> mtcLossOuterRepairInfoList = mtcLossOuterRepairInfoMapper
                    .queryMtcLossOuterRepairList(repairTaskNew.getTaskNo());
            List<MtcLossAssistInfo> mtcLossAssistInfoList = mtcLossAssistInfoMapper
                    .queryMtcLossAssistList(repairTaskNew.getTaskNo());
            if (CollectionUtils.isNotEmpty(mtcLossFitInfoList) || CollectionUtils.isNotEmpty(mtcLossRepairInfoList)
                    || CollectionUtils.isNotEmpty(mtcLossOuterRepairInfoList)
                    || CollectionUtils.isNotEmpty(mtcLossAssistInfoList)) {
                return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "核价项目未全部通过，不可审核通过");
            }
        } else {
            for (MtcRepairItemCheckInfoDTO dto : checkInfoList) {
                if (dto.getCheckStatus() != 1) {
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "核价项目未全部通过，不可审核通过");
                }
            }
        }

        // 检查当前环节是否处于核损核价环节
        if (null == taskSchedule || Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }
        if (taskSchedule.getVerificationLossTaskSchedule() == Contants.VERIFICATION_LOSS_CLOSED) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务已关闭，请重新刷新一览页面");
            return vo;
        }

        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        String userName = comModel.getUserName();

        // 触发审核通过时检查车辆运营单位
        String lawfulCompany = "";
        try {
            lawfulCompany = apolloPropertyUtils.getString("lawfulCompany");
        } catch (Exception e) {
            lawfulCompany = "";
        }

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);
        // 车辆运营单位
        String orgId = repairTaskView.getOrgId();

        log.error("核损核价审核通过  id:" + id);
        log.error("核损核价审核通过  任务编号:" + repairTaskView.getTaskNo());
        log.error("核损核价审核通过  车辆运营单位:" + orgId);
        log.error("核损核价审核通过  登录者所属组织机构ID:" + comModel.getOrgId());

        // 如果触发审核通过时检查车辆运营单位不为空
        if (!"".equals(lawfulCompany.trim())) {
            // 校验当前登录人账户环球时，需在核损核价阶段进行权限限制，触发审核通过时检查车辆运营单位是否为江苏大区、昆明公司、海口公司、三亚公司、天津公司的机构，如不是，则不允许核价通过。
            if ("00".equals(comModel.getOrgId())) {
                // 非法公司
                if (!lawfulCompany.contains("," + orgId + ",")) {
                    vo.setCode(Contants.RETURN_ERROR_CODE);
                    vo.setMessage("非江苏大区、昆明公司、海口公司、三亚公司、天津公司不允许审核通过");
                    return vo;
                }
            }
        }

        vo = updateReviewPassTascheck(updateRepairTaskBO);

        // 选择“是”，则复勘部位必填
        if (Contants.NEEDRESURVEY.equals(updateRepairTaskBO.getResurveyFlag())) {
            if (StringUtils.isBlank(updateRepairTaskBO.getResurveyPart())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("复勘部位不能为空");
                return vo;
            }
        }

        // 事故维修 价格异议
        if (StringUtils.equals("1", updateRepairTaskBO.getVehicleManageViewFlag())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("核价异议,不可审核通过");
            return vo;
        }

        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId()) == null ? 0 : mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId());
        String advancedAuditLeve = repairTaskView.getAdvancedAuditLeve();
        if (!StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), LAPricelevel + StringUtils.EMPTY)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("该任务不在当前审核级别，请更换相应权限账号");
            return vo;
        }

        RepairTask repairTask = new RepairTask();
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);
        repairTask.setUpdateEndTime(updateRepairTaskBO.getUpdateEndTime());
        // 更新状况卡控用当前环节(核损核价)
        repairTask.setControlStatusCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
        // 修理厂修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairRepairTotalAmount())) {
            repairTask.setRepairRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairRepairTotalAmount()));
        }
        // 修理厂定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairInsuranceTotalAmount())) {
            repairTask.setRepairInsuranceTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairInsuranceTotalAmount()));
        }
        // 车管换件金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleReplaceTotalAmount())) {
            repairTask.setVehicleReplaceTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleReplaceTotalAmount()));
        }
        // 车管修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleRepairTotalAmount())) {
            repairTask.setVehicleRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleRepairTotalAmount()));
        }
        // 车管定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleInsuranceTotalAmount())) {
            repairTask.setVehicleInsuranceTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount()));
        }
        // 是否需要复勘
        if (StringUtils.isNotBlank(updateRepairTaskBO.getResurveyFlag())) {
            repairTask.setResurveyFlag(new BigDecimal(updateRepairTaskBO.getResurveyFlag()));
        }
        // 保养费用
        if (StringUtils.isNotBlank(updateRepairTaskBO.getMaintainAmount())) {
            repairTask.setMaintainAmount(new BigDecimal(updateRepairTaskBO.getMaintainAmount()));
        }
        // 复勘部位
        repairTask.setResurveyPart(updateRepairTaskBO.getResurveyPart());
        // 责任情况
        if (StringUtils.isNotBlank(updateRepairTaskBO.getDutySituation())) {
            repairTask.setDutySituation(new BigDecimal(updateRepairTaskBO.getDutySituation()));
        }
        // 向用户追偿费用
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRecoveryAmount())) {
            repairTask.setRecoveryAmount(new BigDecimal(updateRepairTaskBO.getRecoveryAmount()));
        }
        // 保险上付费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getInsuranceAmount())) {
            repairTask.setInsuranceAmount(new BigDecimal(updateRepairTaskBO.getInsuranceAmount()));
        }
        // 加速折旧费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getAccDepAmount())) {
            repairTask.setAccDepAmount(new BigDecimal(updateRepairTaskBO.getAccDepAmount()));
        }
        // 停运损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getOutageLossAmount())) {
            repairTask.setOutageLossAmount(new BigDecimal(updateRepairTaskBO.getOutageLossAmount()));
        }
        // 车辆损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleLossAmount())) {
            repairTask.setVehicleLossAmount(new BigDecimal(updateRepairTaskBO.getVehicleLossAmount()));
        }
        // 拖车救援费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getTrailerRescueAmount())) {
            repairTask.setTrailerRescueAmount(new BigDecimal(updateRepairTaskBO.getTrailerRescueAmount()));
        }
        if ("1".equals(repairTaskView.getAdvancedAuditLeve())) {
            // 一级核损人
            repairTask.setVerificationLossCheckId(operId);
        }
        if (updateRepairTaskBO.getConfirmType() != null) {
            repairTask.setConfirmType(updateRepairTaskBO.getConfirmType());
        }
        // 确认车损状态
        if (updateRepairTaskBO.getConfirmCarDamageType() != null) {
            repairTask.setConfirmCarDamageType(updateRepairTaskBO.getConfirmCarDamageType());
        }
        // 预估理赔金额
        if (updateRepairTaskBO.getEstimatedClaimAmount() != null) {
            repairTask.setEstimatedClaimAmount(updateRepairTaskBO.getEstimatedClaimAmount());
            if (repairTask.getEstimatedClaimAmount().compareTo(repairTaskView.getEstimatedClaimAmount()) != 0) {
                // 修改预估理赔金额日志
                String updateLog = "修改预估理赔金额：" + repairTask.getEstimatedClaimAmount();
                LogUtil.saveMtcOperatorLog(mtcOperatorLogMapper, id, updateLog, CurrentTacheEnum.getEnumDesc(repairTaskView.getCurrentTache()), comModel, time, repairTaskView.getCurrentTache(), Contants.ONE);
            }
        }
        // 核损通过时间
        repairTask.setVerificationLossCheckTime(time);
        // 更新车辆残值
        repairTask.setVehicleScrapeValue(this.queryCurrentVehicleScrapeValue(repairTaskNew.getVin()));
        // 预计修理天数
        if (StringUtils.isNotBlank(updateRepairTaskBO.getExpectedRepairDays())) {
            repairTask.setExpectedRepairDays(Long.valueOf(updateRepairTaskBO.getExpectedRepairDays()));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date;
            try {
                date = sdf.parse(repairTaskView.getVehicleReciveTime());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, Integer.parseInt(updateRepairTaskBO.getExpectedRepairDays()));
                // 车辆接受时间
                repairTask.setExpectedRepairComplete(Timestamp.valueOf(sdf.format(calendar.getTime())));
            } catch (ParseException e) {
                log.error("预计修理完成时间计算错误");
                e.printStackTrace();
            }

        }
        repairTask.setVerificationLossTaskOperId(null);

        // 获取下一审核级别配置
        MtcApprovalConfigDTO mtcApprovalConfigDTO = mtcApprovalConfigMapper.getNextLevelConfig(Integer.parseInt(advancedAuditLeve));
        // 存在下一级配置且核损金额大于等于配置金额 需进入下一级审核
        boolean intoNextStageAudit = mtcApprovalConfigDTO != null
                && Double.parseDouble(updateRepairTaskBO.getVehicleInsuranceTotalAmount()) >= mtcApprovalConfigDTO.getAmount().doubleValue();
        if (intoNextStageAudit) {
            repairTask.setVerificationLossTaskSchedule(300L);
            repairTask.setAdvancedAuditLeve(mtcApprovalConfigDTO.getLevel() + StringUtils.EMPTY);
        } else {
            repairTask.setVerificationLossTaskSchedule(320L);
            // 提前提车任务
            if (StringUtils.equals(Contants.VEHICLE_CHECK_ALERADY + StringUtils.EMPTY,
                    repairTaskView.getVehicleCheckTaskSchedule())) {
                repairTask.setVehicleCheckTaskSchedule((long) Contants.VEHICLE_CHECK_COMPLETED);
                repairTask.setMaterialCollectionTaskSchedule(700);
                repairTask.setCurrentTache((long) Contants.CURRENT_TACHE_MATERIAL_COLLECTION);
                LogUtil.saveMtcOperatorLog(mtcOperatorLogMapper, id, "待处理", "材料收集", comModel, time, 70L);
            } else {
                repairTask.setCurrentTache((long) Contants.CURRENT_TACHE_VEHICLE_REPAIR);
                repairTask.setVehicleRepairTaskSchedule(500L);
            }
        }

        AccidentDamageDTO accidentDamageDTO = null;
        // 非转自费的事故维修任务
        if (Integer.parseInt(repairTaskNew.getRepairTypeId()) == Contants.REPAIR_TYPE_ID_ONE && repairTaskNew.getReviewToSelFeeFlag() == 0) {
            // 核损金额超预审金额130%
            if (Integer.parseInt(repairTaskView.getInsurancePreReviewTaskSchedule()) == Contants.INSURANCE_PRE_REVIEW_PASS) {
                BigDecimal maxVehicleInsuranceTotalAmount = new BigDecimal(repairTaskView.getRepairReviewTotalAmount()).multiply(BigDecimal.valueOf(1.3));
                if (maxVehicleInsuranceTotalAmount.compareTo(new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount())) < 0) {
                    if (updateRepairTaskBO.getCheckFlag() == 0){
                        vo.setCode(-99);
                        vo.setMessage("核损金额超过预审金额130%，不可审核通过！");
                        return vo;
                    }
                }
            }

            /*// 获取事故任务理赔金额
            accidentDamageDTO = manualUtils.getAccidentDamage(repairTaskView.getAccidentNo(), comModel.getToken());
            if (null != accidentDamageDTO && StringUtils.isNotBlank(accidentDamageDTO.getTotalDamageAmount())) {
                if (accidentDamageDTO.getHasOwnerDamageLoad() == 1) {
                    // 整理事故任务剩余金额
                    BigDecimal remainingAmount = new BigDecimal(accidentDamageDTO.getTotalDamageAmount()).subtract(getVehicleInsuranceTotalAmountByAccidentNo(repairTaskView.getAccidentNo()));
                    if (remainingAmount.compareTo(new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount())) < 0) {
                        vo.setCode(Contants.RETURN_ERROR_CODE);
                        vo.setMessage("核损金额超过关联事故任务的剩余保司定损单总金额，不可审核通过！");
                        return vo;
                    }
                }
            }*/
        }
        // 维修任务垫付
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustPaysDirect())) {
            repairTask.setCustPaysDirect(updateRepairTaskBO.getCustPaysDirect());
        }
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
            repairTask.setCustAmount(updateRepairTaskBO.getCustAmount());
        }
        // 先保存
        Integer count = repairTaskMapper.updateRepairTask(repairTask);
        if (count > 0) {
            // 再维护三大金额（维修成本金额、结算金额、税前结算金额）
            if (null != accidentDamageDTO && accidentDamageDTO.hasClaimAmount()) {
                mtcRepairTaskService.updateRepairManual(repairTaskNew.getTaskNo(), updateRepairTaskBO.getClaimsFlag(), accidentDamageDTO.getClaimAmount());
            } else {
                mtcRepairTaskService.updateRepairManual(repairTaskNew.getTaskNo(), updateRepairTaskBO.getClaimsFlag(), updateRepairTaskBO.getEstimatedClaimAmount());
            }
            // 删除并重新保存损坏部位图片
            List<VehicleRepairPic> allVehicleRepairPics = vehicleRepairPicMapper.getAllPicListByTaskNo(repairTaskView.getTaskNo());
            // 损坏部位图片
            List<VehicleRepairPic> insertPicList = new ArrayList<>(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)),
                    BigDecimal.valueOf(1),
                    repairTaskView.getTaskNo(),
                    comModel));
            List<VehicleRepairPic> deletePicList = new ArrayList<>(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)));
            // 客户直付凭证
            insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)),
                    BigDecimal.valueOf(22),
                    repairTaskView.getTaskNo(),
                    comModel));
            deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)));
            // 损坏部位视频
            insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)),
                    BigDecimal.valueOf(23),
                    repairTaskView.getTaskNo(),
                    comModel));
            deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)));

            if (CollectionUtils.isNotEmpty(deletePicList)) {
                // 批量删除
                vehicleRepairPicMapper.delMaterialPic(deletePicList.stream().map(VehicleRepairPic::getId).collect(Collectors.toList()));
            }
            String insertRepairPicLog = "";
            if (CollectionUtils.isNotEmpty(insertPicList)) {
                // 批量插入
                vehicleRepairPicMapper.batchInsert(insertPicList);
                if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(1)))) {
                    insertRepairPicLog += "新增上传损坏部位图片；";
                }
                if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(23)))) {
                    insertRepairPicLog += "新增上传损坏部位视频；";
                }
            }
            // 保存修理项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getRepairItemDetail())) {
                for (ViewRepairItemDetailBO viewRepairItemDetail : updateRepairTaskBO.getRepairItemDetail()) {
                    RepairItemDetail repairItemDetail = new RepairItemDetail();
                    repairItemDetail.setId(Long.valueOf(viewRepairItemDetail.getId()));
                    repairItemDetail.setRemark(viewRepairItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewRepairItemDetail.getViewAmount())) {
                        repairItemDetail.setViewAmount(new BigDecimal(viewRepairItemDetail.getViewAmount()));
                    }
                    repairItemDetail.setUpdateOperId(operId);
                    repairItemDetail.setUpdateOperName(operName);
                    repairItemDetail.setUpdateTime(time);
                    // 更新
                    repairItemDetailMapper.updateByIdSelective(repairItemDetail);
                }
            }

            // 保存换件项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getReplaceItemDetail())) {
                for (ViewReplaceItemDetailBO viewReplaceItemDetail : updateRepairTaskBO.getReplaceItemDetail()) {
                    ReplaceItemDetail replaceItemDetail = new ReplaceItemDetail();
                    replaceItemDetail.setId(Long.valueOf(viewReplaceItemDetail.getId()));
                    replaceItemDetail.setRemark(viewReplaceItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewReplaceItemDetail.getViewAmount())) {
                        replaceItemDetail.setViewAmount(new BigDecimal(viewReplaceItemDetail.getViewAmount()));
                    }
                    replaceItemDetail.setUpdateOperId(operId);
                    replaceItemDetail.setUpdateOperName(operName);
                    replaceItemDetail.setUpdateTime(time);
                    // 更新
                    replaceItemDetailMapper.updateByIdSelective(replaceItemDetail);
                }
            }
            String checkName = LAPricelevel + "级";
            // if (LAPricelevel == 2) {
            // checkName = "运营经理";
            // } else if (LAPricelevel == 3) {
            // checkName = "资产管理部";
            // } else if (LAPricelevel == 4) {
            // checkName = "运营管理部";
            // }

            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            StringBuilder opeContent = new StringBuilder();
            opeContent.append(checkName).append("审核通过，金额为：").append(updateRepairTaskBO.getVehicleInsuranceTotalAmount()).append("元。").append(insertRepairPicLog);
            // 确认车损类型变更记录日志
            if (null != updateRepairTaskBO.getConfirmCarDamageType() && !updateRepairTaskBO.getConfirmCarDamageType().equals(repairTaskNew.getConfirmCarDamageType())) {
                opeContent.append("确认车损类型变更为：").append(ConfirmCarDamageTypeEnum.getEnumDesc(updateRepairTaskBO.getConfirmCarDamageType()));
            }
            // 维修垫付日志inject
            Integer custPaysDirect = updateRepairTaskBO.getCustPaysDirect();
            if (ObjectUtil.isNotEmpty(custPaysDirect)) {
                if (custPaysDirect > 0) {
                    if (custPaysDirect == 1) {
                        opeContent.append(" 是否客户直付:是");
                    } else if (custPaysDirect == 2) {
                        opeContent.append(" 是否客户直付:否");
                    }
                }
                if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
                    opeContent.append(", 金额"+ NumberUtil.decimalFormat("0.00",updateRepairTaskBO.getCustAmount()));
                }
            }
            mtcOperatorLog.setOpeContent(opeContent.toString());
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
            // 添加log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
//            // 如果预估理赔金额不为空且是最后一级审核则需维保手动计提日志
//            if (ArrayUtils.contains(Contants.UPD_MANUAL_LIST, repairTaskNew.getRepairTypeId()) && !intoNextStageAudit) {
//                StringBuilder content = new StringBuilder();
//                if (null != updateRepairTaskBO.getEstimatedClaimAmount()) {
//                    content.append("核损通过，预估理赔金额：").append(updateRepairTaskBO.getEstimatedClaimAmount()).append("元");
//                }
//                manualUtils.saveManualLog(repairTask.getId(), String.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS), content.toString(), comModel);
//            }
//            if (ArrayUtils.contains(Contants.NO_MANUAL_LIST, repairTaskNew.getRepairTypeId()) && !intoNextStageAudit) {
//                manualUtils.saveManualLog(repairTask.getId(), String.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS), "核损完成", comModel);
//            }

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    ThreadPoolUtils.EXECUTOR.submit(() -> {
                        // 核损核价审核通过
                        // 整体调整客户直付金额
                        repairTaskService.adjustCustAmount(new OverallAdjustCustDTO(repairTaskView.getTaskNo(), repairTaskView.getCustPaysDirect(), repairTaskView.getCustAmount()), comModel);

                        // 业财对接-同步自费金额账单
                        bfcCostService.syncSelfFundedAmountBill(repairTaskView.getTaskNo());
                    });
                }
            });

            if (repairTask.getVerificationLossTaskSchedule().equals(320L)) {
                if (advanceCount == 0) {
                    MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
                    mtcProcessLog.setUpdateOperId(operId);
                    mtcProcessLog.setUpdateOperName(operName);
                    mtcProcessLog.setUpdateTime(time);
                    mtcProcessLog.setCreateOperId(operId);
                    mtcProcessLog.setCreateOperName(operName);
                    mtcProcessLog.setCreateTime(time);
                    mtcProcessLog.setRemark("车辆维修");
                    mtcProcessLog.setRecoderId(id + StringUtils.EMPTY);
                    mtcProcessLog.setStatus(0);
                    mtcProcessLog.setOpeContent("维修中");
                    mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
                    mtcProcessLog.setCurrentTache((long) Contants.CURRENT_TACHE_VEHICLE_REPAIR);
                    // 添加流程log
                    mtcOperatorLogMapper.saveSelective(mtcProcessLog);
                }
                // 新增维保记录
                if (ArrayUtils.contains(Contants.MANUAL_LIST, repairTaskNew.getRepairTypeId())) {
                    repairTask.setTaskNo(repairTaskNew.getTaskNo());
                    manualUtils.saveOrUpdateManualTask(repairTask, accidentDamageDTO, comModel);
                }
                if (null != accidentDamageDTO && accidentDamageDTO.hasClaimAmount()) {
                    manualUtils.doUpdateRepairTaskAmount(accidentDamageDTO);
                }

                // 保存之后取得详情
                AtcPricingAdoptBO atcPricingAdopt = new AtcPricingAdoptBO();

                BeanCopyUtils.copyProperties(repairTaskNew, atcPricingAdopt);
                // 生成追偿任务
                if (StringUtils.isNotBlank(repairTaskNew.getAssociatedOrder())) {

                    MtcCostRecourseTask mtcCostRecourseTask = new MtcCostRecourseTask();
                    mtcCostRecourseTask.setTaskSchedule("0");
                    mtcCostRecourseTask.setUpdateOperId(operId);
                    mtcCostRecourseTask.setUpdateOperName(operName);
                    mtcCostRecourseTask.setUpdateTime(time);
                    mtcCostRecourseTask.setCreateOperId(operId);
                    mtcCostRecourseTask.setCreateOperName(operName);
                    mtcCostRecourseTask.setCreateTime(time);
                    // 任务编号
                    String costNo = "FYZC" + StringUtils.substring(repairTaskView.getTaskNo(), 4);

                    mtcCostRecourseTask.setCostNo(costNo);
                    mtcCostRecourseTask.setRepairTaskId(Long.valueOf(repairTaskView.getId()));

                    mtcCostRecourseTaskMapper.save(mtcCostRecourseTask);

                    atcPricingAdopt.setRecoveryFlag("1");

                    mtcOperatorLog.setUpdateOperId(operId);
                    mtcOperatorLog.setUpdateOperName(operName);
                    mtcOperatorLog.setUpdateTime(time);
                    mtcOperatorLog.setCreateOperId(operId);
                    mtcOperatorLog.setCreateOperName(operName);
                    mtcOperatorLog.setCreateTime(time);
                    mtcOperatorLog.setRemark("追偿任务");
                    mtcOperatorLog.setRecoderId(mtcCostRecourseTask.getId() + StringUtils.EMPTY);
                    mtcOperatorLog.setStatus(Contants.ONE);
                    mtcOperatorLog.setOpeContent("生成追偿任务");
                    mtcOperatorLog.setTableName("mtc_cost_recourse_task");
                    // 添加log
                    mtcOperatorLogMapper.saveSelective(mtcOperatorLog);

                } else {
                    atcPricingAdopt.setRecoveryFlag("0");
                }

                atcPricingAdopt.setRepareItemDetailList(repairTaskNew.getRepairItemDetail());
                atcPricingAdopt.setReplaceItemDetailList(repairTaskNew.getReplaceItemDetail());
                atcPricingAdopt.setInsuranceTotalAmount(repairTaskNew.getRepairInsuranceTotalAmount());
                atcPricingAdopt.setDispatchTaskSeq(repairTaskNew.getTaskNo());

                // 通过修理厂id取得所在省市区
                AtcPricingAdoptBO repairTaskArea = repairDepotInfoMapper.getDepotArea(repairTaskNew.getRepairDepotId());
                if (repairTaskArea != null) {
                    atcPricingAdopt.setProvinceName(repairTaskArea.getProvinceName());
                    atcPricingAdopt.setCityName(repairTaskArea.getCityName());
                    atcPricingAdopt.setAreaName(repairTaskArea.getAreaName());

                }

                if (!repairTaskNew.getTaskNo().startsWith("BYZX") && !repairTaskNew.getRepairTypeId().equals("3") && repairTaskNew.getOrigin() == 0) {
                    // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
                    int idsUseMode = 1;
                    try {
                        idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
                    } catch (Exception e) {
                        idsUseMode = 1;
                    }

                    if (idsUseMode == 1) {
                        String info;

                        log.error("给调度之前 任务编号:" + repairTaskNew.getTaskNo() + "任务编号:" + repairTaskNew.getTaskNo()
                                + "  预计修理完成日期：" + atcPricingAdopt.getExpectedRepairComplete());

                        // 提前提车
                        try {
                            if (StringUtils.equals(repairTaskView.getVehicleCheckTaskSchedule(),
                                    Contants.VEHICLE_CHECK_ALERADY + StringUtils.EMPTY)) {
                                info = HttpRequest.postMethod(apolloPropertyUtils.getString("evcard.ids.dns")
                                        + "repair-factory-new/mtcUploadFee", atcPricingAdopt);
                            } else {
                                info = HttpRequest.postMethod(apolloPropertyUtils.getString("evcard.ids.dns")
                                        + "repair-factory-new/mtcPricingAdopt", atcPricingAdopt);
                            }
                        } catch (Exception ex) {
                            log.info("调度系统异常:" + ex.toString());
                            throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                        }

                        log.info("核损核价完成调用调度接口：" + info);

                        if (StringUtils.isBlank(info)) {
                            throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                        } else {
                            vo = JSON.parseObject(info, DefaultServiceRespDTO.class);

                            if (vo.getCode() != 0) {
                                throw new MtcBusinessRuntimeException(vo.getMessage(), String.valueOf(vo.getCode()));
                            }
                        }

                    }
                }

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        //触发车辆核损通过 通知
                        log.info("发送核损核价2.3.4级消息" + repairTaskNew.getTaskNo() + intoNextStageAudit);
                        vehicleMaintenanceLossAuditSubject.notifyAuditApprove(new VehicleMaintenanceLossAuditEvent(new Date(), repairTaskNew.getTaskNo(), intoNextStageAudit));
                        // 记录任务状态持续时间
                        vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));

                        // 梧桐维修任务增加日志
                        SsoUserBaseInfoDto ssoUserBaseInfoDto = ssoUserService.getUserByUserName(comModel.getUserName());
                        repairTaskService.addRepairTaskLogForWt(new AddRepairTaskLogForWtDTO(
                                repairTaskNew.getTaskNo(), "核损核价通过", "",
                                comModel.getUserName(), ssoUserBaseInfoDto.getdAccount()
                        ));
                    }
                });

                if (!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskNew.getRepairTypeId()), repairTaskNew.getReviewToSelFeeFlag())) {
                    // 任务状态同步（请求定损系统）
                    String orgName = orgInfoMapper.getOrgName(comModel.getOrgId());
                    // PACKET > HEAD
                    HeadBean headBean = new HeadBean();
                    headBean.setRequestType("007");
                    headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
                    // PACKET > BODY >EvalLossInfo
                    EvalLossInfoSynchronize evalLossInfoSynchronize = new EvalLossInfoSynchronize();
                    evalLossInfoSynchronize.setLossNo(repairTaskView.getTaskNo());
                    evalLossInfoSynchronize.setReportCode(repairTaskView.getTaskNo());
                    evalLossInfoSynchronize.setDmgVhclId(repairTaskView.getTaskNo());
                    evalLossInfoSynchronize.setStatusCode("016");
                    evalLossInfoSynchronize.setStatusName("核损通过");
                    evalLossInfoSynchronize.setComCode(comModel.getOrgId());
                    evalLossInfoSynchronize.setCompany(orgName);
                    evalLossInfoSynchronize.setBranchComCode(comModel.getOrgId());
                    evalLossInfoSynchronize.setBranchComName(orgName);
                    evalLossInfoSynchronize.setHandlerCode(String.valueOf(operId));
                    evalLossInfoSynchronize.setHandlerName(operName);
                    evalLossInfoSynchronize.setOperationLink("07");
                    evalLossInfoSynchronize.setOperationResults("核损通过");
                    // PACKET > BODY
                    BodyBeanSynchronize bodyBeanSynchronize = new BodyBeanSynchronize();
                    bodyBeanSynchronize.setEvalLossInfoSynchronize(evalLossInfoSynchronize);
                    // PACKET
                    PacketBeanSynchronize packetBeanSynchronize = new PacketBeanSynchronize();
                    packetBeanSynchronize.setHeadBean(headBean);
                    packetBeanSynchronize.setBodyBeanSynchronize(bodyBeanSynchronize);
                    StringWriter stringWriter = new StringWriter();
                    JAXB.marshal(packetBeanSynchronize, stringWriter);
                    String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "007");
                    Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
                    if (MapUtils.isEmpty(resultMap)) {
                        log.error(" ：-------------->" + JSON.toJSONString(resultMap));
                        return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务状态同步失败");
                    }
                    Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
                    if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
                        log.error("任务状态同步失败：-------------->" + JSON.toJSONString(resultMap));
                        return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务状态同步失败");
                    }
                }
            }
        } else {
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            vo.setCode(Contants.RETURN_ERROR_CODE);
        }
        return vo;
    }

    /**
     * 核损核价审核通过
     *
     * @param id 任务id
     * @return
     */
    @Override
    public DefaultServiceRespDTO updateReviewPassTaskNew(Long id, UpdateRepairTaskBO updateRepairTaskBO, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(id);
        // 获取登陆用户信息
        ComModel comModel = ComUtil.getUserInfo(request);

        // 核损核价预检查
        DefaultServiceRespDTO preCheckDto = updateReviewPassTaskPreCheck(repairTaskNew, updateRepairTaskBO, request);
        if (preCheckDto.getCode() < 0) {
            return preCheckDto;
        }

        return doUpdateReviewPassTask(repairTaskNew, updateRepairTaskBO, comModel);
    }

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_UNCOMMITTED)
    private DefaultServiceRespDTO doUpdateReviewPassTask(RepairTaskViewBO repairTaskView, UpdateRepairTaskBO updateRepairTaskBO, ComModel comModel){
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        comModel.setUpdateTime(new Timestamp(System.currentTimeMillis()));

        RepairTask updateRepairTask = packageUpdateRepairTask(repairTaskView, updateRepairTaskBO, comModel);
        // 先保存
        Integer count = repairTaskMapper.updateRepairTask(updateRepairTask);
        if (count > 0) {
            // 删除并重新保存损坏部位图片
            List<VehicleRepairPic> allVehicleRepairPics = vehicleRepairPicMapper.getAllPicListByTaskNo(repairTaskView.getTaskNo());
            // 损坏部位图片
            List<VehicleRepairPic> insertPicList = new ArrayList<>(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)),
                    BigDecimal.valueOf(1),
                    repairTaskView.getTaskNo(),
                    comModel));
            List<VehicleRepairPic> deletePicList = new ArrayList<>(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartPicture(), BigDecimal.valueOf(1)));
            // 客户直付凭证
            insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)),
                    BigDecimal.valueOf(22),
                    repairTaskView.getTaskNo(),
                    comModel));
            deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getCustPicture(), BigDecimal.valueOf(22)));
            // 损坏部位视频
            insertPicList.addAll(RepairTaskUtils.transferStringToVehicleRepairPic(
                    RepairTaskUtils.getInsertPics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)),
                    BigDecimal.valueOf(23),
                    repairTaskView.getTaskNo(),
                    comModel));
            deletePicList.addAll(RepairTaskUtils.getDeletePics(allVehicleRepairPics, updateRepairTaskBO.getDamagedPartVideo(), BigDecimal.valueOf(23)));

            if (CollectionUtils.isNotEmpty(deletePicList)) {
                // 批量删除
                vehicleRepairPicMapper.delMaterialPic(deletePicList.stream().map(VehicleRepairPic::getId).collect(Collectors.toList()));
            }
            String insertRepairPicLog = "";
            if (CollectionUtils.isNotEmpty(insertPicList)) {
                // 批量插入
                vehicleRepairPicMapper.batchInsert(insertPicList);
                if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(1)))) {
                    insertRepairPicLog += "新增上传损坏部位图片；";
                }
                if (insertPicList.stream().anyMatch(vehicleRepairPic -> vehicleRepairPic.getPicType().equals(BigDecimal.valueOf(23)))) {
                    insertRepairPicLog += "新增上传损坏部位视频；";
                }
            }
            // 保存修理项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getRepairItemDetail())) {
                for (ViewRepairItemDetailBO viewRepairItemDetail : updateRepairTaskBO.getRepairItemDetail()) {
                    RepairItemDetail repairItemDetail = new RepairItemDetail();
                    repairItemDetail.setId(Long.valueOf(viewRepairItemDetail.getId()));
                    repairItemDetail.setRemark(viewRepairItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewRepairItemDetail.getViewAmount())) {
                        repairItemDetail.setViewAmount(new BigDecimal(viewRepairItemDetail.getViewAmount()));
                    }
                    repairItemDetail.setUpdateOperId(comModel.getCreateOperId());
                    repairItemDetail.setUpdateOperName(comModel.getCreateOperName());
                    repairItemDetail.setUpdateTime(comModel.getUpdateTime());
                    // 更新
                    repairItemDetailMapper.updateByIdSelective(repairItemDetail);
                }
            }

            // 保存换件项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getReplaceItemDetail())) {
                for (ViewReplaceItemDetailBO viewReplaceItemDetail : updateRepairTaskBO.getReplaceItemDetail()) {
                    ReplaceItemDetail replaceItemDetail = new ReplaceItemDetail();
                    replaceItemDetail.setId(Long.valueOf(viewReplaceItemDetail.getId()));
                    replaceItemDetail.setRemark(viewReplaceItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewReplaceItemDetail.getViewAmount())) {
                        replaceItemDetail.setViewAmount(new BigDecimal(viewReplaceItemDetail.getViewAmount()));
                    }
                    replaceItemDetail.setUpdateOperId(comModel.getCreateOperId());
                    replaceItemDetail.setUpdateOperName(comModel.getCreateOperName());
                    replaceItemDetail.setUpdateTime(comModel.getUpdateTime());
                    // 更新
                    replaceItemDetailMapper.updateByIdSelective(replaceItemDetail);
                }
            }

            // 取得用户核损核价级别
            Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(comModel.getUserName(), comModel.getOrgId()) == null ? 0 : mtcUserMapper.getUserLAPricelevel(comModel.getUserName(), comModel.getOrgId());
            String checkName = LAPricelevel + "级";

            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(comModel.getCreateOperId());
            mtcOperatorLog.setUpdateOperName(comModel.getCreateOperName());
            mtcOperatorLog.setUpdateTime(comModel.getUpdateTime());
            mtcOperatorLog.setCreateOperId(comModel.getCreateOperId());
            mtcOperatorLog.setCreateOperName(comModel.getCreateOperName());
            mtcOperatorLog.setCreateTime(comModel.getUpdateTime());
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(repairTaskView.getId() + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            StringBuilder opeContent = new StringBuilder();
            opeContent.append(checkName).append("审核通过，金额为：").append(updateRepairTaskBO.getVehicleInsuranceTotalAmount()).append("元。").append(insertRepairPicLog);
            // 确认车损类型变更记录日志
            if (null != updateRepairTaskBO.getConfirmCarDamageType() && !updateRepairTaskBO.getConfirmCarDamageType().equals(repairTaskView.getConfirmCarDamageType())) {
                opeContent.append("确认车损类型变更为：").append(ConfirmCarDamageTypeEnum.getEnumDesc(updateRepairTaskBO.getConfirmCarDamageType()));
            }
            // 维修垫付日志inject
            Integer custPaysDirect = updateRepairTaskBO.getCustPaysDirect();
            if (ObjectUtil.isNotEmpty(custPaysDirect)) {
                if (custPaysDirect > 0) {
                    if (custPaysDirect == 1) {
                        opeContent.append(" 是否客户直付:是");
                    } else if (custPaysDirect == 2) {
                        opeContent.append(" 是否客户直付:否");
                    }
                }
                if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
                    opeContent.append(", 金额").append(NumberUtil.decimalFormat("0.00", updateRepairTaskBO.getCustAmount()));
                }
            }
            mtcOperatorLog.setOpeContent(opeContent.toString());
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
            // 添加log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);

            // 业财对接-同步自费金额账单
            bfcCostService.syncSelfFundedAmountBill(repairTaskView.getTaskNo());

            if (updateRepairTask.getVerificationLossTaskSchedule().equals(320L)) {
                int advanceCount = vehicleAdvanceCheckTaskMapper.getadvCheck(updateRepairTaskBO.getTaskNo());
                if (advanceCount == 0) {
                    MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
                    mtcProcessLog.setUpdateOperId(comModel.getCreateOperId());
                    mtcProcessLog.setUpdateOperName(comModel.getCreateOperName());
                    mtcProcessLog.setUpdateTime(comModel.getUpdateTime());
                    mtcProcessLog.setCreateOperId(comModel.getCreateOperId());
                    mtcProcessLog.setCreateOperName(comModel.getCreateOperName());
                    mtcProcessLog.setCreateTime(comModel.getUpdateTime());
                    mtcProcessLog.setRemark("车辆维修");
                    mtcProcessLog.setRecoderId(updateRepairTask.getId() + StringUtils.EMPTY);
                    mtcProcessLog.setStatus(0);
                    mtcProcessLog.setOpeContent("维修中");
                    mtcProcessLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
                    mtcProcessLog.setCurrentTache((long) Contants.CURRENT_TACHE_VEHICLE_REPAIR);
                    // 添加流程log
                    mtcOperatorLogMapper.saveSelective(mtcProcessLog);
                }
                // 新增维保记录
                if (ArrayUtils.contains(Contants.MANUAL_LIST, repairTaskView.getRepairTypeId())) {
                    updateRepairTask.setTaskNo(repairTaskView.getTaskNo());
                    manualUtils.saveOrUpdateManualTask(updateRepairTask, null, comModel);
                }

                // 保存之后取得详情
                AtcPricingAdoptBO atcPricingAdopt = new AtcPricingAdoptBO();

                BeanCopyUtils.copyProperties(repairTaskView, atcPricingAdopt);
                // 生成追偿任务
                if (StringUtils.isNotBlank(repairTaskView.getAssociatedOrder())) {

                    MtcCostRecourseTask mtcCostRecourseTask = new MtcCostRecourseTask();
                    mtcCostRecourseTask.setTaskSchedule("0");
                    mtcCostRecourseTask.setUpdateOperId(comModel.getCreateOperId());
                    mtcCostRecourseTask.setUpdateOperName(comModel.getCreateOperName());
                    mtcCostRecourseTask.setUpdateTime(comModel.getUpdateTime());
                    mtcCostRecourseTask.setCreateOperId(comModel.getCreateOperId());
                    mtcCostRecourseTask.setCreateOperName(comModel.getCreateOperName());
                    mtcCostRecourseTask.setCreateTime(comModel.getUpdateTime());
                    // 任务编号
                    String costNo = "FYZC" + StringUtils.substring(updateRepairTask.getTaskNo(), 4);

                    mtcCostRecourseTask.setCostNo(costNo);
                    mtcCostRecourseTask.setRepairTaskId(updateRepairTask.getId());

                    mtcCostRecourseTaskMapper.save(mtcCostRecourseTask);

                    atcPricingAdopt.setRecoveryFlag("1");

                    mtcOperatorLog.setUpdateOperId(comModel.getCreateOperId());
                    mtcOperatorLog.setUpdateOperName(comModel.getCreateOperName());
                    mtcOperatorLog.setUpdateTime(comModel.getUpdateTime());
                    mtcOperatorLog.setCreateOperId(comModel.getCreateOperId());
                    mtcOperatorLog.setCreateOperName(comModel.getCreateOperName());
                    mtcOperatorLog.setCreateTime(comModel.getUpdateTime());
                    mtcOperatorLog.setRemark("追偿任务");
                    mtcOperatorLog.setRecoderId(mtcCostRecourseTask.getId() + StringUtils.EMPTY);
                    mtcOperatorLog.setStatus(Contants.ONE);
                    mtcOperatorLog.setOpeContent("生成追偿任务");
                    mtcOperatorLog.setTableName("mtc_cost_recourse_task");
                    // 添加log
                    mtcOperatorLogMapper.saveSelective(mtcOperatorLog);

                } else {
                    atcPricingAdopt.setRecoveryFlag("0");
                }

                atcPricingAdopt.setRepareItemDetailList(repairTaskView.getRepairItemDetail());
                atcPricingAdopt.setReplaceItemDetailList(repairTaskView.getReplaceItemDetail());
                atcPricingAdopt.setInsuranceTotalAmount(repairTaskView.getRepairInsuranceTotalAmount());
                atcPricingAdopt.setDispatchTaskSeq(repairTaskView.getTaskNo());

                // 通过修理厂id取得所在省市区
                AtcPricingAdoptBO repairTaskArea = repairDepotInfoMapper.getDepotArea(repairTaskView.getRepairDepotId());
                if (repairTaskArea != null) {
                    atcPricingAdopt.setProvinceName(repairTaskArea.getProvinceName());
                    atcPricingAdopt.setCityName(repairTaskArea.getCityName());
                    atcPricingAdopt.setAreaName(repairTaskArea.getAreaName());

                }

                if (!repairTaskView.getTaskNo().startsWith("BYZX") && !repairTaskView.getRepairTypeId().equals("3") && repairTaskView.getOrigin() == 0) {
                    // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
                    int idsUseMode = 1;
                    try {
                        idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
                    } catch (Exception e) {
                        idsUseMode = 1;
                    }

                    if (idsUseMode == 1) {
                        String info;

                        log.error("给调度之前 任务编号:" + repairTaskView.getTaskNo() + "任务编号:" + repairTaskView.getTaskNo()
                                + "  预计修理完成日期：" + atcPricingAdopt.getExpectedRepairComplete());

                        // 提前提车
                        try {
                            if (StringUtils.equals(updateRepairTask.getVehicleCheckTaskSchedule().toString(),
                                    Contants.VEHICLE_CHECK_ALERADY + StringUtils.EMPTY)) {
                                info = HttpRequest.postMethod(apolloPropertyUtils.getString("evcard.ids.dns")
                                        + "repair-factory-new/mtcUploadFee", atcPricingAdopt);
                            } else {
                                info = HttpRequest.postMethod(apolloPropertyUtils.getString("evcard.ids.dns")
                                        + "repair-factory-new/mtcPricingAdopt", atcPricingAdopt);
                            }
                        } catch (Exception ex) {
                            log.info("调度系统异常:" + ex.toString());
                            throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                        }

                        log.info("核损核价完成调用调度接口：" + info);

                        if (StringUtils.isBlank(info)) {
                            throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                        } else {
                            vo = JSON.parseObject(info, DefaultServiceRespDTO.class);

                            if (vo.getCode() != 0) {
                                throw new MtcBusinessRuntimeException(vo.getMessage(), String.valueOf(vo.getCode()));
                            }
                        }

                    }
                }

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        //触发车辆核损通过 通知
                        log.info("发送核损核价2.3.4级消息" + repairTaskView.getTaskNo() + updateRepairTaskBO.isIntoNextStageAudit());
                        vehicleMaintenanceLossAuditSubject.notifyAuditApprove(new VehicleMaintenanceLossAuditEvent(new Date(), repairTaskView.getTaskNo(), updateRepairTaskBO.isIntoNextStageAudit()));
                        // 记录任务状态持续时间
                        vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(comModel.getUpdateTime(), updateRepairTask.getTaskNo()));

                        // 梧桐维修任务增加日志
                        SsoUserBaseInfoDto ssoUserBaseInfoDto = ssoUserService.getUserByUserName(comModel.getUserName());
                        repairTaskService.addRepairTaskLogForWt(new AddRepairTaskLogForWtDTO(
                                repairTaskView.getTaskNo(), "核损核价通过", "",
                                comModel.getUserName(), ssoUserBaseInfoDto.getdAccount()
                        ));
                    }
                });

                if (!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskView.getRepairTypeId()), repairTaskView.getReviewToSelFeeFlag())) {
                    // 任务状态同步（请求定损系统）
                    String orgName = orgInfoMapper.getOrgName(comModel.getOrgId());
                    // PACKET > HEAD
                    HeadBean headBean = new HeadBean();
                    headBean.setRequestType("007");
                    headBean.setOperatingTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
                    // PACKET > BODY >EvalLossInfo
                    EvalLossInfoSynchronize evalLossInfoSynchronize = new EvalLossInfoSynchronize();
                    evalLossInfoSynchronize.setLossNo(updateRepairTask.getTaskNo());
                    evalLossInfoSynchronize.setReportCode(updateRepairTask.getTaskNo());
                    evalLossInfoSynchronize.setDmgVhclId(updateRepairTask.getTaskNo());
                    evalLossInfoSynchronize.setStatusCode("016");
                    evalLossInfoSynchronize.setStatusName("核损通过");
                    evalLossInfoSynchronize.setComCode(comModel.getOrgId());
                    evalLossInfoSynchronize.setCompany(orgName);
                    evalLossInfoSynchronize.setBranchComCode(comModel.getOrgId());
                    evalLossInfoSynchronize.setBranchComName(orgName);
                    evalLossInfoSynchronize.setHandlerCode(String.valueOf(comModel.getCreateOperId()));
                    evalLossInfoSynchronize.setHandlerName(comModel.getCreateOperName());
                    evalLossInfoSynchronize.setOperationLink("07");
                    evalLossInfoSynchronize.setOperationResults("核损通过");
                    // PACKET > BODY
                    BodyBeanSynchronize bodyBeanSynchronize = new BodyBeanSynchronize();
                    bodyBeanSynchronize.setEvalLossInfoSynchronize(evalLossInfoSynchronize);
                    // PACKET
                    PacketBeanSynchronize packetBeanSynchronize = new PacketBeanSynchronize();
                    packetBeanSynchronize.setHeadBean(headBean);
                    packetBeanSynchronize.setBodyBeanSynchronize(bodyBeanSynchronize);
                    StringWriter stringWriter = new StringWriter();
                    JAXB.marshal(packetBeanSynchronize, stringWriter);
                    String result = HttpUtils.sendXmlPost(stringWriter.toString(), mtcSystemConfig.getJyInterfaceUrl() + "007");
                    Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
                    if (MapUtils.isEmpty(resultMap)) {
                        log.error(" ：-------------->" + JSON.toJSONString(resultMap));
                        return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务状态同步失败");
                    }
                    Map<String, String> headMap = (Map<String, String>) resultMap.get("HEAD");
                    if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
                        log.error("任务状态同步失败：-------------->" + JSON.toJSONString(resultMap));
                        return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务状态同步失败");
                    }
                }
            }
        } else {
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            vo.setCode(Contants.RETURN_ERROR_CODE);
        }
        return vo;
    }

    /**
     * 核损核价预校验
     *
     * @param repairTaskView     任务实时信息
     * @param updateRepairTaskBO 修改任务信息
     * @param request            request
     * @return
     */
    private DefaultServiceRespDTO updateReviewPassTaskPreCheck(RepairTaskViewBO repairTaskView , UpdateRepairTaskBO updateRepairTaskBO, HttpServletRequest request){
        DefaultServiceRespDTO respDTO = new DefaultServiceRespDTO();
        respDTO.setMessage(Contants.SUCCESS_INFO);

        // 获取登陆用户信息
        ComModel comModel = ComUtil.getUserInfo(request);

        // 触发审核通过时检查车辆运营单位
        // 江苏大区、昆明公司、海口公司、三亚公司、天津公司
        /*
         * 008P 环球车享江苏事业部 008P01 环球车享（苏州）汽车租赁有限公司 008P02 无锡赛可汽车租赁有限公司 008P03
         * 环球车享（常州）汽车租赁有限公司 008P04 环球车享镇江汽车租赁有限公司 008P05 环球车享汽车租赁南通有限公司 008P06
         * 环球车享扬州汽车租赁有限公司 008P07 环球车享盐城汽车租赁有限公司 005T 环球车享（昆明）汽车租赁有限公司 005F
         * 环球车享（海口）汽车租赁有限公司 008L 环球车享（三亚）汽车租赁有限公司 008F 环球车享（天津）汽车租赁有限公司
         */

        // 判断是否有提前提车 若有则必须提车已完成才能审核通过
        int advanceCount = vehicleAdvanceCheckTaskMapper.getadvCheck(updateRepairTaskBO.getTaskNo());
        if (advanceCount > 0) {
            if (!vehicleAdvanceCheckTaskMapper.getSchedule(updateRepairTaskBO.getTaskNo()).equals("3")) {
                respDTO.setCode(Contants.RETURN_ERROR_CODE);
                respDTO.setMessage("当前任务有提前提车申请未处理，请优先处理!");
                return respDTO;
            }
        }

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskView.getTaskNo());

//        if ("1".equals(repairTaskView.getAdvancedAuditLeve())) {
//            // 查询预算
//            BudgetInfoDTO budgetInfoDTO = repairTaskService.getBudgetInfoByTaskNo(repairTaskView.getTaskNo(), request);
//            if (null == budgetInfoDTO) {
//                log.info("【{}】1级核损核价，未查询到预算！", repairTaskView.getTaskNo());
//                respDTO.setCode(Contants.RETURN_ERROR_CODE);
//                respDTO.setMessage("预算不足！请先联系财务调整预算！");
//                return respDTO;
//            }
//            // 计算自费金额
//            BigDecimal selfFundedAmount = BigDecimal.ZERO;
//            AccidentDamageDTO accidentDamageDTO = manualUtils.getAccidentDamage(repairTaskView.getAccidentNo(), comModel.getToken());
//            if (null == accidentDamageDTO || null == accidentDamageDTO.getVehicleTotalLossDisposalFlag() || accidentDamageDTO.getVehicleTotalLossDisposalFlag() != 1) {
//                // 【自费金额】=【维修总金额】-【预估保险理赔金额】-【非用户承担金额】
//                BigDecimal estimatedClaimAmount = null != updateRepairTaskBO.getEstimatedClaimAmount() ? updateRepairTaskBO.getEstimatedClaimAmount() : repairTaskView.getEstimatedClaimAmount();
//                selfFundedAmount = new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount()).subtract(estimatedClaimAmount).subtract(repairTaskView.getUserAssumedAmount());
//                log.info("【{}】自费金额计算：{}-{}-{}", repairTaskView.getTaskNo(), updateRepairTaskBO.getVehicleInsuranceTotalAmount(), estimatedClaimAmount, repairTaskView.getUserAssumedAmount());
//            }
//            log.info("【{}】1级核损核价自费金额：{}，参与预算对比", repairTaskView.getTaskNo(), selfFundedAmount);
//            if (selfFundedAmount.compareTo(BigDecimal.ZERO) > 0) {
//                if (new BigDecimal(budgetInfoDTO.getAvailableBalance()).compareTo(selfFundedAmount) < 0) {
//                    respDTO.setCode(Contants.RETURN_ERROR_CODE);
//                    respDTO.setMessage("预算不足！请先联系财务调整预算！");
//                    return respDTO;
//                }
//            }
//        }

        // 检查是否存在定损数据
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
        mtcLossInfoDTO.setStatus(1);
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        List<MtcRepairItemCheckInfoDTO> checkInfoList = mtcRepairItemCheckInfoService.queryCheckListByTaskNo(repairTaskView.getTaskNo());
        if (CollectionUtils.isEmpty(mtcLossInfoList) && CollectionUtils.isEmpty(checkInfoList)) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "未经过定损系统维修报价，请退回后重新定损");
        } else {
            if (!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskView.getRepairTypeId()), repairTaskView.getReviewToSelFeeFlag())) {
                if (CollectionUtils.isEmpty(mtcLossInfoList)) {
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "配件库切换中，请退回至维修报价重新操作！");
                }
                MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
                if (StringUtils.isBlank(mtcLossInfo.getAuditHandlerCode())) {
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "请先进行核损核价");
                }
            }
        }
        // 核价项目未全部通过，不可审核通过
        if (!RepairTaskUtils.isUseRepairItemLibraryNational(Integer.parseInt(repairTaskView.getRepairTypeId()), repairTaskView.getReviewToSelFeeFlag())) {
            List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper.queryMtcLossFitList(repairTaskView.getTaskNo());
            List<MtcLossRepairInfo> mtcLossRepairInfoList = mtcLossRepairInfoMapper
                    .queryMtcLossRepairList(repairTaskView.getTaskNo());
            List<MtcLossOuterRepairInfo> mtcLossOuterRepairInfoList = mtcLossOuterRepairInfoMapper
                    .queryMtcLossOuterRepairList(repairTaskView.getTaskNo());
            List<MtcLossAssistInfo> mtcLossAssistInfoList = mtcLossAssistInfoMapper
                    .queryMtcLossAssistList(repairTaskView.getTaskNo());
            if (CollectionUtils.isNotEmpty(mtcLossFitInfoList) || CollectionUtils.isNotEmpty(mtcLossRepairInfoList)
                    || CollectionUtils.isNotEmpty(mtcLossOuterRepairInfoList)
                    || CollectionUtils.isNotEmpty(mtcLossAssistInfoList)) {
                return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "核价项目未全部通过，不可审核通过");
            }
        } else {
            for (MtcRepairItemCheckInfoDTO dto : checkInfoList) {
                if (dto.getCheckStatus() != 1) {
                    return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "核价项目未全部通过，不可审核通过");
                }
            }
        }

        // 检查当前环节是否处于核损核价环节
        if (null == taskSchedule || Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            respDTO.setCode(Contants.RETURN_ERROR_CODE);
            respDTO.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return respDTO;
        }
        if (taskSchedule.getVerificationLossTaskSchedule() == Contants.VERIFICATION_LOSS_CLOSED) {
            respDTO.setCode(Contants.RETURN_ERROR_CODE);
            respDTO.setMessage("任务已关闭，请重新刷新一览页面");
            return respDTO;
        }

        // 触发审核通过时检查车辆运营单位
        String lawfulCompany = "";
        try {
            lawfulCompany = apolloPropertyUtils.getString("lawfulCompany");
        } catch (Exception e) {
            lawfulCompany = "";
        }

        // 车辆运营单位
        String orgId = repairTaskView.getOrgId();

        log.error("核损核价审核通过  id:" + repairTaskView.getId());
        log.error("核损核价审核通过  任务编号:" + repairTaskView.getTaskNo());
        log.error("核损核价审核通过  车辆运营单位:" + orgId);
        log.error("核损核价审核通过  登录者所属组织机构ID:" + comModel.getOrgId());

        // 如果触发审核通过时检查车辆运营单位不为空
        if (!"".equals(lawfulCompany.trim())) {
            // 校验当前登录人账户环球时，需在核损核价阶段进行权限限制，触发审核通过时检查车辆运营单位是否为江苏大区、昆明公司、海口公司、三亚公司、天津公司的机构，如不是，则不允许核价通过。
            if ("00".equals(comModel.getOrgId())) {
                // 非法公司
                if (!lawfulCompany.contains("," + orgId + ",")) {
                    respDTO.setCode(Contants.RETURN_ERROR_CODE);
                    respDTO.setMessage("非江苏大区、昆明公司、海口公司、三亚公司、天津公司不允许审核通过");
                    return respDTO;
                }
            }
        }

        respDTO = updateReviewPassTascheck(updateRepairTaskBO);

        // 选择“是”，则复勘部位必填
        if (Contants.NEEDRESURVEY.equals(updateRepairTaskBO.getResurveyFlag())) {
            if (StringUtils.isBlank(updateRepairTaskBO.getResurveyPart())) {
                respDTO.setCode(Contants.RETURN_ERROR_CODE);
                respDTO.setMessage("复勘部位不能为空");
                return respDTO;
            }
        }

        // 事故维修 价格异议
        if (StringUtils.equals("1", updateRepairTaskBO.getVehicleManageViewFlag())) {
            respDTO.setCode(Contants.RETURN_ERROR_CODE);
            respDTO.setMessage("核价异议,不可审核通过");
            return respDTO;
        }

        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(comModel.getUserName(), comModel.getOrgId()) == null ? 0 : mtcUserMapper.getUserLAPricelevel(comModel.getUserName(), comModel.getOrgId());
        if (!StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), LAPricelevel + StringUtils.EMPTY)) {
            respDTO.setCode(Contants.RETURN_ERROR_CODE);
            respDTO.setMessage("该任务不在当前审核级别，请更换相应权限账号");
            return respDTO;
        }

        // 非转自费的事故维修任务
        if (Integer.parseInt(repairTaskView.getRepairTypeId()) == Contants.REPAIR_TYPE_ID_ONE && repairTaskView.getReviewToSelFeeFlag() == 0) {
            // 核损金额超预审金额130%
            if (Integer.parseInt(repairTaskView.getInsurancePreReviewTaskSchedule()) == Contants.INSURANCE_PRE_REVIEW_PASS) {
                BigDecimal maxVehicleInsuranceTotalAmount = new BigDecimal(repairTaskView.getRepairReviewTotalAmount()).multiply(BigDecimal.valueOf(1.3));
                if (maxVehicleInsuranceTotalAmount.compareTo(new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount())) < 0) {
                    if (updateRepairTaskBO.getCheckFlag() == 0){
                        return new DefaultServiceRespDTO(-99, "核损金额超过预审金额130%，不可审核通过！");
                    }
                }
            }
        }

        return respDTO;
    }

    /**
     * 包装更新任务结构
     *
     * @param repairTaskView     任务实时信息
     * @param updateRepairTaskBO 修改任务信息
     * @param comModel           登录人
     * @return 更新任务数据
     */
    private RepairTask packageUpdateRepairTask(RepairTaskViewBO repairTaskView, UpdateRepairTaskBO updateRepairTaskBO, ComModel comModel){
        Timestamp time = comModel.getUpdateTime();

        RepairTask repairTask = new RepairTask();
        BeanCopyUtils.copyProperties(repairTaskView, repairTask);
        repairTask.setId(Long.parseLong(repairTaskView.getId()));
        repairTask.setUpdateOperId(comModel.getCreateOperId());
        repairTask.setUpdateOperName(comModel.getCreateOperName());
        repairTask.setUpdateTime(time);
        repairTask.setUpdateEndTime(updateRepairTaskBO.getUpdateEndTime());
        // 更新状况卡控用当前环节(核损核价)
        repairTask.setCurrentTache(null);
        repairTask.setControlStatusCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
        repairTask.setVehicleReciveTime(new Timestamp(Objects.requireNonNull(DateUtil.getDateFromStr(repairTaskView.getVehicleReciveTime(), DateUtil.DATE_TYPE2)).getTime()));
        repairTask.setVehicleCheckTaskSchedule(Long.parseLong(repairTaskView.getVehicleCheckTaskSchedule()));

        // 修理厂修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairRepairTotalAmount())) {
            repairTask.setRepairRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairRepairTotalAmount()));
        }
        // 修理厂定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairInsuranceTotalAmount())) {
            repairTask.setRepairInsuranceTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairInsuranceTotalAmount()));
        }
        // 车管换件金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleReplaceTotalAmount())) {
            repairTask.setVehicleReplaceTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleReplaceTotalAmount()));
        }
        // 车管修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleRepairTotalAmount())) {
            repairTask.setVehicleRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleRepairTotalAmount()));
        }
        // 车管定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleInsuranceTotalAmount())) {
            repairTask.setVehicleInsuranceTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount()));
        }
        // 是否需要复勘
        if (StringUtils.isNotBlank(updateRepairTaskBO.getResurveyFlag())) {
            repairTask.setResurveyFlag(new BigDecimal(updateRepairTaskBO.getResurveyFlag()));
        }
        // 保养费用
        if (StringUtils.isNotBlank(updateRepairTaskBO.getMaintainAmount())) {
            repairTask.setMaintainAmount(new BigDecimal(updateRepairTaskBO.getMaintainAmount()));
        }
        // 复勘部位
        repairTask.setResurveyPart(updateRepairTaskBO.getResurveyPart());
        // 责任情况
        if (StringUtils.isNotBlank(updateRepairTaskBO.getDutySituation())) {
            repairTask.setDutySituation(new BigDecimal(updateRepairTaskBO.getDutySituation()));
        }
        // 向用户追偿费用
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRecoveryAmount())) {
            repairTask.setRecoveryAmount(new BigDecimal(updateRepairTaskBO.getRecoveryAmount()));
        }
        // 保险上付费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getInsuranceAmount())) {
            repairTask.setInsuranceAmount(new BigDecimal(updateRepairTaskBO.getInsuranceAmount()));
        }
        // 加速折旧费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getAccDepAmount())) {
            repairTask.setAccDepAmount(new BigDecimal(updateRepairTaskBO.getAccDepAmount()));
        }
        // 停运损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getOutageLossAmount())) {
            repairTask.setOutageLossAmount(new BigDecimal(updateRepairTaskBO.getOutageLossAmount()));
        }
        // 车辆损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleLossAmount())) {
            repairTask.setVehicleLossAmount(new BigDecimal(updateRepairTaskBO.getVehicleLossAmount()));
        }
        // 拖车救援费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getTrailerRescueAmount())) {
            repairTask.setTrailerRescueAmount(new BigDecimal(updateRepairTaskBO.getTrailerRescueAmount()));
        }
        if ("1".equals(repairTask.getAdvancedAuditLeve())) {
            // 一级核损人
            repairTask.setVerificationLossCheckId(comModel.getCreateOperId());
        }
        if (updateRepairTaskBO.getConfirmType() != null) {
            repairTask.setConfirmType(updateRepairTaskBO.getConfirmType());
        }
        // 确认车损状态
        if (updateRepairTaskBO.getConfirmCarDamageType() != null) {
            repairTask.setConfirmCarDamageType(updateRepairTaskBO.getConfirmCarDamageType());
        }
        // 预估理赔金额
        if (updateRepairTaskBO.getEstimatedClaimAmount() != null) {
            repairTask.setEstimatedClaimAmount(updateRepairTaskBO.getEstimatedClaimAmount());
            if (repairTask.getEstimatedClaimAmount().compareTo(repairTaskView.getEstimatedClaimAmount()) != 0) {
                // 修改预估理赔金额日志
                String updateLog = "修改预估理赔金额：" + repairTask.getEstimatedClaimAmount();
                LogUtil.saveMtcOperatorLog(mtcOperatorLogMapper, repairTask.getId(), updateLog, CurrentTacheEnum.getEnumDesc(repairTask.getCurrentTache()), comModel, repairTask.getUpdateTime(), repairTask.getCurrentTache(), Contants.ONE);
            }
        }
        // 核损通过时间
        repairTask.setVerificationLossCheckTime(repairTask.getUpdateTime());
        // 更新车辆残值
        repairTask.setVehicleScrapeValue(this.queryCurrentVehicleScrapeValue(repairTask.getVin()));
        // 预计修理天数
        if (StringUtils.isNotBlank(updateRepairTaskBO.getExpectedRepairDays())) {
            repairTask.setExpectedRepairDays(Long.valueOf(updateRepairTaskBO.getExpectedRepairDays()));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(repairTask.getVehicleReciveTime());
            calendar.add(Calendar.DAY_OF_MONTH, Integer.parseInt(updateRepairTaskBO.getExpectedRepairDays()));
            // 车辆接受时间
            repairTask.setExpectedRepairComplete(Timestamp.valueOf(sdf.format(calendar.getTime())));

        }
        repairTask.setVerificationLossTaskOperId(null);

        // 获取下一审核级别配置
        MtcApprovalConfigDTO mtcApprovalConfigDTO = mtcApprovalConfigMapper.getNextLevelConfig(Integer.parseInt(repairTask.getAdvancedAuditLeve()));
        // 存在下一级配置且核损金额大于等于配置金额 需进入下一级审核
        boolean intoNextStageAudit = mtcApprovalConfigDTO != null
                && Double.parseDouble(updateRepairTaskBO.getVehicleInsuranceTotalAmount()) >= mtcApprovalConfigDTO.getAmount().doubleValue();
        updateRepairTaskBO.setIntoNextStageAudit(intoNextStageAudit);
        if (intoNextStageAudit) {
            repairTask.setVerificationLossTaskSchedule(300L);
            repairTask.setAdvancedAuditLeve(mtcApprovalConfigDTO.getLevel() + StringUtils.EMPTY);
        } else {
            repairTask.setVerificationLossTaskSchedule(320L);
            // 提前提车任务
            if (StringUtils.equals(Contants.VEHICLE_CHECK_ALERADY + StringUtils.EMPTY, repairTask.getVehicleCheckTaskSchedule().toString())) {
                repairTask.setVehicleCheckTaskSchedule((long) Contants.VEHICLE_CHECK_COMPLETED);
                repairTask.setMaterialCollectionTaskSchedule(700);
                repairTask.setCurrentTache((long) Contants.CURRENT_TACHE_MATERIAL_COLLECTION);
                LogUtil.saveMtcOperatorLog(mtcOperatorLogMapper, repairTask.getId(), "待处理", "材料收集", comModel, repairTask.getUpdateTime(), 70L);
            } else {
                repairTask.setCurrentTache((long) Contants.CURRENT_TACHE_VEHICLE_REPAIR);
                repairTask.setVehicleRepairTaskSchedule(500L);
            }
        }

        // 是否客户直付金额
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustPaysDirect())) {
            repairTask.setCustPaysDirect(updateRepairTaskBO.getCustPaysDirect());
        }
        // 客户直付金额 -> 影响用户承担金额
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
            if (repairTaskView.getCustAmount().compareTo(updateRepairTaskBO.getCustAmount()) != 0 || !repairTaskView.getCustPaysDirect().equals(updateRepairTaskBO.getCustPaysDirect())) {
                // 单独修改客户直付金额逻辑（用户承担=客户直付金额）
                log.info("任务【{}】单独修改客户直付金额", repairTask.getTaskNo());
                repairTask.setCustAmount(updateRepairTaskBO.getCustAmount());
                repairTask.setUserAssumedAmount(updateRepairTaskBO.getCustAmount());
                repairTask.setNotUserAssumedAmount(BigDecimal.ZERO);
            }
        }

        // 重新计算自费金额
        RepairAmountDTO repairAmountDTO = new RepairAmountDTO();
        BeanCopyUtils.copyProperties(repairTask, repairAmountDTO);
        BigDecimal selfFundedAmount = repairTaskService.getSelfFundedAmount(repairAmountDTO, manualUtils.getAccidentDamage(repairTask.getAccidentNo(), comModel.getToken()));
        repairTask.setSelfFundedAmount(selfFundedAmount);

        return repairTask;
    }

    /**
     * 核损核价平级移交
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updateTransferTask(Long id, String updateEndTime, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 取得详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(id);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskNew.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        if (taskSchedule.getVerificationLossTaskSchedule() == Contants.VERIFICATION_LOSS_CLOSED) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务已关闭，请重新刷新一览页面");
            return vo;
        }

        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        if (!operId.equals(repairTaskView.getVerificationLossTaskOperId())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("你没有占据此任务，不可移交");
            return vo;
        } else {
            // 移交任务
            RepairTask repairTask = new RepairTask();
            repairTask.setUpdateOperId(operId);
            repairTask.setUpdateOperName(operName);
            repairTask.setUpdateTime(time);
            repairTask.setId(id);
            repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.VERIFICATION_LOSS_UNTREATED));
            repairTask.setUpdateEndTime(updateEndTime);
            // 清空任务占有人ID
            repairTask.setVerificationLossTaskOperId(null);
            // 更新状况卡控用当前环节(核损核价)
            repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

            // 保存
            int count = repairTaskMapper.updateTransferTask(repairTask);

            if (count > 0) {

                MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
                mtcOperatorLog.setUpdateOperId(operId);
                mtcOperatorLog.setUpdateOperName(operName);
                mtcOperatorLog.setUpdateTime(time);
                mtcOperatorLog.setCreateOperId(operId);
                mtcOperatorLog.setCreateOperName(operName);
                mtcOperatorLog.setCreateTime(time);
                mtcOperatorLog.setRemark("核损核价");
                mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
                mtcOperatorLog.setStatus(Contants.ONE);
                mtcOperatorLog.setOpeContent("平级移交，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
                mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
                // 添加log
                mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
                // 记录任务状态持续时间
                vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));
                return vo;
            } else {
                vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
                vo.setCode(Contants.RETURN_ERROR_CODE);
                return vo;
            }

        }
    }

    /**
     * 核损核价放弃
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updategiveUpTask(Long id, String updateEndTime, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskView.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        if (taskSchedule.getVerificationLossTaskSchedule() == Contants.VERIFICATION_LOSS_CLOSED) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务已关闭，请重新刷新一览页面");
            return vo;
        }

        // 取得换件详细
        List<ViewReplaceItemDetailBO> replaceItemDetailList = repairTaskMapper
                .getReplaceItemDetailList(repairTaskView.getTaskNo());

        // 取得修理详情
        List<ViewRepairItemDetailBO> repairItemDetailList = repairTaskMapper
                .getViewReplaceItemDetailList(repairTaskView.getTaskNo());

        repairTaskView.setReplaceItemDetail(replaceItemDetailList);
        repairTaskView.setRepairItemDetail(repairItemDetailList);

        if (!operId.equals(repairTaskView.getVerificationLossTaskOperId())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("你没有占据此任务，不可放弃");
            return vo;
        } else {
            // 放弃任务
            RepairTask repairTask = new RepairTask();

            if (StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "1")) {

                BigDecimal initialzero = new BigDecimal(0);
                repairTask.setResurveyFlag(initialzero);
                repairTask.setRecoveryFlag(initialzero);
                repairTask.setRecoveryAmount(initialzero);
                repairTask.setInsuranceAmount(initialzero);
                repairTask.setAccDepAmount(initialzero);
                repairTask.setOutageLossAmount(initialzero);
                repairTask.setVehicleLossAmount(initialzero);
                repairTask.setTrailerRescueAmount(initialzero);
                repairTask.setDutySituation(initialzero);
                repairTask.setUpdateOperId(operId);
                repairTask.setUpdateOperName(operName);
                repairTask.setUpdateTime(time);
                repairTask.setId(id);
                repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.VERIFICATION_LOSS_UNTREATED));
                repairTask.setResurveyPart("");
                repairTask.setVehicleInsuranceTotalAmount(repairTask.getRepairInsuranceTotalAmount());
                repairTask.setVehicleRepairTotalAmount(repairTask.getRepairRepairTotalAmount());
                repairTask.setVehicleReplaceTotalAmount(repairTask.getRepairReplaceTotalAmount());
                repairTask.setUpdateEndTime(updateEndTime);
                // 更新状况卡控用当前环节(核损核价)
                repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

                // 保存
                int Count = repairTaskMapper.updateTransferTask(repairTask);

                if (Count > 0) {
                    // 删除换件项目核损意见
                    if (("2").equals(repairTaskView.getRepairTypeId())) {
                        if (CollectionUtils.isNotEmpty(repairTaskView.getReplaceItemDetail())) {

                            for (ViewReplaceItemDetailBO viewReplaceItemDetail : repairTaskView
                                    .getReplaceItemDetail()) {

                                ReplaceItemDetail replaceItemDetailModel = new ReplaceItemDetail();
                                if (StringUtils.isNotBlank(viewReplaceItemDetail.getInsuranceQuoteAmount())) {
                                    replaceItemDetailModel.setViewAmount(
                                            new BigDecimal(viewReplaceItemDetail.getInsuranceQuoteAmount()));
                                }
                                replaceItemDetailModel.setUpdateOperId(operId);
                                replaceItemDetailModel.setUpdateOperName(operName);
                                replaceItemDetailModel.setUpdateTime(time);
                                replaceItemDetailModel.setId(Long.valueOf(viewReplaceItemDetail.getId()));

                                // 更新
                                replaceItemDetailMapper.updateByIdSelective(replaceItemDetailModel);
                            }

                        }

                        if (CollectionUtils.isNotEmpty(repairTaskView.getRepairItemDetail())) {

                            for (ViewRepairItemDetailBO viewRepairItemDetail : repairTaskView.getRepairItemDetail()) {

                                RepairItemDetail repairItemDetail = new RepairItemDetail();

                                if (StringUtils.isNotBlank(viewRepairItemDetail.getRepairAmount())) {
                                    repairItemDetail
                                            .setViewAmount(new BigDecimal(viewRepairItemDetail.getRepairAmount()));
                                }
                                repairItemDetail.setUpdateOperId(operId);
                                repairItemDetail.setUpdateOperName(operName);
                                repairItemDetail.setUpdateTime(time);
                                repairItemDetail.setId(Long.valueOf(viewRepairItemDetail.getId()));

                                // 更新
                                repairItemDetailMapper.updateByIdSelective(repairItemDetail);
                            }

                        }
                    }

                    // 删除当前登陆人的备注
                    repairRemarkMapper.deleteByUser(operId, repairTaskView.getTaskNo());

                    MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
                    mtcOperatorLog.setUpdateOperId(operId);
                    mtcOperatorLog.setUpdateOperName(operName);
                    mtcOperatorLog.setUpdateTime(time);
                    mtcOperatorLog.setCreateOperId(operId);
                    mtcOperatorLog.setCreateOperName(operName);
                    mtcOperatorLog.setCreateTime(time);
                    mtcOperatorLog.setRemark("核损核价");
                    mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
                    mtcOperatorLog.setStatus(Contants.ONE);
                    mtcOperatorLog.setOpeContent("放弃，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
                    mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);

                    // 添加log
                    mtcOperatorLogMapper.saveSelective(mtcOperatorLog);

                    // 记录任务状态持续时间
                    vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));

                    return vo;

                }

            }
//            else if (StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "5")
//                    || StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "6")) {
//                repairTask.setId(id);
//                repairTask.setAdvancedAuditLeve("2");
//                repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.VERIFICATION_LOSS_UNTREATED));
//                // 清空占据人
//                repairTaskMapper.updateTransferTask(repairTask);
//                // 删除备注
//                String opeContent = "";
//
//                if (StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "5")) {
//                    opeContent = "资产管理部退回至运营经理";
//                } else if (StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "6")) {
//                    opeContent = "运营管理部退回至运营经理";
//                }
//                String recoderId = repairTaskView.getId();
//
//                // 取得当前状态的log 时间
//                String logUpdateTimeString = mtcOperatorLogMapper.getLogUpdateTime(opeContent, recoderId);
//
//                // 删除当前登陆人大于退回时间节点的备注
//                repairRemarkMapper.deleteByUpdateTime(logUpdateTimeString, repairTaskView.getTaskNo(), operId);
//
//                MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
//                mtcOperatorLog.setUpdateOperId(operId);
//                mtcOperatorLog.setUpdateOperName(operName);
//                mtcOperatorLog.setUpdateTime(time);
//                mtcOperatorLog.setCreateOperId(operId);
//                mtcOperatorLog.setCreateOperName(operName);
//                mtcOperatorLog.setCreateTime(time);
//                mtcOperatorLog.setRemark("核损核价");
//                mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
//                mtcOperatorLog.setStatus(Contants.ONE);
//                mtcOperatorLog.setOpeContent("放弃，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
//                mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
//
//                // 添加log
//                mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
//
//            }
//            else if (StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "2")
//                    || StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "3")
//                    || StringUtils.equals(repairTaskView.getAdvancedAuditLeve(), "4"))
            else {
                repairTask.setId(id);
                repairTask.setUpdateOperId(operId);
                repairTask.setUpdateOperName(operName);
                repairTask.setUpdateTime(time);
                repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.VERIFICATION_LOSS_UNTREATED));
                repairTask.setVehicleInsuranceTotalAmount(repairTask.getRepairInsuranceTotalAmount());
                repairTask.setVehicleRepairTotalAmount(repairTask.getRepairRepairTotalAmount());
                repairTask.setVehicleReplaceTotalAmount(repairTask.getRepairReplaceTotalAmount());
                repairTask.setUpdateEndTime(updateEndTime);

                repairTask.setVerificationLossTaskOperId(null);
                // 更新状况卡控用当前环节(核损核价)
                repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

                // 保存
                int Count = repairTaskMapper.updateTransferTask(repairTask);

                if (Count > 0) {
                    // 删除换件项目核损意见
                    if (("2").equals(repairTaskView.getRepairTypeId())) {
                        if (CollectionUtils.isNotEmpty(repairTaskView.getReplaceItemDetail())) {

                            for (ViewReplaceItemDetailBO viewReplaceItemDetail : repairTaskView
                                    .getReplaceItemDetail()) {

                                ReplaceItemDetail replaceItemDetailModel = new ReplaceItemDetail();

                                if (StringUtils.isNotBlank(viewReplaceItemDetail.getInsuranceQuoteAmount())) {
                                    replaceItemDetailModel.setViewAmount(
                                            new BigDecimal(viewReplaceItemDetail.getInsuranceQuoteAmount()));
                                }
                                replaceItemDetailModel.setUpdateOperId(operId);
                                replaceItemDetailModel.setUpdateOperName(operName);
                                replaceItemDetailModel.setUpdateTime(time);
                                replaceItemDetailModel.setId(Long.valueOf(viewReplaceItemDetail.getId()));

                                // 更新
                                replaceItemDetailMapper.updateByIdSelective(replaceItemDetailModel);
                            }

                        }

                        if (CollectionUtils.isNotEmpty(repairTaskView.getRepairItemDetail())) {

                            for (ViewRepairItemDetailBO viewRepairItemDetail : repairTaskView.getRepairItemDetail()) {

                                RepairItemDetail repairItemDetail = new RepairItemDetail();

                                if (StringUtils.isNotBlank(viewRepairItemDetail.getRepairAmount())) {
                                    repairItemDetail
                                            .setViewAmount(new BigDecimal(viewRepairItemDetail.getRepairAmount()));
                                }
                                repairItemDetail.setUpdateOperId(operId);
                                repairItemDetail.setUpdateOperName(operName);
                                repairItemDetail.setUpdateTime(time);
                                repairItemDetail.setId(Long.valueOf(viewRepairItemDetail.getId()));

                                // 更新
                                repairItemDetailMapper.updateByIdSelective(repairItemDetail);
                            }

                        }
                    }

                    // 删除当前登陆人的备注
                    repairRemarkMapper.deleteByUser(operId, repairTaskView.getTaskNo());

                    MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
                    mtcOperatorLog.setUpdateOperId(operId);
                    mtcOperatorLog.setUpdateOperName(operName);
                    mtcOperatorLog.setUpdateTime(time);
                    mtcOperatorLog.setCreateOperId(operId);
                    mtcOperatorLog.setCreateOperName(operName);
                    mtcOperatorLog.setCreateTime(time);
                    mtcOperatorLog.setRemark("核损核价");
                    mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
                    mtcOperatorLog.setStatus(Contants.ONE);
                    mtcOperatorLog.setOpeContent("放弃，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount() + "元");
                    mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);

                    // 添加log
                    mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
                    // 记录任务状态持续时间
                    vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));

                    return vo;

                }
            }
        }
        return vo;

    }

    /**
     * 核损核价提交上级
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updatetoHigherTask(Long id, UpdateRepairTaskBO updateRepairTaskBO,
                                                    HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        // 取得用户名称
        String userName = comModel.getUserName();

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskView.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        if (taskSchedule.getVerificationLossTaskSchedule() == Contants.VERIFICATION_LOSS_CLOSED) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("任务已关闭，请重新刷新一览页面");
            return vo;
        }

        // 检查是否存在定损数据
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskView.getTaskNo());
        mtcLossInfoDTO.setStatus(1);
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        if (CollectionUtils.isEmpty(mtcLossInfoList)) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "未经过定损系统维修报价，请退回后重新定损");
        } else {
            MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
            if (StringUtils.isBlank(mtcLossInfo.getAuditHandlerCode())) {
                return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "请先进行核损核价");
            }
        }
        // 核价项目未全部通过，不可审核通过
        List<MtcLossFitInfo> mtcLossFitInfoList = mtcLossFitInfoMapper.queryMtcLossFitList(repairTaskView.getTaskNo());
        List<MtcLossRepairInfo> mtcLossRepairInfoList = mtcLossRepairInfoMapper
                .queryMtcLossRepairList(repairTaskView.getTaskNo());
        List<MtcLossOuterRepairInfo> mtcLossOuterRepairInfoList = mtcLossOuterRepairInfoMapper
                .queryMtcLossOuterRepairList(repairTaskView.getTaskNo());
        List<MtcLossAssistInfo> mtcLossAssistInfoList = mtcLossAssistInfoMapper
                .queryMtcLossAssistList(repairTaskView.getTaskNo());
        if (CollectionUtils.isNotEmpty(mtcLossFitInfoList) || CollectionUtils.isNotEmpty(mtcLossRepairInfoList)
                || CollectionUtils.isNotEmpty(mtcLossOuterRepairInfoList)
                || CollectionUtils.isNotEmpty(mtcLossAssistInfoList)) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "核价项目未全部通过，不能提交上级");
        }

        // 不同意报价不可提交上级
        /*
         * if (StringUtils.equals("1",
         * updateRepairTaskBO.getVehicleManageViewFlag())) {
         * vo.setCode(Contants.RETURN_ERROR_CODE); vo.setMessage("价格异议，不可提交上级");
         * return vo; }
         */

        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId()) == null ? 0
                : mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId());

        if (LAPricelevel > (Contants.SECOND_LEVEL)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("您的核价权限超过二级，无法提交上级");
            return vo;
        }

        /*
         * if (StringUtils.isNotBlank(updateRepairTaskBO.
         * getVehicleInsuranceTotalAmount())) { // 取得维修金额 Double
         * repairInsuranceTotalAmount =
         * Double.valueOf(updateRepairTaskBO.getVehicleInsuranceTotalAmount());
         *
         * if (repairInsuranceTotalAmount < Contants.DAMAGETHRESHOLD) {
         * vo.setCode(Contants.RETURN_ERROR_CODE);
         * vo.setMessage("维修金额小于2000元，不可提交上级审核"); return vo; } } else {
         * vo.setCode(Contants.RETURN_ERROR_CODE);
         * vo.setMessage("维修金额小于2000元，不可提交上级审核"); return vo;
         *
         * }
         */

        RepairTask repairTask = new RepairTask();

        // 修理厂修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairRepairTotalAmount())) {
            repairTask.setRepairRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairRepairTotalAmount()));
        }

        // 修理厂定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRepairInsuranceTotalAmount())) {
            repairTask
                    .setRepairInsuranceTotalAmount(new BigDecimal(updateRepairTaskBO.getRepairInsuranceTotalAmount()));
        }

        // 车管换件金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleReplaceTotalAmount())) {
            repairTask.setVehicleReplaceTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleReplaceTotalAmount()));
        }

        // 车管修理金额合计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleRepairTotalAmount())) {
            repairTask.setVehicleRepairTotalAmount(new BigDecimal(updateRepairTaskBO.getVehicleRepairTotalAmount()));
        }

        // 车管定损总计
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleInsuranceTotalAmount())) {
            repairTask.setVehicleInsuranceTotalAmount(
                    new BigDecimal(updateRepairTaskBO.getVehicleInsuranceTotalAmount()));
        }

        // 是否需要复勘
        if (StringUtils.isNotBlank(updateRepairTaskBO.getResurveyFlag())) {
            repairTask.setResurveyFlag(new BigDecimal(updateRepairTaskBO.getResurveyFlag()));
        }

        // 复勘部位
        repairTask.setResurveyPart(updateRepairTaskBO.getResurveyPart());

        // 责任情况
        if (StringUtils.isNotBlank(updateRepairTaskBO.getDutySituation())) {
            repairTask.setDutySituation(new BigDecimal(updateRepairTaskBO.getDutySituation()));
        }

        // 向用户追偿费用
        if (StringUtils.isNotBlank(updateRepairTaskBO.getRecoveryAmount())) {
            repairTask.setRecoveryAmount(new BigDecimal(updateRepairTaskBO.getRecoveryAmount()));
        }

        // 保险上付费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getInsuranceAmount())) {
            repairTask.setInsuranceAmount(new BigDecimal(updateRepairTaskBO.getInsuranceAmount()));
        }

        // 加速折旧费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getAccDepAmount())) {
            repairTask.setAccDepAmount(new BigDecimal(updateRepairTaskBO.getAccDepAmount()));
        }

        // 停运损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getOutageLossAmount())) {
            repairTask.setOutageLossAmount(new BigDecimal(updateRepairTaskBO.getOutageLossAmount()));
        }

        // 车辆损失费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getVehicleLossAmount())) {
            repairTask.setVehicleLossAmount(new BigDecimal(updateRepairTaskBO.getVehicleLossAmount()));
        }

        // 拖车救援费
        if (StringUtils.isNotBlank(updateRepairTaskBO.getTrailerRescueAmount())) {
            repairTask.setTrailerRescueAmount(new BigDecimal(updateRepairTaskBO.getTrailerRescueAmount()));
        }
        // 一级核损人
        repairTask.setVerificationLossCheckId(operId);

        // 预计修理天数
        if (StringUtils.isNotBlank(updateRepairTaskBO.getExpectedRepairDays())) {
            repairTask.setExpectedRepairDays(Long.valueOf(updateRepairTaskBO.getExpectedRepairDays()));

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date;
            try {
                date = sdf.parse(repairTaskView.getVehicleReciveTime());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, Integer.valueOf(updateRepairTaskBO.getExpectedRepairDays()));

                // 车辆接受时间
                repairTask.setExpectedRepairComplete(Timestamp.valueOf(sdf.format(calendar.getTime())));
            } catch (ParseException e) {
                log.error("预计修理完成时间计算错误");
                e.printStackTrace();
            }

        }

        repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.VERIFICATION_LOSS_UNTREATED));
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);
        repairTask.setAdvancedAuditLeve(2 + StringUtils.EMPTY);
        repairTask.setExamineLevel(new BigDecimal(1));
        repairTask.setUpdateEndTime(updateRepairTaskBO.getUpdateEndTime());
        // 更新状况卡控用当前环节(核损核价)
        repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));
        // 维修任务垫付
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustPaysDirect())) {
            repairTask.setCustPaysDirect(updateRepairTaskBO.getCustPaysDirect());
        }
        if (ObjectUtil.isNotEmpty(updateRepairTaskBO.getCustAmount())) {
            repairTask.setCustAmount(updateRepairTaskBO.getCustAmount());
        }
        // 保存
        int count = repairTaskMapper.updateTransferTask(repairTask);

        if (count > 0) {

            List<VehicleRepairPic> vehicleRepairPicList = new ArrayList<VehicleRepairPic>();

            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getDamagedPartPicture())) {

                // 保存损坏部位图片
                for (String damagedPartPicture : updateRepairTaskBO.getDamagedPartPicture()) {

                    // 长度check
                    vo = MessageUtil.checkLength(Contants.DAMAGEDPARTPICTURE, damagedPartPicture, 200);

                    if (vo.getCode() != 0) {
                        return vo;
                    }

                    VehicleRepairPic vehicleRepairPic = new VehicleRepairPic();
                    vehicleRepairPic.setTaskNo(updateRepairTaskBO.getTaskNo());
                    vehicleRepairPic.setPicType(BigDecimal.valueOf(1));
                    vehicleRepairPic.setPicUrl(ComUtil.subStringUrl(damagedPartPicture));
                    vehicleRepairPic.setUpdateOperId(operId);
                    vehicleRepairPic.setUpdateOperName(operName);
                    vehicleRepairPic.setUpdateTime(time);
                    vehicleRepairPic.setCreateOperId(operId);
                    vehicleRepairPic.setCreateOperName(operName);
                    vehicleRepairPic.setCreateTime(time);

                    vehicleRepairPicList.add(vehicleRepairPic);

                }
            }

            if (CollectionUtils.isNotEmpty(vehicleRepairPicList)) {
                // 批量插入
                vehicleRepairPicMapper.batchInsert(vehicleRepairPicList);
            }

            // 保存修理项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getRepairItemDetail())) {
                for (ViewRepairItemDetailBO viewRepairItemDetail : updateRepairTaskBO.getRepairItemDetail()) {
                    RepairItemDetail repairItemDetail = new RepairItemDetail();
                    repairItemDetail.setId(Long.valueOf(viewRepairItemDetail.getId()));
                    repairItemDetail.setRemark(viewRepairItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewRepairItemDetail.getViewAmount())) {
                        repairItemDetail.setViewAmount(new BigDecimal(viewRepairItemDetail.getViewAmount()));
                    }
                    repairItemDetail.setUpdateOperId(operId);
                    repairItemDetail.setUpdateOperName(operName);
                    repairItemDetail.setUpdateTime(time);

                    // 更新
                    repairItemDetailMapper.updateByIdSelective(repairItemDetail);

                }

            }

            // 保存换件项目明细
            if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getReplaceItemDetail())) {
                for (ViewReplaceItemDetailBO viewReplaceItemDetail : updateRepairTaskBO.getReplaceItemDetail()) {
                    ReplaceItemDetail replaceItemDetail = new ReplaceItemDetail();
                    replaceItemDetail.setId(Long.valueOf(viewReplaceItemDetail.getId()));
                    replaceItemDetail.setRemark(viewReplaceItemDetail.getRemark());
                    if (StringUtils.isNotBlank(viewReplaceItemDetail.getViewAmount())) {
                        replaceItemDetail.setViewAmount(new BigDecimal(viewReplaceItemDetail.getViewAmount()));
                    }
                    replaceItemDetail.setUpdateOperId(operId);
                    replaceItemDetail.setUpdateOperName(operName);
                    replaceItemDetail.setUpdateTime(time);

                    // 更新
                    replaceItemDetailMapper.updateByIdSelective(replaceItemDetail);
                }
            }

            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("提交上级，金额为：" + updateRepairTaskBO.getVehicleInsuranceTotalAmount() + "元");
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

            // 添加log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
            // 记录任务状态持续时间
            vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));

        } else {
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            return vo;
        }

        return vo;
    }

    /**
     * 查看备注
     *
     * @param taskNo 任务编号
     * @param comModel
     * @return
     */
    @Override
    public List<ViewRepairRemarkBO> viewRepareRemark(String taskNo, ComModel comModel) {
        Long createOperId = comModel.getCreateOperId();
        List<ViewRepairRemarkBO> result = repareRemarkMapper.getviewRepareRemark(taskNo, createOperId);
        if (CollectionUtils.isNotEmpty(result)) {
            jedisUtils.addSet(REMARK_REMARK_REDIS_KEY + taskNo, new String[]{comModel.getUserName()});
        } else {
            jedisUtils.del(REMARK_REMARK_REDIS_KEY + taskNo);
        }
        return result;
    }

    /**
     * 核损核价任务驳回
     *
     * @param id 任务id
     * @return
     */
    @Transactional
    @Override
    public DefaultServiceRespDTO updateRejectTask(Long id, String updateEndTime, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long operId = comModel.getCreateOperId();
        String operName = comModel.getCreateOperName();
        // 取得用户名称
        String userName = StringUtils.substringBeforeLast(comModel.getUserName(), Contants.AT);

        // 取得详情
        RepairTaskViewBO repairTaskView = repairTaskMapper.getRepairTaskView(id);

        // 取得任务状态详情
        TaskScheduleBO taskSchedule = repairTaskMapper.getTaskSchedule(repairTaskView.getTaskNo());

        if (null == taskSchedule) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 检查当前环节是否处于核损核价环节
        if (Contants.CURRENT_TACHE_VERIFICATION_LOSS != taskSchedule.getCurrentTache()) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("已经不处于核损核价环节，请重新刷新一览页面");
            return vo;
        }

        // 取得用户核损核价级别
        Integer LAPricelevel = mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId()) == null ? 0
                : mtcUserMapper.getUserLAPricelevel(userName, comModel.getOrgId());

        if (!LAPricelevel.equals(Contants.SECOND_LEVEL)) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("您没有核损核价二级权限，无法驳回");
            return vo;
        }

        RepairTask repairTask = new RepairTask();
        repairTask.setVerificationLossTaskSchedule(Long.valueOf(Contants.TASK_SCHEDULE_DEFAULT));
        repairTask.setInsuranceQuoteTaskSchedule(Long.valueOf(Contants.INSURANCE_QUOTE_REJECT));
        repairTask.setCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_INSURANCE_QUOTE));
        repairTask.setExamineLevel(new BigDecimal(0));
        repairTask.setUpdateOperId(operId);
        repairTask.setUpdateOperName(operName);
        repairTask.setUpdateTime(time);
        repairTask.setId(id);
        BigDecimal initialzero = new BigDecimal(0);
        repairTask.setResurveyFlag(initialzero);
        repairTask.setRecoveryFlag(initialzero);
        repairTask.setRecoveryAmount(initialzero);
        repairTask.setInsuranceAmount(initialzero);
        repairTask.setAccDepAmount(initialzero);
        repairTask.setOutageLossAmount(initialzero);
        repairTask.setVehicleLossAmount(initialzero);
        repairTask.setTrailerRescueAmount(initialzero);
        repairTask.setDutySituation(initialzero);
        repairTask.setResurveyPart("");
        repairTask.setUpdateEndTime(updateEndTime);
        // 更新状况卡控用当前环节(核损核价)
        repairTask.setControlStatusCurrentTache(Long.valueOf(Contants.CURRENT_TACHE_VERIFICATION_LOSS));

        // 保存
        int count = repairTaskMapper.updateTransferTask(repairTask);

        if (count > 0) {
            // 删除定损内容
            repairItemDetailMapper.deleteBytaskNo(repairTaskView.getTaskNo());
            replaceItemDetailMapper.deleteBytaskNo(repairTaskView.getTaskNo());

            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateOperId(operId);
            mtcOperatorLog.setUpdateOperName(operName);
            mtcOperatorLog.setUpdateTime(time);
            mtcOperatorLog.setCreateOperId(operId);
            mtcOperatorLog.setCreateOperName(operName);
            mtcOperatorLog.setCreateTime(time);
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecoderId(id + StringUtils.EMPTY);
            mtcOperatorLog.setStatus(Contants.ONE);
            mtcOperatorLog.setOpeContent("驳回，金额为：" + repairTaskView.getVehicleInsuranceTotalAmount());
            mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);

            // 添加log
            mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
            // 记录任务状态持续时间
            vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(time, repairTaskView.getTaskNo()));
            return vo;
        } else {
            vo.setMessage(Contants.PAGE_IS_NOT_UPDATED_MESSAGE);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            return vo;
        }

    }

    /**
     * 查看查看任务处理记录
     *
     * @param taskNo 任务编号
     * @return
     */
    @Override
    public ObjectDataBo mtcTaskProcessingRecordList(String taskNo) {
        ObjectDataBo vo = new ObjectDataBo();

        String info = "";
        // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
        int idsUseMode = 1;
        try {
            idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
        } catch (Exception e) {
            idsUseMode = 1;
        }

        if (idsUseMode == 1) {
            try {
                info = HttpRequest.sendGet(apolloPropertyUtils.getString("evcard.ids.dns")
                        + "repair-factory-new/mtcTaskProcessingRecordList/" + taskNo, null);
            } catch (Exception ex) {
                log.info("调度系统异常:" + ex.toString());
                throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
            }

            if (StringUtils.isBlank(info)) {
                throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
            } else {
                vo = JSON.parseObject(info, ObjectDataBo.class);
            }
        }

        return vo;
    }

    /**
     * 查看任务处理详情
     *
     * @param taskNo 任务编号
     * @return
     */
    @Override
    public ObjectDataBo mtcTaskProcessingRecordDetail(String taskNo, String recordId) {
        ObjectDataBo vo = new ObjectDataBo();

        if (!taskNo.startsWith("BYZX")) {
            String info = "";

            // 调用调度接口开关(0:关闭调用调度接口开关 1:开启调用调度接口开关)
            int idsUseMode = 1;
            try {
                idsUseMode = Integer.parseInt(apolloPropertyUtils.getString("ids.use.mode"));
            } catch (Exception e) {
                idsUseMode = 1;
            }

            if (idsUseMode == 1) {
                try {
                    info = HttpRequest.sendGet(
                            apolloPropertyUtils.getString("evcard.ids.dns")
                                    + "repair-factory-new/mtcTaskProcessingRecordDetail/" + taskNo + "/" + recordId,
                            null);
                } catch (Exception ex) {
                    log.info("调度系统异常:" + ex.toString());
                    throw new MtcBusinessRuntimeException("调度系统异常", "00000022");
                }

                if (StringUtils.isBlank(info)) {
                    throw new MtcBusinessRuntimeException("通知调度系统异常", "00000022");
                } else {
                    vo = JSON.parseObject(info, ObjectDataBo.class);
                }
            }
        }
        return vo;
    }

    /**
     * 车管核价金额计算
     *
     * @param
     * @return
     */
    @Override
    public Map<String, String> calculationNuclearPrice(RepairTaskViewBO repairTaskViewBO) {

        Map<String, String> map = new HashMap<String, String>();

        Double vehicleReplaceTotalAmount = (double) 0;

        Double vehicleRepairTotalAmount = (double) 0;

        BigDecimal vehicleInsuranceTotalAmount = new BigDecimal(0);

        if (CollectionUtils.isEmpty(repairTaskViewBO.getRepairItemDetail())
                && CollectionUtils.isEmpty(repairTaskViewBO.getReplaceItemDetail())) {
            map.put("vehicleReplaceTotalAmount", null);
            map.put("vehicleRepairTotalAmount", null);
            map.put("vehicleInsuranceTotalAmount", null);
        }

        if (CollectionUtils.isNotEmpty(repairTaskViewBO.getRepairItemDetail())) {

            for (ViewRepairItemDetailBO viewRepairItemDetail : repairTaskViewBO.getRepairItemDetail()) {

                if (StringUtils.isNotBlank(viewRepairItemDetail.getViewAmount())) {
                    vehicleReplaceTotalAmount += new Double(viewRepairItemDetail.getViewAmount());
                }

            }

        }

        if (CollectionUtils.isNotEmpty(repairTaskViewBO.getReplaceItemDetail())) {

            for (ViewReplaceItemDetailBO viewReplaceItemDetail : repairTaskViewBO.getReplaceItemDetail()) {

                if (StringUtils.isNotBlank(viewReplaceItemDetail.getViewAmount())) {
                    vehicleRepairTotalAmount += new Double(viewReplaceItemDetail.getViewAmount());
                }

            }

        }

        vehicleInsuranceTotalAmount = BigDecimal.valueOf(vehicleReplaceTotalAmount)
                .add(BigDecimal.valueOf(vehicleRepairTotalAmount)).setScale(2, RoundingMode.HALF_UP);

        map.put("vehicleReplaceTotalAmount",
                BigDecimal.valueOf(vehicleReplaceTotalAmount).setScale(2, RoundingMode.HALF_UP).toString());
        map.put("vehicleRepairTotalAmount",
                BigDecimal.valueOf(vehicleRepairTotalAmount).setScale(2, RoundingMode.HALF_UP).toString());
        map.put("vehicleInsuranceTotalAmount", vehicleInsuranceTotalAmount.toString());

        return map;
    }

    /**
     * 核损核价维修任务保存CHECK
     *
     * @param updateRepairTaskBO
     * @return
     */
    DefaultServiceRespDTO updateRepairTaskCheck(UpdateRepairTaskBO updateRepairTaskBO) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 事故报案号长度
        vo = MessageUtil.checkLength(Contants.ACCIDENT_REPORT_NUMBER, updateRepairTaskBO.getAccidentReportNumber(), 50);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 预计修理天数长度
        vo = MessageUtil.checkInteger(Contants.EXPECTED_REPAIR_DAYS, updateRepairTaskBO.getExpectedRepairDays());

        if (vo.getCode() != 0) {
            return vo;
        }

        // 预计修理天数长度
        vo = MessageUtil.checkLength(Contants.EXPECTED_REPAIR_DAYS, updateRepairTaskBO.getExpectedRepairDays(), 20);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 修理厂换件金额合计长度
        vo = MessageUtil.checkNumericLen(Contants.REPAIR_REPLACE_TOTAL_AMOUNT,
                updateRepairTaskBO.getRepairInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 修理厂修理金额合计
        vo = MessageUtil.checkNumericLen(Contants.REPAIR_REPAIR_TOTAL_AMOUNT,
                updateRepairTaskBO.getRepairRepairTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 修理厂定损总计
        vo = MessageUtil.checkNumericLen(Contants.REPAIR_INSURANCE_TOTAL_AMOUNT,
                updateRepairTaskBO.getRepairInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管换件金额合计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_REPLACE_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleReplaceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管修理金额合计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_REPAIR_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleRepairTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管定损总计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_INSURANCE_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管定损总计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_INSURANCE_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 保养费用
        vo = MessageUtil.checkNumericLen(Contants.MAINTAIN_AMOUNT, updateRepairTaskBO.getMaintainAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 复勘部位
        vo = MessageUtil.checkLength(Contants.RESURVEY_PART, updateRepairTaskBO.getResurveyPart(), 50);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 向用户追偿费用
        vo = MessageUtil.checkNumericLen(Contants.RECOVERY_AMOUNT, updateRepairTaskBO.getRecoveryAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 保险上付费
        vo = MessageUtil.checkNumericLen(Contants.INSURANCE_AMOUNT, updateRepairTaskBO.getInsuranceAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 加速折旧费
        vo = MessageUtil.checkNumericLen(Contants.ACC_DEP_AMOUNT, updateRepairTaskBO.getAccDepAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 停运损失费
        vo = MessageUtil.checkNumericLen(Contants.OUTAGE_LOSS_AMOUNT, updateRepairTaskBO.getOutageLossAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车辆损失费
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_LOSS_AMOUNT, updateRepairTaskBO.getVehicleLossAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 拖车救援费
        vo = MessageUtil.checkNumericLen(Contants.TRAILER_RESCUE_AMOUNT, updateRepairTaskBO.getVehicleLossAmount(), 7,
                2);

        if (vo.getCode() != 0) {
            return vo;
        }

        if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getReplaceItemDetail())) {

            for (ViewReplaceItemDetailBO viewReplaceItemDetail : updateRepairTaskBO.getReplaceItemDetail()) {

                vo = MessageUtil.checkNumericLen("核价金额", viewReplaceItemDetail.getViewAmount(), 7, 2);

                if (vo.getCode() != 0) {
                    return vo;

                }

                vo = MessageUtil.checkLength("意见", viewReplaceItemDetail.getRemark(), 255);

                if (vo.getCode() != 0) {
                    return vo;
                }

            }
        }

        if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getRepairItemDetail())) {

            for (ViewRepairItemDetailBO viewRepairItemDetail : updateRepairTaskBO.getRepairItemDetail()) {

                vo = MessageUtil.checkNumericLen("核价金额", viewRepairItemDetail.getViewAmount(), 7, 2);

                if (vo.getCode() != 0) {
                    return vo;

                }

                vo = MessageUtil.checkLength("意见", viewRepairItemDetail.getRemark(), 255);

                if (vo.getCode() != 0) {
                    return vo;
                }

            }
        }

        return vo;

    }

    /**
     * 核损核价维修任务保存CHECK
     *
     * @param updateRepairTaskBO
     * @return
     */
    DefaultServiceRespDTO updateReviewPassTascheck(UpdateRepairTaskBO updateRepairTaskBO) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        // 修理厂换件金额合计长度
        vo = MessageUtil.checkNumericLen(Contants.REPAIR_REPLACE_TOTAL_AMOUNT,
                updateRepairTaskBO.getRepairInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 修理厂修理金额合计
        vo = MessageUtil.checkNumericLen(Contants.REPAIR_REPAIR_TOTAL_AMOUNT,
                updateRepairTaskBO.getRepairRepairTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 修理厂定损总计
        vo = MessageUtil.checkNumericLen(Contants.REPAIR_INSURANCE_TOTAL_AMOUNT,
                updateRepairTaskBO.getRepairInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管换件金额合计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_REPLACE_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleReplaceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管修理金额合计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_REPAIR_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleRepairTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管定损总计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_INSURANCE_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车管定损总计
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_INSURANCE_TOTAL_AMOUNT,
                updateRepairTaskBO.getVehicleInsuranceTotalAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 复勘部位
        vo = MessageUtil.checkLength(Contants.RESURVEY_PART, updateRepairTaskBO.getResurveyPart(), 50);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 向用户追偿费用
        vo = MessageUtil.checkNumericLen(Contants.RECOVERY_AMOUNT, updateRepairTaskBO.getRecoveryAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 保险上付费
        vo = MessageUtil.checkNumericLen(Contants.INSURANCE_AMOUNT, updateRepairTaskBO.getInsuranceAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 加速折旧费
        vo = MessageUtil.checkNumericLen(Contants.ACC_DEP_AMOUNT, updateRepairTaskBO.getAccDepAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 停运损失费
        vo = MessageUtil.checkNumericLen(Contants.OUTAGE_LOSS_AMOUNT, updateRepairTaskBO.getOutageLossAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 车辆损失费
        vo = MessageUtil.checkNumericLen(Contants.VEHICLE_LOSS_AMOUNT, updateRepairTaskBO.getVehicleLossAmount(), 7, 2);

        if (vo.getCode() != 0) {
            return vo;
        }

        // 拖车救援费
        vo = MessageUtil.checkNumericLen(Contants.TRAILER_RESCUE_AMOUNT, updateRepairTaskBO.getVehicleLossAmount(), 7,
                2);

        if (vo.getCode() != 0) {
            return vo;
        }

        if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getReplaceItemDetail())) {

            for (ViewReplaceItemDetailBO viewReplaceItemDetail : updateRepairTaskBO.getReplaceItemDetail()) {

                vo = MessageUtil.checkNumericLen("核价金额", viewReplaceItemDetail.getViewAmount(), 7, 2);

                if (vo.getCode() != 0) {
                    return vo;

                }

                vo = MessageUtil.checkLength("意见", viewReplaceItemDetail.getRemark(), 255);

                if (vo.getCode() != 0) {
                    return vo;
                }

            }
        }

        if (CollectionUtils.isNotEmpty(updateRepairTaskBO.getRepairItemDetail())) {

            for (ViewRepairItemDetailBO viewRepairItemDetail : updateRepairTaskBO.getRepairItemDetail()) {

                vo = MessageUtil.checkNumericLen("核价金额", viewRepairItemDetail.getViewAmount(), 7, 2);

                if (vo.getCode() != 0) {
                    return vo;

                }

                vo = MessageUtil.checkLength("意见", viewRepairItemDetail.getRemark(), 255);

                if (vo.getCode() != 0) {
                    return vo;
                }

            }
        }

        return vo;

    }

    /**
     * 维修审批单下载
     *
     * @param id
     * @param response
     * @return
     */
    @Override
    public DefaultServiceRespDTO loadRepairCheckSheet(Long id, HttpServletResponse response) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo.setMessage(Contants.SUCCESS_INFO);

        OutputStream out = null;
        Document document = null;
        try {
            document = new Document(PageSize.A4);
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String dateStr = dateformat.format(System.currentTimeMillis());
            String pdfName = "维修审批单" + dateStr + Contants.PDF;
            // File file = new File("d:\\" + execelName);
            response.setHeader("Content-Type", "application/pdf");
            response.setHeader("content-disposition", "attachment;filename=" + pdfName);
            out = response.getOutputStream();

            PdfWriter.getInstance(document, out);

            // 设置字体
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font fontChinese18 = new com.itextpdf.text.Font(bfChinese, 16, com.itextpdf.text.Font.BOLD);
            Font fontChinese16 = new com.itextpdf.text.Font(bfChinese, 12, com.itextpdf.text.Font.BOLD);
            Font fontChinese12 = new com.itextpdf.text.Font(bfChinese, 9, com.itextpdf.text.Font.NORMAL);
            Font fontChinese11Bold = new com.itextpdf.text.Font(bfChinese, 10, com.itextpdf.text.Font.BOLD);
            Font fontChinese11 = new com.itextpdf.text.Font(bfChinese, 10, com.itextpdf.text.Font.NORMAL);
            Font fontChinese11Normal = new com.itextpdf.text.Font(bfChinese, 14, com.itextpdf.text.Font.NORMAL);

            document.open();

            exportPdf(id, document, fontChinese18, fontChinese16, fontChinese12, fontChinese11Bold, fontChinese11,
                    fontChinese11Normal);

            document.close();
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.toString());
        } finally {

            if (document != null) {
                document.close();
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error(ComUtil.getExceptionMsg(e));
                }

            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO clearOwner(Long id, HttpServletRequest request) {
        // 获取详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(id);
        if (repairTaskNew == null) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务已经不处于核损核价环节，请刷新页面重试");
        }
        if (repairTaskNew.getCurrentTache() != Contants.CURRENT_TACHE_VERIFICATION_LOSS) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务已经不处于核损核价环节，请刷新页面重试");
        }
        if (Integer
                .parseInt(repairTaskNew.getVerificationLossTaskSchedule()) != Contants.VERIFICATION_LOSS_PROCESSING) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "只有处理中的任务才能清除任务占据人");
        }
        // 清除占据人
        ComModel comModel = ComUtil.getUserInfo(request);
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        repairTaskMapper.clearOwner(timestamp, comModel.getCreateOperId(), comModel.getCreateOperName(), id);
        // 添加日志
        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
        mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcOperatorLog.setRecoderId(id.toString());
        mtcOperatorLog.setOpeContent("清除任务占据人");
        mtcOperatorLog.setCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
        mtcOperatorLog.setStatus(Contants.ONE);
        mtcOperatorLog.setRemark("核损核价");
        mtcOperatorLog.setCreateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setCreateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setCreateTime(timestamp);
        mtcOperatorLog.setUpdateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setUpdateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setUpdateTime(timestamp);
        mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
        return DefaultServiceRespDTO.SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO closeTask(CloseTaskBO closeTaskBO, HttpServletRequest request) {
        // 获取详情
        RepairTaskViewBO repairTaskNew = repairTaskMapper.getRepairTaskView(closeTaskBO.getId());
        if (repairTaskNew == null) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务已经不处于核损核价环节，请刷新页面重试");
        }
        if (repairTaskNew.getCurrentTache() != Contants.CURRENT_TACHE_VERIFICATION_LOSS) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "任务已经不处于核损核价环节，请刷新页面重试");
        }
        if (Integer.parseInt(repairTaskNew.getVerificationLossTaskSchedule()) != Contants.VERIFICATION_LOSS_UNTREATED
                && Integer.parseInt(
                repairTaskNew.getVerificationLossTaskSchedule()) != Contants.VERIFICATION_LOSS_PROCESSING) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "只有未处理和处理中的任务才能关闭");
        }
        if (StringUtils.isBlank(closeTaskBO.getCloseReason())) {
            return new DefaultServiceRespDTO(Contants.RETURN_ERROR_CODE, "关闭原因不能为空");
        }
        // 关闭任务
        ComModel comModel = ComUtil.getUserInfo(request);
        Date now = new Date();
        SsoUserBaseInfoDto ssoUserBaseInfoDto = ssoUserService.getUserByUserName(comModel.getUserName());
        
        Timestamp timestamp = new Timestamp(now.getTime());
        RepairTask repairTask = new RepairTask();
        repairTask.setTaskNo(repairTaskNew.getTaskNo());
        repairTask.setVerificationLossTaskSchedule((long) Contants.VERIFICATION_LOSS_CLOSED);
        repairTask.setCloseReason(closeTaskBO.getCloseReason());
        repairTask.setAdvancedAuditLeve("1");
        repairTask.setUpdateTime(timestamp);
        repairTask.setUpdateOperId(comModel.getCreateOperId());
        repairTask.setUpdateOperName(comModel.getCreateOperName() + "-" + ssoUserBaseInfoDto.getdAccount());
        repairTaskMapper.updateByIdSelective(repairTask);
        // 添加日志
        MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
        mtcOperatorLog.setTableName(Contants.TABLENAME_MTC_REPAIR_TASK);
        mtcOperatorLog.setRecoderId(closeTaskBO.getId().toString());
        mtcOperatorLog.setOpeContent("关闭任务");
        mtcOperatorLog.setCurrentTache((long) Contants.CURRENT_TACHE_VERIFICATION_LOSS);
        mtcOperatorLog.setStatus(Contants.ONE);
        mtcOperatorLog.setRemark("核损核价");
        mtcOperatorLog.setCreateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setCreateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setCreateTime(timestamp);
        mtcOperatorLog.setUpdateOperId(comModel.getCreateOperId());
        mtcOperatorLog.setUpdateOperName(comModel.getCreateOperName());
        mtcOperatorLog.setUpdateTime(timestamp);
        mtcOperatorLogMapper.saveSelective(mtcOperatorLog);
        // 通知车管或调度系统
        DefaultServiceRespDTO defaultServiceRespDTO = DefaultServiceRespDTO.SUCCESS;
        if (!repairTaskNew.getTaskNo().startsWith("BYZX")) {
            if (repairTaskNew.getOrigin() == 0) {
                CloseTaskForMtcRequest closeTaskForMtcRequest = new CloseTaskForMtcRequest();
                closeTaskForMtcRequest.setTaskSeq(repairTaskNew.getTaskNo());
                closeTaskForMtcRequest.setCloseReason(closeTaskBO.getCloseReason());
                closeTaskForMtcRequest.setOptUser(comModel.getCreateOperName());
                ApiBaseResponse apiBaseResponse = idsDispatchTaskServiceProvider
                        .closeRepairTaskForMtc(closeTaskForMtcRequest);
                if (apiBaseResponse.getCode() != 0) {
                    throw new WarnException("-1", apiBaseResponse.getMessage());
                }
            } 
            if (repairTaskNew.getOrigin() == 1) {
                ComUtil.updateBdpTaskInfoStatus(bdpMtcTaskInfoService, repairTaskNew.getTaskNo(), 5, comModel,
                        defaultServiceRespDTO);
            }
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                ThreadPoolUtils.EXECUTOR.submit(() -> {
                    // 业财对接-关闭维修任务
                    bfcCostService.closeRepairBill(repairTaskNew.getTaskNo(), comModel.getToken());
                });
            }
        });

        //通知任务关闭
        vehicleMaintenanceLossAuditSubject.notifyTaskClose(new BaseRepairEvent(now, repairTaskNew.getTaskNo()));
        // 记录任务状态持续时间
        vehicleMaintenanceLossAuditSubject.recordPeriod(new BaseRepairEvent(now, repairTaskNew.getTaskNo()));

        //替代车任务处理
        //this.doReplaceVehicleTask(repairTaskNew.getTaskNo());
        replaceVehicleUtils.doReplaceVehicleTask(repairTaskNew.getTaskNo());

        // 关闭出厂登记
        vehicleLeavingFactoryService.closeLeavingFactory(repairTaskNew.getTaskNo());

        return defaultServiceRespDTO;
    }

    /**
     * 替代车任务处理逻辑
     * @param taskNo
     */
    private void doReplaceVehicleTask(String taskNo) {
        // 查询维修任务是否有替代车任务
        // 判断当前维修任务是否有关联的替代车任务
        DefaultWebRespVO defaultWebRespVO = rentReplaceVehicleService.selectReplaceTaskMtcsMtcRelationByMtcNo(taskNo);
        if (defaultWebRespVO!= null && "0".equals(defaultWebRespVO.getCode())){
            List<RentMtcTaskRelationDTO> taskRelationDTOS =  (List<RentMtcTaskRelationDTO>)defaultWebRespVO.getData();
            if (taskRelationDTOS != null && taskRelationDTOS.size() != 0){
                // 查询该维修任务关联的替代车任务是否完成
                String replaceVehicleTaskNo = taskRelationDTOS.get(0).getReplaceVehicleTaskNo();
                IrtRentReplaceVehicleTaskBo taskBoReturn = null;
                try {
                    taskBoReturn = iIrtRentReplaceVehicleTaskService.queryByTaskNo(replaceVehicleTaskNo);
                } catch (BusinessException e) {
                    e.printStackTrace();
                }
                if (taskBoReturn != null){

                    List<String> mtcTaskNos = taskRelationDTOS.stream()
                            .map(RentMtcTaskRelationDTO::getMtcTaskNo)
                            .collect(Collectors.toList());
                    // 未完成标志
                    int endTask = 0;
                    int cacelTask = 0;
                    // 任务状态(任务状态(0:已送修、1:维修中、2:待改派、3:待提车、4:已完成、5:已取消))
                    // 去除当前任务进行比较
                    List<String> endTaskList = new ArrayList<>();
                    List<String> mtcTaskNoList = taskRelationDTOS.stream()
                            .map(RentMtcTaskRelationDTO::getMtcTaskNo)
                            .filter(mtcTaskNo -> !taskNo.equals(mtcTaskNo))
                            .collect(Collectors.toList());
                    for (String mtcTaskNo : mtcTaskNoList) {
                        BdpMtcTaskInfoDTO bdpMtcTaskInfoDTO = bdpMtcTaskInfoService.selectTaskInfoByTaskNo(mtcTaskNo);
                        if (bdpMtcTaskInfoDTO != null){
                            if (bdpMtcTaskInfoDTO.getTaskStatus() == 4 || bdpMtcTaskInfoDTO.getTaskStatus() == 3){
                                endTask ++;
                                endTaskList.add(mtcTaskNo);
                            }else if (bdpMtcTaskInfoDTO.getTaskStatus() == 5){
                                cacelTask ++;
                            }
                        }
                    }
                    IrtRentReplaceVehicleTask replaceVehicleTask = new IrtRentReplaceVehicleTask();
                    replaceVehicleTask.setTaskNo(replaceVehicleTaskNo);
                    replaceVehicleTask.setExpectTakeTime(null);
                    Date expectTime = null;
                    // 如果替代车任务关联的维修任务部分取消部分完成
                    // 替代车约定取车时间== 最后一个完成的维修任务验收时间+缓冲时间（剔除非工作时间）
                    if (cacelTask + endTask ==  mtcTaskNoList.size() && cacelTask != mtcTaskNoList.size()){
                        // 设置约定取车时间
                        List<RepairTaskViewDTO> repairTaskViewBOS = new ArrayList<>();
                        for (String mtcTaskNo : endTaskList) {
                            RepairTaskViewDTO repairTaskViewDTO = mtcRepairTaskService.selectMtcDetailTaskInfo(mtcTaskNo);
                            repairTaskViewBOS.add(repairTaskViewDTO);
                        }
                        log.info("维修任务：{}", JSON.toJSONString(repairTaskViewBOS));
                        // 最大维修任务完成时间
                        String vehicleCheckTime = repairTaskViewBOS.stream()
                                .filter(taskView -> taskView.getVehicleCheckTime() != null)
                                .max(Comparator.comparing(RepairTaskViewDTO::getVehicleCheckTime))
                                .get().getVehicleCheckTime();

                        // 获取运营公司ID
                        String vin = taskBoReturn.getVin();
                        VehicleOperateDTO vehicleOperate = rentReplaceVehicleService.getVehicleOperateDTOByVin(vin);
                        String orgId = taskBoReturn.getOrgId();
                        if (vehicleOperate != null){
                            orgId = vehicleOperate.getOperationOrgId();
                        }
                        BdpManageWorkDTO subBufferByOrgId = rentReplaceVehicleService.getSubBufferByOrgId(orgId);
                        // 获取缓冲时间
                        String subBufferTime = "0";
                        if (subBufferByOrgId == null){
                            subBufferTime = "4";
                        }else {
                            subBufferTime = subBufferByOrgId.getBufferTime();
                        }
                        if(vehicleCheckTime != null){
                            //计算缓冲时间跳过非工作时间/节假日
                            expectTime = this.skipHolidayAndWeekend(subBufferByOrgId, vehicleCheckTime);
                            if (expectTime == null){
                                expectTime = DateUtil.AddHours(DateUtil.getDateFromStr(vehicleCheckTime, DateUtil.DATE_TYPE6), Integer.valueOf(subBufferTime));
                            }
                            replaceVehicleTask.setExpectTakeTime(expectTime);
                            iIrtRentReplaceVehicleTaskService.updateExpectTimeByTaskNo(replaceVehicleTask);

                            // 验收成功通知发送短信
                            this.sendMsg(taskBoReturn, DateUtil.getFormatDate(expectTime, DateUtil.DATE_TYPE6), subBufferByOrgId);
                        }
                    }else if (cacelTask != mtcTaskNoList.size()){
                        // 任务全部取消 约定与取车时间 == 维修任务取消时间
                        replaceVehicleTask.setExpectTakeTime(new Date());
                        iIrtRentReplaceVehicleTaskService.updateExpectTimeByTaskNo(replaceVehicleTask);
                    }
                }
            }
        }
    }

    /*private IrtRentReplaceVehicleTaskBo doReplaceSendGetCar(Long taskId) {
        // 查询关联维修任务 替代车发车任务
        // 判断替代车收车任务
        IrtRentReplaceVehicleTaskBo taskBo = null;
        BdpIrtSendGetCarRelation getCarRelation = new BdpIrtSendGetCarRelation();
        getCarRelation.setSendCarId(taskId);
        DefaultWebRespVO sendGetCarRelation = iIrtRentReplaceVehicleTaskService.getSendGetCarRelation(getCarRelation);
        if (sendGetCarRelation!= null && "0".equals(sendGetCarRelation.getCode())){
            BdpIrtSendGetCarRelation bdpIrtSendGetCarRelations = (BdpIrtSendGetCarRelation)sendGetCarRelation.getData();

            if (bdpIrtSendGetCarRelations != null){
                Long getCarId = bdpIrtSendGetCarRelations.getGetCarId();
                try {
                    taskBo  = iIrtRentReplaceVehicleTaskService.detailTask(getCarId);
                } catch (BusinessException e) {
                    e.printStackTrace();
                }
            }
        }
        return taskBo;
    }*/

    @Override
    public DefaultServiceRespDTO batchLoadRepairCheckSheet(List<Long> idList, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(idList)) {
            return new DefaultServiceRespDTO(-1, "请至少选择一条任务");
        }
        OutputStream out = null;
        Document document = null;
        try {
            document = new Document(PageSize.A4);
            String dateStr = ComUtil.getSystemDate(ComUtil.DATE_TYPE4);
            String pdfName = "维修审批单" + dateStr + Contants.PDF;
            response.setHeader("Content-Type", "application/pdf");
            response.setHeader("content-disposition", "attachment;filename=" + pdfName);
            out = response.getOutputStream();

            PdfWriter.getInstance(document, out);

            // 设置字体
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font fontChinese18 = new com.itextpdf.text.Font(bfChinese, 16, com.itextpdf.text.Font.BOLD);
            Font fontChinese16 = new com.itextpdf.text.Font(bfChinese, 12, com.itextpdf.text.Font.BOLD);
            Font fontChinese12 = new com.itextpdf.text.Font(bfChinese, 9, com.itextpdf.text.Font.NORMAL);
            Font fontChinese11Bold = new com.itextpdf.text.Font(bfChinese, 10, com.itextpdf.text.Font.BOLD);
            Font fontChinese11 = new com.itextpdf.text.Font(bfChinese, 10, com.itextpdf.text.Font.NORMAL);
            Font fontChinese11Normal = new com.itextpdf.text.Font(bfChinese, 14, com.itextpdf.text.Font.NORMAL);

            document.open();

            for (int i = 0; i < idList.size(); i++) {
                if (i > 0) {
                    document.newPage();
                }
                exportPdf(idList.get(i), document, fontChinese18, fontChinese16, fontChinese12, fontChinese11Bold,
                        fontChinese11, fontChinese11Normal);
            }

            document.close();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("batchLoadRepairCheckSheet error", e);
        } finally {
            if (document != null) {
                document.close();
            }
            IOUtils.closeQuietly(out);
        }
        return DefaultServiceRespDTO.SUCCESS;
    }

    @Override
    public List<MtcLossFitInfoBO> queryMtcLossFitItemList(String taskNo) {
        return mtcLossFitInfoMapper.queryMtcLossFitItemList(taskNo);
    }

    @Override
    public List<MtcLossRepairInfoBO> queryMtcLossRepairItemList(String taskNo) {
        return mtcLossRepairInfoMapper.queryMtcLossRepairItemList(taskNo);
    }

    @Override
    public BigDecimal queryCurrentVehicleScrapeValue(String vin) {
        BigDecimal result = BigDecimal.ZERO;
        try {
            String url = apolloPropertyUtils.getString("sap.ZIFICO158");
            Map<String, String> headerMap = new HashMap<>();
            String auth = apolloPropertyUtils.getString("sap.username") + ":" + apolloPropertyUtils.getString("sap.password");
            byte[] encodedAuth = Base64.encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);
            headerMap.put("Authorization", authHeader);
            log.info("sap车辆净值-header：{}", JSONObject.toJSONString(headerMap));

            Map<String, List<Map<String, String>>> parameter = new HashMap<>();
            parameter.put("T_ZIFICO158_IN", new ArrayList<Map<String, String>>() {{
                add(new HashMap<String, String>() {{
                    put("VHVIN", vin);
                }});
            }});
            log.info("车辆剩余资产sap请求参数：{}", JSONObject.toJSONString(parameter));

            JSONObject resJson = HttpClientUtils.sendRestHttp_post(url, "POST", JSONObject.toJSONString(parameter), headerMap);
            if (null != resJson) {
                log.info("车辆剩余资产sap返回结果：{}", resJson.toJSONString());
                String ZBLANCE = resJson.getJSONObject("T_ZIFICO158_OUT").getString("ZBALANCE");
                result = new BigDecimal(ZBLANCE.trim());
            }
        } catch (Exception e) {
            log.error("调用sap获取车辆残值异常", e);
        }
        return result;
    }

    @Override
    public List<MtcLossRepairInfoBO> queryMtcLossRepairItemList(String taskNo, Integer insurancePreReviewStatus) {
        List<MtcLossRepairInfoBO> result = queryMtcLossRepairItemList(taskNo);
        // 自有配件库
        List<MtcRepairItemCheckInfoDTO> repairItemCheckInfoList = mtcRepairItemCheckInfoService.queryCheckListByTaskNoAndInsurancePreReviewStatus(taskNo, insurancePreReviewStatus);
        if (CollectionUtils.isNotEmpty(repairItemCheckInfoList)) {
            for (MtcRepairItemCheckInfoDTO mtcRepairItemCheckInfoDTO : repairItemCheckInfoList) {
                MtcLossRepairInfoBO bo = new MtcLossRepairInfoBO();
                BeanCopyUtils.copyProperties(mtcRepairItemCheckInfoDTO, bo);
                bo.setRepairModeName(mtcRepairItemCheckInfoDTO.getItemName());
                result.add(bo);
            }
        }
        return result;
    }

    /**
     * 输出PDF内容
     *
     * @param id
     * @param document
     * @param fontChinese18
     * @param fontChinese16
     * @param fontChinese12
     * @param fontChinese11Bold
     * @param fontChinese11
     * @param fontChinese11Normal
     * @throws Exception
     */
    private void exportPdf(Long id, Document document, Font fontChinese18, Font fontChinese16, Font fontChinese12,
                           Font fontChinese11Bold, Font fontChinese11, Font fontChinese11Normal) throws Exception {
        // 获取维修任务详细信息
        RepairTaskViewBO repairTaskViewBO = repairTaskMapper.getRepairTaskView(id);
        // 获取当前时间
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String now = df.format(System.currentTimeMillis());
        // 标题
        PdfPTable table3 = new PdfPTable(3);
        int width3[] = {210, 280, 150};
        table3.setWidths(width3);
        PdfPCell cell31 = new PdfPCell(new Paragraph("", fontChinese11Normal));
        PdfPCell cell32 = new PdfPCell(new Paragraph("环球车享汽车租赁有限公司", fontChinese11Normal));
        PdfPCell cell33 = new PdfPCell(new Paragraph("", fontChinese11Normal));
        cell31.setBorder(0);
        cell32.setBorder(0);
        cell33.setBorder(0);
        table3.addCell(cell31);
        table3.addCell(cell32);
        table3.addCell(cell33);
        document.add(table3);

        Paragraph blank = new Paragraph(18f, repairTaskViewBO.getOrgName(), fontChinese11);
        blank.setAlignment(Element.ALIGN_RIGHT);
        blank.setIndentationRight(50);
        document.add(blank);
        // 加入空行
        Paragraph blankRow41 = new Paragraph(18f, " ", fontChinese11);
        document.add(blankRow41);

        PdfPTable tablea = new PdfPTable(3);
        int widtha[] = {300, 250, 150};
        tablea.setWidths(widtha);
        PdfPCell cell31a = new PdfPCell(new Paragraph("", fontChinese11Normal));
        PdfPCell cell32a = new PdfPCell(new Paragraph("维修审批单", fontChinese16));
        PdfPCell cell33a = new PdfPCell(new Paragraph("", fontChinese11Normal));
        cell31a.setBorder(0);
        cell32a.setBorder(0);
        cell33a.setBorder(0);
        tablea.addCell(cell31a);
        tablea.addCell(cell32a);
        tablea.addCell(cell33a);
        document.add(tablea);
        // 加入空行
        Paragraph blankRow31a = new Paragraph(18f, " ", fontChinese11);
        document.add(blankRow31a);

        // table5
        PdfPTable table5 = new PdfPTable(4);
        int widthb[] = {150, 800, 150, 300};
        table5.setWidths(widthb);
        PdfPCell cell51 = new PdfPCell(new Paragraph("任务编号:", fontChinese12));
        PdfPCell cell52 = new PdfPCell(new Paragraph(repairTaskViewBO.getTaskNo(), fontChinese12));
        PdfPCell cell53 = new PdfPCell(new Paragraph("打印日期:", fontChinese12));
        PdfPCell cell54 = new PdfPCell(new Paragraph(now, fontChinese12));
        cell51.setBorder(0);
        cell52.setBorder(0);
        cell53.setBorder(0);
        cell54.setBorder(0);
        table5.addCell(cell51);
        table5.addCell(cell52);
        table5.addCell(cell53);
        table5.addCell(cell54);
        document.add(table5);
        // 加入空行
        Paragraph blankRow51 = new Paragraph(18f, " ", fontChinese18);
        document.add(blankRow51);

        // table7
        PdfPTable table7 = new PdfPTable(4);

        int width7[] = {41, 25, 25, 25};

        table7.setWidths(width7);
        PdfPCell cell71 = new PdfPCell(new Paragraph("车牌号", fontChinese12));
        PdfPCell cell72 = new PdfPCell(new Paragraph(repairTaskViewBO.getVehicleNo(), fontChinese12));
        PdfPCell cell73 = new PdfPCell(new Paragraph("车架号", fontChinese12));
        PdfPCell cell74 = new PdfPCell(new Paragraph(repairTaskViewBO.getVin(), fontChinese12));

        // 表格高度
        cell71.setFixedHeight(20);
        cell72.setFixedHeight(20);
        cell73.setFixedHeight(20);
        cell74.setFixedHeight(20);

        // 水平居中
        cell71.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell72.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell73.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell74.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 垂直居中
        cell71.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell72.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell73.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell74.setVerticalAlignment(Element.ALIGN_MIDDLE);

        // 边框颜色
        cell71.setBorderColor(BaseColor.BLACK);
        cell72.setBorderColor(BaseColor.BLACK);
        cell73.setBorderColor(BaseColor.BLACK);
        cell74.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框

        cell72.disableBorderSide(4);

        cell73.disableBorderSide(4);

        cell74.disableBorderSide(4);

        table7.addCell(cell71);
        table7.addCell(cell72);
        table7.addCell(cell73);
        table7.addCell(cell74);

        document.add(table7);

        // table8
        PdfPTable table8 = new PdfPTable(4);

        int width8[] = {41, 25, 25, 25};
        table8.setWidths(width8);
        PdfPCell cell81 = new PdfPCell(new Paragraph("发动机号", fontChinese12));
        PdfPCell cell82 = new PdfPCell(new Paragraph("", fontChinese12));
        PdfPCell cell83 = new PdfPCell(new Paragraph("车辆型号", fontChinese12));
        PdfPCell cell84 = new PdfPCell(new Paragraph(repairTaskViewBO.getVehicleModelInfo(), fontChinese12));

        // 表格高度
        cell81.setFixedHeight(20);
        cell82.setFixedHeight(20);
        cell83.setFixedHeight(20);
        cell84.setFixedHeight(20);

        // 水平居中
        cell81.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell82.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell83.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell84.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 垂直居中
        cell81.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell82.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell83.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell84.setVerticalAlignment(Element.ALIGN_MIDDLE);

        // 边框颜色
        cell81.setBorderColor(BaseColor.BLACK);
        cell82.setBorderColor(BaseColor.BLACK);
        cell83.setBorderColor(BaseColor.BLACK);
        cell84.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框

        cell82.disableBorderSide(4);

        cell83.disableBorderSide(4);

        cell84.disableBorderSide(4);

        table8.addCell(cell81);
        table8.addCell(cell82);
        table8.addCell(cell83);
        table8.addCell(cell84);

        document.add(table8);

        // table9
        PdfPTable table9 = new PdfPTable(4);

        int width9[] = {41, 25, 25, 25};
        table9.setWidths(width9);
        PdfPCell cell91 = new PdfPCell(new Paragraph("任务创建日期", fontChinese12));
        PdfPCell cell92 = new PdfPCell(new Paragraph(repairTaskViewBO.getTaskInflowTime(), fontChinese12));
        PdfPCell cell93 = new PdfPCell(new Paragraph("审核完成日期", fontChinese12));
        PdfPCell cell94 = null;

        cell94 = new PdfPCell(new Paragraph(
                mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "审核通过").getUpdateTime(), fontChinese12));

        // 表格高度
        cell91.setFixedHeight(20);
        cell92.setFixedHeight(20);
        cell93.setFixedHeight(20);
        cell94.setFixedHeight(20);

        // 水平居中
        cell91.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell92.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell93.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell94.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 垂直居中
        cell91.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell92.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell93.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell94.setVerticalAlignment(Element.ALIGN_MIDDLE);

        // 边框颜色
        cell91.setBorderColor(BaseColor.BLACK);
        cell92.setBorderColor(BaseColor.BLACK);
        cell93.setBorderColor(BaseColor.BLACK);
        cell94.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框

        cell92.disableBorderSide(4);

        cell93.disableBorderSide(4);

        cell94.disableBorderSide(4);

        table9.addCell(cell91);
        table9.addCell(cell92);
        table9.addCell(cell93);
        table9.addCell(cell94);

        document.add(table9);

        // tableb
        PdfPTable tableb = new PdfPTable(4);

        int width11[] = {41, 25, 25, 25};
        tableb.setWidths(width11);
        PdfPCell cellb1 = new PdfPCell(new Paragraph("维修单位（修理厂）", fontChinese12));
        PdfPCell cellb2 = new PdfPCell(new Paragraph(repairTaskViewBO.getRepairDepotName(), fontChinese12));
        PdfPCell cellb3 = new PdfPCell(new Paragraph("修理厂类型", fontChinese12));
        PdfPCell cellb4 = new PdfPCell(
                new Paragraph(repairDepotInfoMapper.getRepairType(repairTaskViewBO.getRepairDepotId()), fontChinese12));

        // 表格高度
        cellb1.setMinimumHeight(20);
        cellb2.setMinimumHeight(20);
        cellb3.setMinimumHeight(20);
        cellb4.setMinimumHeight(20);
        // 水平居中
        cellb1.setHorizontalAlignment(Element.ALIGN_CENTER);
        cellb2.setHorizontalAlignment(Element.ALIGN_CENTER);
        cellb3.setHorizontalAlignment(Element.ALIGN_CENTER);
        cellb4.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 垂直居中
        cellb1.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cellb2.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cellb3.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cellb4.setVerticalAlignment(Element.ALIGN_MIDDLE);

        // 边框颜色
        cellb1.setBorderColor(BaseColor.BLACK);
        cellb2.setBorderColor(BaseColor.BLACK);
        cellb3.setBorderColor(BaseColor.BLACK);
        cellb4.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框

        cellb2.disableBorderSide(4);

        cellb3.disableBorderSide(4);

        cellb4.disableBorderSide(4);

        tableb.addCell(cellb1);
        tableb.addCell(cellb2);
        tableb.addCell(cellb3);
        tableb.addCell(cellb4);

        document.add(tableb);
        // taclec
        PdfPTable tablec = new PdfPTable(2);

        float widthc[] = {41, 75};
        tablec.setWidths(widthc);
        PdfPCell cellc1 = new PdfPCell(new Paragraph("维修费用总金额", fontChinese12));
        PdfPCell cellc2 = new PdfPCell(
                new Paragraph(repairTaskViewBO.getVehicleInsuranceTotalAmount() + "（元）", fontChinese12));

        // 表格高度
        cellc1.setFixedHeight(20);
        cellc2.setFixedHeight(20);

        // 水平居中
        cellc1.setHorizontalAlignment(Element.ALIGN_CENTER);
        cellc2.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 垂直居中
        cellc1.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cellc2.setVerticalAlignment(Element.ALIGN_MIDDLE);

        // 边框颜色
        cellc1.setBorderColor(BaseColor.BLACK);
        cellc2.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框

        cellc2.disableBorderSide(4);

        tablec.addCell(cellc1);
        tablec.addCell(cellc2);

        document.add(tablec);
        // 加入空行
        Paragraph blankRow31c = new Paragraph(18f, " ", fontChinese11);
        document.add(blankRow31c);

        // 查询是否通过定损系统
        MtcLossInfo mtcLossInfoDTO = new MtcLossInfo();
        mtcLossInfoDTO.setTaskNo(repairTaskViewBO.getTaskNo());
        mtcLossInfoDTO.setStatus(1);
        List<MtcLossInfo> mtcLossInfoList = mtcLossInfoMapper.queryMtcLossList(mtcLossInfoDTO);
        if (CollectionUtils.isEmpty(mtcLossInfoList)) {
            PdfPTable tabled = new PdfPTable(1);
            int widthd[] = {300};
            tabled.setWidths(widthd);
            PdfPCell cell31d = new PdfPCell(new Paragraph("换件项目清单", fontChinese11Bold));
            cell31d.setFixedHeight(20);
            cell31d.setBorder(0);

            tabled.addCell(cell31d);

            document.add(tabled);

            List<RepairReplaceBO> list = replaceItemDetailMapper.getReplaceItem(repairTaskViewBO.getTaskNo());
            // 换件项目
            PdfPTable tablee = new PdfPTable(7);

            int wiethe[] = {20, 30, 25, 25, 25, 25, 25};
            tablee.setWidths(wiethe);
            PdfPCell celle1 = new PdfPCell(new Paragraph("序号", fontChinese12));
            PdfPCell celle2 = new PdfPCell(new Paragraph("更换项目", fontChinese12));
            PdfPCell celle3 = new PdfPCell(new Paragraph("原厂编号", fontChinese12));
            PdfPCell celle4 = new PdfPCell(new Paragraph("价格", fontChinese12));
            PdfPCell celle5 = new PdfPCell(new Paragraph("数量", fontChinese12));
            PdfPCell celle6 = new PdfPCell(new Paragraph("残值", fontChinese12));
            PdfPCell celle7 = new PdfPCell(new Paragraph("价格方案", fontChinese12));

            // 表格高度
            celle1.setFixedHeight(20);
            celle2.setFixedHeight(20);
            celle3.setFixedHeight(20);
            celle4.setFixedHeight(20);
            celle5.setFixedHeight(20);
            celle6.setFixedHeight(20);
            celle7.setFixedHeight(20);

            // 水平居中
            celle1.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle2.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle3.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle4.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle5.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle6.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle7.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            celle1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle2.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle3.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle4.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle5.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle6.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle7.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            celle1.setBorderColor(BaseColor.BLACK);
            celle2.setBorderColor(BaseColor.BLACK);
            celle3.setBorderColor(BaseColor.BLACK);
            celle4.setBorderColor(BaseColor.BLACK);
            celle5.setBorderColor(BaseColor.BLACK);
            celle6.setBorderColor(BaseColor.BLACK);
            celle7.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            celle2.disableBorderSide(4);

            celle3.disableBorderSide(4);

            celle4.disableBorderSide(4);

            celle5.disableBorderSide(4);
            celle6.disableBorderSide(4);
            celle7.disableBorderSide(4);

            tablee.addCell(celle1);
            tablee.addCell(celle2);
            tablee.addCell(celle3);
            tablee.addCell(celle4);
            tablee.addCell(celle5);
            tablee.addCell(celle6);
            tablee.addCell(celle7);

            document.add(tablee);
            Integer numInteger = 1;
            for (RepairReplaceBO repairReplaceBO : list) {
                PdfPTable a = new PdfPTable(7);
                int width[] = {20, 30, 25, 25, 25, 25, 25};
                a.setWidths(width);
                PdfPCell a1 = new PdfPCell(new Paragraph(numInteger + StringUtils.EMPTY, fontChinese12));
                PdfPCell a2 = new PdfPCell(new Paragraph(repairReplaceBO.getPartName(), fontChinese12));
                PdfPCell a3 = new PdfPCell(new Paragraph("", fontChinese12));
                PdfPCell a4 = new PdfPCell(
                        new Paragraph(repairReplaceBO.getUnitPrice() + StringUtils.EMPTY + "（元）", fontChinese12));
                PdfPCell a5 = new PdfPCell(
                        new Paragraph(repairReplaceBO.getPartNumber() + StringUtils.EMPTY, fontChinese12));
                PdfPCell a6 = new PdfPCell(new Paragraph("", fontChinese12));
                PdfPCell a7 = new PdfPCell(new Paragraph(repairReplaceBO.getPriceProgrammeName(), fontChinese12));
                // 表格高度
                a1.setFixedHeight(20);
                a2.setFixedHeight(20);
                a3.setFixedHeight(20);
                a4.setFixedHeight(20);
                a5.setFixedHeight(20);
                a6.setFixedHeight(20);
                a7.setFixedHeight(20);

                // 水平居中
                a1.setHorizontalAlignment(Element.ALIGN_CENTER);
                a2.setHorizontalAlignment(Element.ALIGN_CENTER);
                a3.setHorizontalAlignment(Element.ALIGN_CENTER);
                a4.setHorizontalAlignment(Element.ALIGN_CENTER);
                a5.setHorizontalAlignment(Element.ALIGN_CENTER);
                a6.setHorizontalAlignment(Element.ALIGN_CENTER);
                a7.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                a1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a3.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a4.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a5.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a6.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a7.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                a1.setBorderColor(BaseColor.BLACK);
                a2.setBorderColor(BaseColor.BLACK);
                a3.setBorderColor(BaseColor.BLACK);
                a4.setBorderColor(BaseColor.BLACK);
                a5.setBorderColor(BaseColor.BLACK);
                a6.setBorderColor(BaseColor.BLACK);
                a7.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框

                a2.disableBorderSide(4);

                a3.disableBorderSide(4);

                a4.disableBorderSide(4);

                a5.disableBorderSide(4);
                a6.disableBorderSide(4);
                a7.disableBorderSide(4);

                a.addCell(a1);
                a.addCell(a2);
                a.addCell(a3);
                a.addCell(a4);
                a.addCell(a5);
                a.addCell(a6);
                a.addCell(a7);

                document.add(a);
                numInteger++;
            }

            // b
            PdfPTable b = new PdfPTable(2);

            int bwidth[] = {20, 155};
            b.setWidths(bwidth);
            PdfPCell b1 = new PdfPCell(new Paragraph("小计", fontChinese12));
            PdfPCell b2 = new PdfPCell(
                    new Paragraph(repairTaskViewBO.getRepairReplaceTotalAmount() + "（元）", fontChinese12));

            // 表格高度
            b1.setFixedHeight(20);
            b2.setFixedHeight(20);

            // 水平居中
            b1.setHorizontalAlignment(Element.ALIGN_CENTER);
            b2.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            b1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            b2.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            b1.setBorderColor(BaseColor.BLACK);
            b2.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            b2.disableBorderSide(4);

            b.addCell(b1);
            b.addCell(b2);

            document.add(b);
            document.add(blankRow31c);

            PdfPTable d = new PdfPTable(1);
            int dwidth[] = {300};
            d.setWidths(dwidth);
            PdfPCell d1 = new PdfPCell(new Paragraph("修理项目清单", fontChinese11Bold));
            d1.setFixedHeight(20);
            d1.setBorder(0);

            d.addCell(d1);

            document.add(d);

            List<RepairDetailBO> repairDetailBOs = repairItemDetailMapper.getRepairDetail(repairTaskViewBO.getTaskNo());
            // 修理项目清单
            PdfPTable c = new PdfPTable(4);

            int cwidth[] = {20, 50, 30, 50};
            c.setWidths(cwidth);
            PdfPCell c1 = new PdfPCell(new Paragraph("序号", fontChinese12));
            PdfPCell c2 = new PdfPCell(new Paragraph("修理名称", fontChinese12));
            PdfPCell c3 = new PdfPCell(new Paragraph("维修金额", fontChinese12));
            PdfPCell c4 = new PdfPCell(new Paragraph("价格方案", fontChinese12));

            // 表格高度
            c1.setFixedHeight(20);
            c2.setFixedHeight(20);
            c3.setFixedHeight(20);
            c4.setFixedHeight(20);

            // 水平居中
            c1.setHorizontalAlignment(Element.ALIGN_CENTER);
            c2.setHorizontalAlignment(Element.ALIGN_CENTER);
            c3.setHorizontalAlignment(Element.ALIGN_CENTER);
            c4.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            c1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c2.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c3.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c4.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            c1.setBorderColor(BaseColor.BLACK);
            c2.setBorderColor(BaseColor.BLACK);
            c3.setBorderColor(BaseColor.BLACK);
            c4.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            c2.disableBorderSide(4);

            c3.disableBorderSide(4);

            c4.disableBorderSide(4);

            c.addCell(c1);
            c.addCell(c2);
            c.addCell(c3);
            c.addCell(c4);
            Integer number = 1;
            document.add(c);
            for (RepairDetailBO repairDetailBO : repairDetailBOs) {
                // e
                PdfPTable e = new PdfPTable(4);

                int ewidth[] = {20, 50, 30, 50};
                e.setWidths(ewidth);
                PdfPCell e1 = new PdfPCell(new Paragraph(number + StringUtils.EMPTY, fontChinese12));
                PdfPCell e2 = new PdfPCell(new Paragraph(repairDetailBO.getRepairName(), fontChinese12));
                PdfPCell e3 = new PdfPCell(
                        new Paragraph(repairDetailBO.getRepairAmount() + StringUtils.EMPTY + "（元）", fontChinese12));
                PdfPCell e4 = new PdfPCell(new Paragraph(repairDetailBO.getPriceProgrammeName(), fontChinese12));

                // 表格高度
                e1.setFixedHeight(20);
                e2.setFixedHeight(20);
                e3.setFixedHeight(20);
                e4.setFixedHeight(20);

                // 水平居中
                e1.setHorizontalAlignment(Element.ALIGN_CENTER);
                e2.setHorizontalAlignment(Element.ALIGN_CENTER);
                e3.setHorizontalAlignment(Element.ALIGN_CENTER);
                e4.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                e1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e3.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e4.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                e1.setBorderColor(BaseColor.BLACK);
                e2.setBorderColor(BaseColor.BLACK);
                e3.setBorderColor(BaseColor.BLACK);
                e4.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框

                e2.disableBorderSide(4);

                e3.disableBorderSide(4);

                e4.disableBorderSide(4);

                e.addCell(e1);
                e.addCell(e2);
                e.addCell(e3);
                e.addCell(e4);

                document.add(e);
                number++;
            }

            // f
            PdfPTable f = new PdfPTable(2);

            int fwidth[] = {20, 130};
            f.setWidths(fwidth);
            PdfPCell f1 = new PdfPCell(new Paragraph("小计", fontChinese12));
            PdfPCell f2 = new PdfPCell(
                    new Paragraph(repairTaskViewBO.getRepairRepairTotalAmount() + "（元）", fontChinese12));

            // 表格高度
            f1.setFixedHeight(20);
            f2.setFixedHeight(20);

            // 水平居中
            f1.setHorizontalAlignment(Element.ALIGN_CENTER);
            f2.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            f1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            f2.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            f1.setBorderColor(BaseColor.BLACK);
            f2.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            f2.disableBorderSide(4);

            f.addCell(f1);
            f.addCell(f2);

            document.add(f);
            document.add(blankRow31c);
        } else {
            MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
            List<MtcLossFitInfoBO> mtcLossFitInfoBOList = mtcLossFitInfoMapper
                    .queryMtcLossFitItemList(repairTaskViewBO.getTaskNo());
            List<MtcLossRepairInfoBO> mtcLossRepairInfoBOList = mtcLossRepairInfoMapper
                    .queryMtcLossRepairItemList(repairTaskViewBO.getTaskNo());

            PdfPTable tabled = new PdfPTable(1);
            int widthd[] = {300};
            tabled.setWidths(widthd);
            PdfPCell cell31d = new PdfPCell(new Paragraph("换件项目清单", fontChinese11Bold));
            cell31d.setFixedHeight(20);
            cell31d.setBorder(0);

            tabled.addCell(cell31d);

            document.add(tabled);

            // 换件项目
            PdfPTable tablee = new PdfPTable(7);

            int wiethe[] = {20, 30, 25, 25, 25, 25, 25};
            tablee.setWidths(wiethe);
            PdfPCell celle1 = new PdfPCell(new Paragraph("序号", fontChinese12));
            PdfPCell celle2 = new PdfPCell(new Paragraph("更换项目", fontChinese12));
            PdfPCell celle3 = new PdfPCell(new Paragraph("原厂编号", fontChinese12));
            PdfPCell celle4 = new PdfPCell(new Paragraph("价格", fontChinese12));
            PdfPCell celle5 = new PdfPCell(new Paragraph("数量", fontChinese12));
            PdfPCell celle6 = new PdfPCell(new Paragraph("残值", fontChinese12));
            PdfPCell celle7 = new PdfPCell(new Paragraph("价格方案", fontChinese12));

            // 表格高度
            celle1.setFixedHeight(20);
            celle2.setFixedHeight(20);
            celle3.setFixedHeight(20);
            celle4.setFixedHeight(20);
            celle5.setFixedHeight(20);
            celle6.setFixedHeight(20);
            celle7.setFixedHeight(20);

            // 水平居中
            celle1.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle2.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle3.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle4.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle5.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle6.setHorizontalAlignment(Element.ALIGN_CENTER);
            celle7.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            celle1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle2.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle3.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle4.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle5.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle6.setVerticalAlignment(Element.ALIGN_MIDDLE);
            celle7.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            celle1.setBorderColor(BaseColor.BLACK);
            celle2.setBorderColor(BaseColor.BLACK);
            celle3.setBorderColor(BaseColor.BLACK);
            celle4.setBorderColor(BaseColor.BLACK);
            celle5.setBorderColor(BaseColor.BLACK);
            celle6.setBorderColor(BaseColor.BLACK);
            celle7.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            celle2.disableBorderSide(4);

            celle3.disableBorderSide(4);

            celle4.disableBorderSide(4);

            celle5.disableBorderSide(4);
            celle6.disableBorderSide(4);
            celle7.disableBorderSide(4);

            tablee.addCell(celle1);
            tablee.addCell(celle2);
            tablee.addCell(celle3);
            tablee.addCell(celle4);
            tablee.addCell(celle5);
            tablee.addCell(celle6);
            tablee.addCell(celle7);

            document.add(tablee);
            int numInteger = 1;
            for (MtcLossFitInfoBO mtcLossFitInfoBO : mtcLossFitInfoBOList) {
                PdfPTable a = new PdfPTable(7);
                int width[] = {20, 30, 25, 25, 25, 25, 25};
                a.setWidths(width);
                PdfPCell a1 = new PdfPCell(new Paragraph(numInteger + StringUtils.EMPTY, fontChinese12));
                PdfPCell a2 = new PdfPCell(new Paragraph(mtcLossFitInfoBO.getItemName(), fontChinese12));
                PdfPCell a3 = new PdfPCell(new Paragraph("", fontChinese12));
                PdfPCell a4 = new PdfPCell(new Paragraph(
                        mtcLossFitInfoBO.getAuditMaterialFee() + StringUtils.EMPTY + "（元）", fontChinese12));
                PdfPCell a5 = new PdfPCell(
                        new Paragraph(mtcLossFitInfoBO.getAuditCount().intValue() + StringUtils.EMPTY, fontChinese12));
                PdfPCell a6 = new PdfPCell(new Paragraph("", fontChinese12));
                String chgCompSetCode = "";
                if (StringUtils.isNotBlank(mtcLossFitInfoBO.getChgCompSetCode())) {
                    switch (mtcLossFitInfoBO.getChgCompSetCode()) {
                        case "1":
                            chgCompSetCode = "4S价格";
                            break;
                        case "2":
                            chgCompSetCode = "市场原厂价格";
                            break;
                        case "3":
                            chgCompSetCode = "品牌价格";
                            break;
                        case "4":
                            chgCompSetCode = "适用价格";
                            break;
                        case "5":
                            chgCompSetCode = "再制造价格";
                            break;
                        default:
                            chgCompSetCode = "";
                            break;
                    }
                }
                PdfPCell a7 = new PdfPCell(new Paragraph(chgCompSetCode, fontChinese12));
                // 表格高度
                a1.setFixedHeight(20);
                a2.setFixedHeight(20);
                a3.setFixedHeight(20);
                a4.setFixedHeight(20);
                a5.setFixedHeight(20);
                a6.setFixedHeight(20);
                a7.setFixedHeight(20);

                // 水平居中
                a1.setHorizontalAlignment(Element.ALIGN_CENTER);
                a2.setHorizontalAlignment(Element.ALIGN_CENTER);
                a3.setHorizontalAlignment(Element.ALIGN_CENTER);
                a4.setHorizontalAlignment(Element.ALIGN_CENTER);
                a5.setHorizontalAlignment(Element.ALIGN_CENTER);
                a6.setHorizontalAlignment(Element.ALIGN_CENTER);
                a7.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                a1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a3.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a4.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a5.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a6.setVerticalAlignment(Element.ALIGN_MIDDLE);
                a7.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                a1.setBorderColor(BaseColor.BLACK);
                a2.setBorderColor(BaseColor.BLACK);
                a3.setBorderColor(BaseColor.BLACK);
                a4.setBorderColor(BaseColor.BLACK);
                a5.setBorderColor(BaseColor.BLACK);
                a6.setBorderColor(BaseColor.BLACK);
                a7.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框

                a2.disableBorderSide(4);

                a3.disableBorderSide(4);

                a4.disableBorderSide(4);

                a5.disableBorderSide(4);
                a6.disableBorderSide(4);
                a7.disableBorderSide(4);

                a.addCell(a1);
                a.addCell(a2);
                a.addCell(a3);
                a.addCell(a4);
                a.addCell(a5);
                a.addCell(a6);
                a.addCell(a7);

                document.add(a);
                numInteger++;
            }

            // b
            PdfPTable b = new PdfPTable(2);

            int bwidth[] = {20, 155};
            b.setWidths(bwidth);
            PdfPCell b1 = new PdfPCell(new Paragraph("小计", fontChinese12));
            PdfPCell b2 = new PdfPCell(
                    new Paragraph(repairTaskViewBO.getRepairReplaceTotalAmount() + "（元）", fontChinese12));

            // 表格高度
            b1.setFixedHeight(20);
            b2.setFixedHeight(20);

            // 水平居中
            b1.setHorizontalAlignment(Element.ALIGN_CENTER);
            b2.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            b1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            b2.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            b1.setBorderColor(BaseColor.BLACK);
            b2.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            b2.disableBorderSide(4);

            b.addCell(b1);
            b.addCell(b2);

            document.add(b);
            document.add(blankRow31c);

            PdfPTable d = new PdfPTable(1);
            int dwidth[] = {300};
            d.setWidths(dwidth);
            PdfPCell d1 = new PdfPCell(new Paragraph("修理项目清单", fontChinese11Bold));
            d1.setFixedHeight(20);
            d1.setBorder(0);

            d.addCell(d1);

            document.add(d);

            List<RepairDetailBO> repairDetailBOs = repairItemDetailMapper.getRepairDetail(repairTaskViewBO.getTaskNo());
            // 修理项目清单
            PdfPTable c = new PdfPTable(6);

            int cwidth[] = {20, 50, 20, 35, 20, 35};
            c.setWidths(cwidth);
            PdfPCell c1 = new PdfPCell(new Paragraph("序号", fontChinese12));
            PdfPCell c2 = new PdfPCell(new Paragraph("修理名称", fontChinese12));
            PdfPCell c3 = new PdfPCell(new Paragraph("工种", fontChinese12));
            PdfPCell c4 = new PdfPCell(new Paragraph("工时单价", fontChinese12));
            PdfPCell c5 = new PdfPCell(new Paragraph("工时数", fontChinese12));
            PdfPCell c6 = new PdfPCell(new Paragraph("工时费", fontChinese12));

            // 表格高度
            c1.setFixedHeight(20);
            c2.setFixedHeight(20);
            c3.setFixedHeight(20);
            c4.setFixedHeight(20);
            c5.setFixedHeight(20);
            c6.setFixedHeight(20);

            // 水平居中
            c1.setHorizontalAlignment(Element.ALIGN_CENTER);
            c2.setHorizontalAlignment(Element.ALIGN_CENTER);
            c3.setHorizontalAlignment(Element.ALIGN_CENTER);
            c4.setHorizontalAlignment(Element.ALIGN_CENTER);
            c5.setHorizontalAlignment(Element.ALIGN_CENTER);
            c6.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            c1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c2.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c3.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c4.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c5.setVerticalAlignment(Element.ALIGN_MIDDLE);
            c6.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            c1.setBorderColor(BaseColor.BLACK);
            c2.setBorderColor(BaseColor.BLACK);
            c3.setBorderColor(BaseColor.BLACK);
            c4.setBorderColor(BaseColor.BLACK);
            c5.setBorderColor(BaseColor.BLACK);
            c6.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            c2.disableBorderSide(4);

            c3.disableBorderSide(4);

            c4.disableBorderSide(4);

            c5.disableBorderSide(4);

            c6.disableBorderSide(4);

            c.addCell(c1);
            c.addCell(c2);
            c.addCell(c3);
            c.addCell(c4);
            c.addCell(c5);
            c.addCell(c6);
            int number = 1;
            document.add(c);
            for (MtcLossRepairInfoBO mtcLossRepairInfoBO : mtcLossRepairInfoBOList) {
                // e
                PdfPTable e = new PdfPTable(6);

                int ewidth[] = {20, 50, 20, 35, 20, 35};
                e.setWidths(ewidth);
                PdfPCell e1 = new PdfPCell(new Paragraph(number + StringUtils.EMPTY, fontChinese12));
                PdfPCell e2 = new PdfPCell(new Paragraph(mtcLossRepairInfoBO.getItemName(), fontChinese12));
                PdfPCell e3 = new PdfPCell(new Paragraph(mtcLossRepairInfoBO.getRepairModeName(), fontChinese12));
                PdfPCell e4 = new PdfPCell(new Paragraph(
                        mtcLossRepairInfoBO.getRepairUnitPrice() + StringUtils.EMPTY + "（元）", fontChinese12));
                PdfPCell e5 = new PdfPCell(
                        new Paragraph(mtcLossRepairInfoBO.getApprHour() + StringUtils.EMPTY, fontChinese12));
                PdfPCell e6 = new PdfPCell(new Paragraph(
                        mtcLossRepairInfoBO.getAuditManpowerFee() + StringUtils.EMPTY + "（元）", fontChinese12));

                // 表格高度
                e1.setFixedHeight(20);
                e2.setFixedHeight(20);
                e3.setFixedHeight(20);
                e4.setFixedHeight(20);
                e5.setFixedHeight(20);
                e6.setFixedHeight(20);

                // 水平居中
                e1.setHorizontalAlignment(Element.ALIGN_CENTER);
                e2.setHorizontalAlignment(Element.ALIGN_CENTER);
                e3.setHorizontalAlignment(Element.ALIGN_CENTER);
                e4.setHorizontalAlignment(Element.ALIGN_CENTER);
                e5.setHorizontalAlignment(Element.ALIGN_CENTER);
                e6.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                e1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e3.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e4.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e5.setVerticalAlignment(Element.ALIGN_MIDDLE);
                e6.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                e1.setBorderColor(BaseColor.BLACK);
                e2.setBorderColor(BaseColor.BLACK);
                e3.setBorderColor(BaseColor.BLACK);
                e4.setBorderColor(BaseColor.BLACK);
                e5.setBorderColor(BaseColor.BLACK);
                e6.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框

                e2.disableBorderSide(4);

                e3.disableBorderSide(4);

                e4.disableBorderSide(4);

                e5.disableBorderSide(4);

                e6.disableBorderSide(4);

                e.addCell(e1);
                e.addCell(e2);
                e.addCell(e3);
                e.addCell(e4);
                e.addCell(e5);
                e.addCell(e6);

                document.add(e);
                number++;
            }

            // f
            PdfPTable f = new PdfPTable(2);

            int fwidth[] = {20, 160};
            f.setWidths(fwidth);
            PdfPCell f1 = new PdfPCell(new Paragraph("小计", fontChinese12));
            PdfPCell f2 = new PdfPCell(
                    new Paragraph(repairTaskViewBO.getRepairRepairTotalAmount() + "（元）", fontChinese12));

            // 表格高度
            f1.setFixedHeight(20);
            f2.setFixedHeight(20);

            // 水平居中
            f1.setHorizontalAlignment(Element.ALIGN_CENTER);
            f2.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            f1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            f2.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            f1.setBorderColor(BaseColor.BLACK);
            f2.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框

            f2.disableBorderSide(4);

            f.addCell(f1);
            f.addCell(f2);

            document.add(f);
            document.add(blankRow31c);

            if (mtcLossInfo.getAuditMateSum().doubleValue() > 0 || mtcLossInfo.getTotalManageSum().doubleValue() > 0
                    || mtcLossInfo.getAuditSalvageFee().doubleValue() > 0
                    || mtcLossInfo.getAuditRemnantFee().doubleValue() > 0) {
                PdfPTable o = new PdfPTable(1);
                int owidth[] = {300};
                tabled.setWidths(owidth);
                PdfPCell cello = new PdfPCell(new Paragraph("其他合计费用", fontChinese11Bold));
                cello.setFixedHeight(20);
                cello.setBorder(0);

                o.addCell(cello);

                document.add(o);

                // 其他合计费用
                PdfPTable tableo = new PdfPTable(3);

                int wietho[] = {20, 80, 80};
                tableo.setWidths(wietho);
                PdfPCell cello1 = new PdfPCell(new Paragraph("序号", fontChinese12));
                PdfPCell cello2 = new PdfPCell(new Paragraph("合计费用类目", fontChinese12));
                PdfPCell cello3 = new PdfPCell(new Paragraph("合计金额", fontChinese12));

                // 表格高度
                cello1.setFixedHeight(20);
                cello2.setFixedHeight(20);
                cello3.setFixedHeight(20);

                // 水平居中
                cello1.setHorizontalAlignment(Element.ALIGN_CENTER);
                cello2.setHorizontalAlignment(Element.ALIGN_CENTER);
                cello3.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                cello1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cello2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cello3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                cello1.setBorderColor(BaseColor.BLACK);
                cello2.setBorderColor(BaseColor.BLACK);
                cello3.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框
                cello2.disableBorderSide(4);
                cello3.disableBorderSide(4);

                tableo.addCell(cello1);
                tableo.addCell(cello2);
                tableo.addCell(cello3);

                document.add(tableo);
                numInteger = 1;

                if (mtcLossInfo.getAuditMateSum().doubleValue() > 0) {
                    PdfPTable a = new PdfPTable(3);
                    int width[] = {20, 80, 80};
                    a.setWidths(width);
                    PdfPCell a1 = new PdfPCell(new Paragraph(numInteger + StringUtils.EMPTY, fontChinese12));
                    PdfPCell a2 = new PdfPCell(new Paragraph("辅料", fontChinese12));
                    PdfPCell a3 = new PdfPCell(new Paragraph(mtcLossInfo.getAuditMateSum() + "（元）", fontChinese12));
                    // 表格高度
                    a1.setFixedHeight(20);
                    a2.setFixedHeight(20);
                    a3.setFixedHeight(20);

                    // 水平居中
                    a1.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a2.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a3.setHorizontalAlignment(Element.ALIGN_CENTER);

                    // 垂直居中
                    a1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                    // 边框颜色
                    a1.setBorderColor(BaseColor.BLACK);
                    a2.setBorderColor(BaseColor.BLACK);
                    a3.setBorderColor(BaseColor.BLACK);

                    // 去掉左右边框
                    a2.disableBorderSide(4);
                    a3.disableBorderSide(4);

                    a.addCell(a1);
                    a.addCell(a2);
                    a.addCell(a3);

                    document.add(a);
                    numInteger++;
                }

                if (mtcLossInfo.getTotalManageSum().doubleValue() > 0) {
                    PdfPTable a = new PdfPTable(3);
                    int width[] = {20, 80, 80};
                    a.setWidths(width);
                    PdfPCell a1 = new PdfPCell(new Paragraph(numInteger + StringUtils.EMPTY, fontChinese12));
                    PdfPCell a2 = new PdfPCell(new Paragraph("管理费", fontChinese12));
                    PdfPCell a3 = new PdfPCell(new Paragraph(mtcLossInfo.getTotalManageSum() + "（元）", fontChinese12));
                    // 表格高度
                    a1.setFixedHeight(20);
                    a2.setFixedHeight(20);
                    a3.setFixedHeight(20);

                    // 水平居中
                    a1.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a2.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a3.setHorizontalAlignment(Element.ALIGN_CENTER);

                    // 垂直居中
                    a1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                    // 边框颜色
                    a1.setBorderColor(BaseColor.BLACK);
                    a2.setBorderColor(BaseColor.BLACK);
                    a3.setBorderColor(BaseColor.BLACK);

                    // 去掉左右边框
                    a2.disableBorderSide(4);
                    a3.disableBorderSide(4);

                    a.addCell(a1);
                    a.addCell(a2);
                    a.addCell(a3);

                    document.add(a);
                    numInteger++;
                }

                if (mtcLossInfo.getAuditSalvageFee().doubleValue() > 0) {
                    PdfPTable a = new PdfPTable(3);
                    int width[] = {20, 80, 80};
                    a.setWidths(width);
                    PdfPCell a1 = new PdfPCell(new Paragraph(numInteger + StringUtils.EMPTY, fontChinese12));
                    PdfPCell a2 = new PdfPCell(new Paragraph("施救", fontChinese12));
                    PdfPCell a3 = new PdfPCell(new Paragraph(mtcLossInfo.getAuditSalvageFee() + "（元）", fontChinese12));
                    // 表格高度
                    a1.setFixedHeight(20);
                    a2.setFixedHeight(20);
                    a3.setFixedHeight(20);

                    // 水平居中
                    a1.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a2.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a3.setHorizontalAlignment(Element.ALIGN_CENTER);

                    // 垂直居中
                    a1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                    // 边框颜色
                    a1.setBorderColor(BaseColor.BLACK);
                    a2.setBorderColor(BaseColor.BLACK);
                    a3.setBorderColor(BaseColor.BLACK);

                    // 去掉左右边框
                    a2.disableBorderSide(4);
                    a3.disableBorderSide(4);

                    a.addCell(a1);
                    a.addCell(a2);
                    a.addCell(a3);

                    document.add(a);
                    numInteger++;
                }

                if (mtcLossInfo.getAuditRemnantFee().doubleValue() > 0) {
                    PdfPTable a = new PdfPTable(3);
                    int width[] = {20, 80, 80};
                    a.setWidths(width);
                    PdfPCell a1 = new PdfPCell(new Paragraph(numInteger + StringUtils.EMPTY, fontChinese12));
                    PdfPCell a2 = new PdfPCell(new Paragraph("残值", fontChinese12));
                    PdfPCell a3 = new PdfPCell(new Paragraph(mtcLossInfo.getAuditRemnantFee() + "（元）", fontChinese12));
                    // 表格高度
                    a1.setFixedHeight(20);
                    a2.setFixedHeight(20);
                    a3.setFixedHeight(20);

                    // 水平居中
                    a1.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a2.setHorizontalAlignment(Element.ALIGN_CENTER);
                    a3.setHorizontalAlignment(Element.ALIGN_CENTER);

                    // 垂直居中
                    a1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    a3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                    // 边框颜色
                    a1.setBorderColor(BaseColor.BLACK);
                    a2.setBorderColor(BaseColor.BLACK);
                    a3.setBorderColor(BaseColor.BLACK);

                    // 去掉左右边框
                    a2.disableBorderSide(4);
                    a3.disableBorderSide(4);

                    a.addCell(a1);
                    a.addCell(a2);
                    a.addCell(a3);

                    document.add(a);
                }

                PdfPTable n = new PdfPTable(2);

                int nwidth[] = {20, 160};
                n.setWidths(nwidth);
                PdfPCell n1 = new PdfPCell(new Paragraph("小计", fontChinese12));
                PdfPCell n2 = new PdfPCell(new Paragraph(mtcLossInfo.getAuditMateSum()
                        .add(mtcLossInfo.getTotalManageSum()).add(mtcLossInfo.getAuditSalvageFee())
                        .subtract(mtcLossInfo.getAuditRemnantFee()).toString() + "（元）", fontChinese12));

                // 表格高度
                n1.setFixedHeight(20);
                n2.setFixedHeight(20);

                // 水平居中
                n1.setHorizontalAlignment(Element.ALIGN_CENTER);
                n2.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                n1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                n2.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                n1.setBorderColor(BaseColor.BLACK);
                n2.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框

                n2.disableBorderSide(4);

                n.addCell(n1);
                n.addCell(n2);

                document.add(n);
                document.add(blankRow31c);
            }
        }

        PdfPTable g = new PdfPTable(1);
        int gwidth[] = {300};
        g.setWidths(gwidth);
        PdfPCell g1 = new PdfPCell(new Paragraph("签字区", fontChinese11Bold));
        g1.setFixedHeight(20);
        g1.setBorder(0);

        g.addCell(g1);

        document.add(g);
        // h
        PdfPTable h = new PdfPTable(4);

        int hwidth[] = {30, 20, 20, 20};
        h.setWidths(hwidth);
        PdfPCell h1 = new PdfPCell(new Paragraph("车管", fontChinese12));
        PdfPCell h2 = new PdfPCell(new Paragraph("运营经理", fontChinese12));
        PdfPCell h3 = new PdfPCell(new Paragraph("资产管理部", fontChinese12));
        PdfPCell h4 = new PdfPCell(new Paragraph("运营管理部", fontChinese12));

        // 表格高度
        h1.setFixedHeight(20);
        h2.setFixedHeight(20);
        h3.setFixedHeight(20);
        h4.setFixedHeight(20);

        // 水平居中
        h1.setHorizontalAlignment(Element.ALIGN_CENTER);
        h2.setHorizontalAlignment(Element.ALIGN_CENTER);
        h3.setHorizontalAlignment(Element.ALIGN_CENTER);
        h4.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 垂直居中
        h1.setVerticalAlignment(Element.ALIGN_MIDDLE);
        h2.setVerticalAlignment(Element.ALIGN_MIDDLE);
        h3.setVerticalAlignment(Element.ALIGN_MIDDLE);
        h4.setVerticalAlignment(Element.ALIGN_MIDDLE);

        // 边框颜色
        h1.setBorderColor(BaseColor.BLACK);
        h2.setBorderColor(BaseColor.BLACK);
        h3.setBorderColor(BaseColor.BLACK);
        h4.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框

        h2.disableBorderSide(4);

        h3.disableBorderSide(4);

        h4.disableBorderSide(4);

        h.addCell(h1);
        h.addCell(h2);
        h.addCell(h3);
        h.addCell(h4);
        document.add(h);
        // i
        PdfPTable i = new PdfPTable(4);

        int iwidti[] = {30, 20, 20, 20};
        i.setWidths(iwidti);
        // PdfPCell i1 = new PdfPCell(new
        // Paragraph(mtcUserMapper.taskOperName(repairTaskViewBO.getVerificationLossCheckId())
        // == null ? " 姓名:" : " 姓名:" +
        // mtcUserMapper.taskOperName(repairTaskViewBO.getVerificationLossCheckId()),
        // FontChinese12));

        if (Double.valueOf(repairTaskViewBO.getVehicleInsuranceTotalAmount()) > 2000) {
            if (mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "运营经理") != null) {
                PdfPCell i1 = new PdfPCell(new Paragraph(
                        "     姓名:"
                                + mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "提交上级").getUpdateOperName(),
                        fontChinese12));
                PdfPCell i2 = new PdfPCell(new Paragraph(
                        mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "运营经理").getUpdateOperName(),
                        fontChinese12));
                PdfPCell i3 = new PdfPCell(new Paragraph(
                        mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "资产管理部").getUpdateOperName(),
                        fontChinese12));
                PdfPCell i4 = new PdfPCell(new Paragraph(
                        mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "运营管理部").getUpdateOperName(),
                        fontChinese12));

                // 表格高度
                i1.setFixedHeight(20);
                i2.setFixedHeight(20);
                i3.setFixedHeight(20);
                i4.setFixedHeight(20);

                i1.setHorizontalAlignment(Element.ALIGN_LEFT);
                i2.setHorizontalAlignment(Element.ALIGN_CENTER);
                i3.setHorizontalAlignment(Element.ALIGN_CENTER);
                i4.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                i1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                i2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                i3.setVerticalAlignment(Element.ALIGN_MIDDLE);
                i4.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                i1.setBorderColor(BaseColor.BLACK);
                i2.setBorderColor(BaseColor.BLACK);
                i3.setBorderColor(BaseColor.BLACK);
                i4.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框
                i1.disableBorderSide(2);
                i2.disableBorderSide(4);
                i2.disableBorderSide(2);
                i3.disableBorderSide(4);
                i3.disableBorderSide(2);
                i4.disableBorderSide(4);
                i4.disableBorderSide(2);

                i.addCell(i1);
                i.addCell(i2);
                i.addCell(i3);
                i.addCell(i4);
            } else {
                PdfPCell i1 = new PdfPCell(new Paragraph(
                        "     姓名:"
                                + mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "审核通过").getUpdateOperName(),
                        fontChinese12));
                PdfPCell i2 = new PdfPCell(new Paragraph("", fontChinese12));
                PdfPCell i3 = new PdfPCell(new Paragraph("", fontChinese12));
                PdfPCell i4 = new PdfPCell(new Paragraph("", fontChinese12));

                // 表格高度
                i1.setFixedHeight(20);
                i2.setFixedHeight(20);
                i3.setFixedHeight(20);
                i4.setFixedHeight(20);

                i1.setHorizontalAlignment(Element.ALIGN_LEFT);
                i2.setHorizontalAlignment(Element.ALIGN_CENTER);
                i3.setHorizontalAlignment(Element.ALIGN_CENTER);
                i4.setHorizontalAlignment(Element.ALIGN_CENTER);

                // 垂直居中
                i1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                i2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                i3.setVerticalAlignment(Element.ALIGN_MIDDLE);
                i4.setVerticalAlignment(Element.ALIGN_MIDDLE);

                // 边框颜色
                i1.setBorderColor(BaseColor.BLACK);
                i2.setBorderColor(BaseColor.BLACK);
                i3.setBorderColor(BaseColor.BLACK);
                i4.setBorderColor(BaseColor.BLACK);

                // 去掉左右边框
                i1.disableBorderSide(2);
                i2.disableBorderSide(4);
                i2.disableBorderSide(2);
                i3.disableBorderSide(4);
                i3.disableBorderSide(2);
                i4.disableBorderSide(4);
                i4.disableBorderSide(2);

                i.addCell(i1);
                i.addCell(i2);
                i.addCell(i3);
                i.addCell(i4);
            }
        } else {
            PdfPCell i1 = new PdfPCell(new Paragraph(
                    "     姓名:" + mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "审核通过").getUpdateOperName(),
                    fontChinese12));
            PdfPCell i2 = new PdfPCell(new Paragraph("", fontChinese12));
            PdfPCell i3 = new PdfPCell(new Paragraph("", fontChinese12));
            PdfPCell i4 = new PdfPCell(new Paragraph("", fontChinese12));

            // 表格高度
            i1.setFixedHeight(20);
            i2.setFixedHeight(20);
            i3.setFixedHeight(20);
            i4.setFixedHeight(20);

            i1.setHorizontalAlignment(Element.ALIGN_LEFT);
            i2.setHorizontalAlignment(Element.ALIGN_CENTER);
            i3.setHorizontalAlignment(Element.ALIGN_CENTER);
            i4.setHorizontalAlignment(Element.ALIGN_CENTER);

            // 垂直居中
            i1.setVerticalAlignment(Element.ALIGN_MIDDLE);
            i2.setVerticalAlignment(Element.ALIGN_MIDDLE);
            i3.setVerticalAlignment(Element.ALIGN_MIDDLE);
            i4.setVerticalAlignment(Element.ALIGN_MIDDLE);

            // 边框颜色
            i1.setBorderColor(BaseColor.BLACK);
            i2.setBorderColor(BaseColor.BLACK);
            i3.setBorderColor(BaseColor.BLACK);
            i4.setBorderColor(BaseColor.BLACK);

            // 去掉左右边框
            i1.disableBorderSide(2);
            i2.disableBorderSide(4);
            i2.disableBorderSide(2);
            i3.disableBorderSide(4);
            i3.disableBorderSide(2);
            i4.disableBorderSide(4);
            i4.disableBorderSide(2);

            i.addCell(i1);
            i.addCell(i2);
            i.addCell(i3);
            i.addCell(i4);
        }

        document.add(i);
        // j
        PdfPTable j = new PdfPTable(4);

        int jwidth[] = {30, 20, 20, 20};
        j.setWidths(jwidth);
        String checkTime = null;
        if (Double.valueOf(repairTaskViewBO.getVehicleInsuranceTotalAmount()) > Double.valueOf(2000)) {
            if (mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "提交上级") != null) {
                checkTime = mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "提交上级").getUpdateTime();
            } else {
                checkTime = mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "审核通过").getUpdateTime();
            }

        } else {
            checkTime = mtcOperatorLogMapper.getReapirLog(id + StringUtils.EMPTY, "审核通过").getUpdateTime();
        }
        PdfPCell j1 = new PdfPCell(new Paragraph("     审核时间:" + checkTime, fontChinese12));
        PdfPCell j2 = new PdfPCell(new Paragraph("", fontChinese12));
        PdfPCell j3 = new PdfPCell(new Paragraph("", fontChinese12));
        PdfPCell j4 = new PdfPCell(new Paragraph("", fontChinese12));

        // 表格高度
        j1.setFixedHeight(20);
        j2.setFixedHeight(20);
        j3.setFixedHeight(20);
        j4.setFixedHeight(20);

        // 垂直居中
        j1.setVerticalAlignment(Element.ALIGN_MIDDLE);
        j2.setVerticalAlignment(Element.ALIGN_MIDDLE);
        j3.setVerticalAlignment(Element.ALIGN_MIDDLE);
        j4.setVerticalAlignment(Element.ALIGN_MIDDLE);

        j1.setHorizontalAlignment(Element.ALIGN_LEFT);
        j2.setHorizontalAlignment(Element.ALIGN_CENTER);
        j3.setHorizontalAlignment(Element.ALIGN_CENTER);
        j4.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 边框颜色
        j1.setBorderColor(BaseColor.BLACK);
        j2.setBorderColor(BaseColor.BLACK);
        j3.setBorderColor(BaseColor.BLACK);
        j4.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框
        j1.disableBorderSide(2);
        j1.disableBorderSide(1);
        j2.disableBorderSide(4);
        j2.disableBorderSide(1);
        j2.disableBorderSide(2);
        j3.disableBorderSide(4);
        j3.disableBorderSide(2);
        j3.disableBorderSide(1);
        j4.disableBorderSide(4);
        j4.disableBorderSide(2);
        j4.disableBorderSide(1);
        j.addCell(j1);
        j.addCell(j2);
        j.addCell(j3);
        j.addCell(j4);

        document.add(j);
        // k
        PdfPTable k = new PdfPTable(4);

        int kwidth[] = {30, 20, 20, 20};
        k.setWidths(kwidth);
        PdfPCell k1 = new PdfPCell(
                new Paragraph("     审核金额:" + repairTaskViewBO.getVehicleInsuranceTotalAmount() + "（元）", fontChinese12));
        PdfPCell k2 = new PdfPCell(new Paragraph("", fontChinese12));
        PdfPCell k3 = new PdfPCell(new Paragraph("", fontChinese12));
        PdfPCell k4 = new PdfPCell(new Paragraph("", fontChinese12));

        // 表格高度
        k1.setFixedHeight(20);
        k2.setFixedHeight(20);
        k3.setFixedHeight(20);
        k4.setFixedHeight(20);

        // 垂直居中
        k1.setVerticalAlignment(Element.ALIGN_MIDDLE);
        k2.setVerticalAlignment(Element.ALIGN_MIDDLE);
        k3.setVerticalAlignment(Element.ALIGN_MIDDLE);
        k4.setVerticalAlignment(Element.ALIGN_MIDDLE);

        k1.setHorizontalAlignment(Element.ALIGN_LEFT);
        k2.setHorizontalAlignment(Element.ALIGN_CENTER);
        k3.setHorizontalAlignment(Element.ALIGN_CENTER);
        k4.setHorizontalAlignment(Element.ALIGN_CENTER);

        // 边框颜色
        k1.setBorderColor(BaseColor.BLACK);
        k2.setBorderColor(BaseColor.BLACK);
        k3.setBorderColor(BaseColor.BLACK);
        k4.setBorderColor(BaseColor.BLACK);

        // 去掉左右边框

        k1.disableBorderSide(1);
        k2.disableBorderSide(4);
        k2.disableBorderSide(1);

        k3.disableBorderSide(4);

        k3.disableBorderSide(1);
        k4.disableBorderSide(4);

        k4.disableBorderSide(1);
        k.addCell(k1);
        k.addCell(k2);
        k.addCell(k3);
        k.addCell(k4);

        document.add(k);
        document.add(blankRow31c);
        PdfPTable l = new PdfPTable(1);
        l.setWidths(gwidth);
        PdfPCell l1 = new PdfPCell(new Paragraph("其他意见", fontChinese11Bold));
        l1.setFixedHeight(20);
        l1.setBorder(0);
        l.addCell(l1);
        document.add(l);
        PdfPTable m = new PdfPTable(1);
        int mwidth[] = {1000};
        m.setWidths(mwidth);
        PdfPCell m1 = new PdfPCell(new Paragraph("", fontChinese11Bold));
        m1.setFixedHeight(50);
        m1.setBorderColor(BaseColor.BLACK);
        m.addCell(m1);
        document.add(m);
    }

    /**
     * 计算缓冲时间跳过非工作时间/节假日
     * @param subBufferInfo
     * @param vehicleCheckTime
     * @return
     */
    private Date skipHolidayAndWeekend(BdpManageWorkDTO subBufferInfo, String vehicleCheckTime) {

        String bufferTime = subBufferInfo.getBufferTime();
        String workTime = subBufferInfo.getWorkTime();
        if (workTime == null) {
            workTime = "09:00-17:00";
        }
        String[] split = workTime.split("-");
        String startTime = split[0];
        String endTime = split[1];
        //Date date = new Date();
        Date date = DateUtil.getDateFromStr(vehicleCheckTime, DateUtil.DATE_TYPE5);
        // 今天上班时间
        String todayWorkTime = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE5) + " " +startTime + ":00";
        String tomorrowWorkTime = DateUtil.getFormatDate(DateUtil.AddDay(date, 1), DateUtil.DATE_TYPE5) + " " +startTime + ":00";
        String todayUpTime = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE5) + " " + endTime + ":00";
        log.info("公司信息:{}", JSON.toJSONString(subBufferInfo));
        log.info("今天上班时间:{}-->今天下班时间:{}-->明天上班时间:{}", todayWorkTime, todayUpTime, tomorrowWorkTime);
        // 验收时间+缓冲时间
        Date checkTime = DateUtil.AddHours(DateUtil.getDateFromStr(vehicleCheckTime, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime));
        String expectTime = DateUtil.getFormatDate(checkTime, DateUtil.DATE_TYPE2);
        log.info("验收时间:{}", vehicleCheckTime);
        log.info("验收时间+缓冲时间:{}",expectTime);

        Date todayWorkDateTime = DateUtil.getDateFromStr(todayWorkTime, DateUtil.DATE_TYPE2);
        Date todayUpDateTime = DateUtil.getDateFromStr(todayUpTime, DateUtil.DATE_TYPE2);
        Date tomorrowWorkDateTime = DateUtil.getDateFromStr(tomorrowWorkTime, DateUtil.DATE_TYPE2);

//        // 如果验收当天是周末或者节假日
//        //getWorkDay(date)
//        String formatDate = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE2);
//        if (isHoliday(formatDate) || isWeekEnd(formatDate)){
//            String workDay = DateUtil.getFormatDate(getWorkDay(date), DateUtil.DATE_TYPE5) + " " + startTime + ":00";
//            expectTime = DateUtil.getFormatDate(DateUtil.AddHours(DateUtil.getDateFromStr(workDay, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime)), DateUtil.DATE_TYPE2);
//        }else if (vehicleCheckTime.compareTo(todayWorkTime) < 0){
//            // 验收时间早于上班时间 --》约定时间== 今天上班时间+缓冲时间
//            expectTime = DateUtil.getFormatDate(DateUtil.AddHours(DateUtil.getDateFromStr(todayWorkTime, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime)), DateUtil.DATE_TYPE2);
//        }else if (vehicleCheckTime.compareTo(todayUpTime) > 0){
//            // 验收时间晚于下班时间 --》约定时间== 明天上班时间+缓冲时间
//            expectTime = DateUtil.getFormatDate(DateUtil.AddHours(DateUtil.getDateFromStr(tomorrowWorkTime, DateUtil.DATE_TYPE2), Integer.valueOf(bufferTime)), DateUtil.DATE_TYPE2);
//        }else if (expectTime.compareTo(todayUpTime) > 0){
//            // 验收时间大于今天下班时间
//            //int differHour = DateUtil.getDifferHour(todayUpTime, expectTime);
//            int differMin = DateUtil.getDiffMin(todayUpTime, expectTime);
//            expectTime = DateUtil.getFormatDate(DateUtil.AddMin(DateUtil.getDateFromStr(tomorrowWorkTime, DateUtil.DATE_TYPE2), differMin), DateUtil.DATE_TYPE2);
//        }else if (expectTime.compareTo(todayUpTime) < 0){
//            // 验收时间小于今天下班时
//        }

        if (checkTime.compareTo(todayWorkDateTime) < 0){
            // 验收时间早于上班时间 --》约定时间== 今天上班时间+缓冲时间
            checkTime = DateUtil.AddHours(todayWorkDateTime,Integer.valueOf(bufferTime));
        } else if (checkTime.compareTo(todayUpDateTime) > 0){
            // 验收时间晚于下班时间 --》 计算时间差
            int minute = DateUtil.minuteBetween(todayUpDateTime,checkTime);
            if (minute < Integer.valueOf(bufferTime) * 60){
                checkTime = DateUtil.AddMin(tomorrowWorkDateTime,minute);
            } else {
                checkTime = DateUtil.AddHours(tomorrowWorkDateTime,Integer.valueOf(bufferTime));
            }
        }

//        if (isHoliday(expectTime) || isWeekEnd(expectTime)){
//            Date workDay = getWorkDay(DateUtil.getDateFromStr(expectTime, DateUtil.DATE_TYPE2));
//            expectTime = DateUtil.getFormatDate(workDay, DateUtil.DATE_TYPE2);
//        }
        log.info("实际返回取车时间为：{}",DateUtil.getFormatDate(checkTime, DateUtil.DATE_TYPE2));
        return checkTime;

    }

    /**
     * 验收通过发送短信
     * @param taskBoReturn
     * @param expectTime
     */
    private void sendMsg(IrtRentReplaceVehicleTaskBo taskBoReturn, String expectTime, BdpManageWorkDTO subBuffer) {

        // 验收成功通知发送短信
        String rentUserPhone = taskBoReturn.getRentUserPhone();
        String rentUserName = taskBoReturn.getRentUserName();
        String salePhone = taskBoReturn.getSalePhone();
        String saleName = taskBoReturn.getSaleName();

      /*  StringBuilder rentUserSb = new StringBuilder();
        rentUserSb.append("您的长租车辆 车牌：")
                .append(taskBoReturn.getVehicleNo())
                .append("，已维修完毕。请于")
                .append(expectTime)
                .append("前取车，如有问题请联系工作人员：")
                .append(subBuffer.getContactPhone());
        BaseResponse rentUserResponse = iMessagepushServ.syncSendvoiceVerify(rentUserPhone, rentUserSb.toString(), "evcard-mtc");
        if (rentUserResponse != null && "0".equals(rentUserResponse.getCode())){
            log.info("租客：{} 短信发送成功 手机号码为:{} 短信内容:{}", rentUserName, rentUserPhone, rentUserSb.toString());
        }*/
        StringBuilder manageSb = new StringBuilder();
        manageSb.append("长租客户：")
                .append(rentUserName)
                .append(" 手机号：")
                .append(rentUserPhone)
                .append(" 车牌：")
                .append(taskBoReturn.getVehicleNo())
                .append("，已维修完毕。请联系用户于")
                .append(expectTime)
                .append("前取车");
        BaseResponse manageRes = iMessagepushServ.syncSendvoiceVerify(subBuffer.getContactPhone(), manageSb.toString(), "evcard-mtc");
        if (manageRes != null && "0".equals(manageRes.getCode())){
            log.info("车管：{} 短信发送成功 手机号码为:{} 短信内容:{}", subBuffer.getContactName(), subBuffer.getContactPhone(),  manageSb.toString());
        }
        /*
        BaseResponse sellRes = iMessagepushServ.syncSendvoiceVerify(salePhone, manageSb.toString(), "evcard-mtc");
        if (sellRes != null && "0".equals(sellRes.getCode())){
            log.info("销售：{} 短信发送成功 手机号码为:{} 短信内容:{}", saleName, salePhone, manageSb.toString());
        }*/

    }

    /**
     * 获取工作日时间
     * @param date
     * @return
     */
    public Date getWorkDay(Date date){

        for (int i = 0; i < 10; i++) {
            String formatDate = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE7);
            String formatDate1 = DateUtil.getFormatDate(date, DateUtil.DATE_TYPE2);
            boolean holiday = this.isHoliday(formatDate);
            boolean weekEnd = this.isWeekEnd(formatDate1);
            if (holiday || weekEnd ){
                date = DateUtil.AddDay(date, 1);
            }else {
                break;
            }
        }
        return date;
    }

    /**
     * 是否是周末
     * @param date
     * @return
     */
    private boolean isWeekEnd(String date){
        boolean isWeekend = DateUtilsHmy.isWeeks(DateUtilsHmy.str2Date( date, DateUtilsHmy.yyyyMMddFormat));
        return isWeekend;
    }

    private boolean isHoliday(String date){
        long l = rentReplaceVehicleService.queryHolidayCount(date);
        if (l > 0){
            return true;
        }
        return false;
    }

    /**
     * 根据事故编号获取已核价总金额
     *
     * @param accidentNo 事故编号
     * @return
     */
    private BigDecimal getVehicleInsuranceTotalAmountByAccidentNo(String accidentNo) {
        if (StringUtils.isNotBlank(accidentNo)) {
            List<RepairTask> repairTaskList = manualUtils.getRepairTaskListByManualNo(accidentNo);
            return repairTaskList.stream().map(RepairTask::getVehicleInsuranceTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return null;
    }
}
