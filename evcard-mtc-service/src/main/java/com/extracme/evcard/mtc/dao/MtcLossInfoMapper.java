package com.extracme.evcard.mtc.dao;

import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import com.extracme.evcard.mtc.model.MtcLossInfo;

import java.util.List;

/**
 * Mapper类，，对应表mtc_loss_info
 */
@OecsMapper
public interface MtcLossInfoMapper extends Dao<MtcLossInfo> {
    /**
     * 查询定损单列表
     * @param mtcLossInfo 参数
     * @return 返回结果
     */
    List<MtcLossInfo> queryMtcLossList(MtcLossInfo mtcLossInfo);

    /**
     * 修改定损单状态
     * @param taskNo 任务编号
     */
    void updateStatus(String taskNo);

    /**
     * 修改定损单状态
     * @param taskNo 任务编号
     */
    void updateAuditHandlerCode(String taskNo);

    /**
     * 删除定损单
     * @param taskNo 任务编号
     */
    void deleteByTaskNo(String taskNo);
}