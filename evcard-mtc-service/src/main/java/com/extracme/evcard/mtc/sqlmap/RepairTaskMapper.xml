<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- RepairTaskMapper，，对应表mtc_repair_task -->
<mapper namespace="com.extracme.evcard.mtc.dao.RepairTaskMapper">
  <!-- 返回结果集Map -->
  <resultMap id="BaseResultMap" type="com.extracme.evcard.mtc.model.RepairTask">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="id" jdbcType="BIGINT" property="id"/>
    <result column="task_no" jdbcType="VARCHAR" property="taskNo"/>
    <result column="task_type" jdbcType="DECIMAL" property="taskType"/>
    <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
    <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo"/>
    <result column="vehicle_model_seq" jdbcType="BIGINT" property="vehicleModelSeq"/>
    <result column="vehicle_model_info" jdbcType="VARCHAR" property="vehicleModelInfo"/>
    <result column="vin" jdbcType="VARCHAR" property="vin"/>
    <result column="insurance_company_name" jdbcType="VARCHAR" property="insuranceCompanyName"/>
    <result column="repair_type_id" jdbcType="DECIMAL" property="repairTypeId"/>
    <result column="repair_type_name" jdbcType="VARCHAR" property="repairTypeName"/>
    <result column="repair_grade" jdbcType="VARCHAR" property="repairGrade"/>
    <result column="repair_depot_id" jdbcType="VARCHAR" property="repairDepotId"/>
    <result column="repair_depot_name" jdbcType="VARCHAR" property="repairDepotName"/>
    <result column="repair_depot_org_id" jdbcType="VARCHAR" property="repairDepotOrgId"/>
    <result column="repair_depot_type" jdbcType="INTEGER" property="repairDepotType"/>
    <result column="task_inflow_time" jdbcType="TIMESTAMP" property="taskInflowTime"/>
    <result column="vehicle_recive_time" jdbcType="TIMESTAMP" property="vehicleReciveTime"/>
    <result column="vehicle_check_time" jdbcType="TIMESTAMP" property="vehicleCheckTime"/>
    <result column="vehicle_repair_time" jdbcType="TIMESTAMP" property="vehicleRepairTime"/>
    <result column="reassignment_pass_time" jdbcType="TIMESTAMP" property="reassignmentPassTime"/>
    <result column="settle_closing_time" jdbcType="TIMESTAMP" property="settleClosingTime"/>
    <result column="expected_repair_days" jdbcType="BIGINT" property="expectedRepairDays"/>
    <result column="expected_repair_complete" jdbcType="TIMESTAMP" property="expectedRepairComplete"/>
    <result column="accident_report_number" jdbcType="VARCHAR" property="accidentReportNumber"/>
    <result column="repair_flag" jdbcType="DECIMAL" property="repairFlag"/>
    <result column="terminal_id" jdbcType="VARCHAR" property="terminalId"/>
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage"/>
    <result column="associated_order" jdbcType="VARCHAR" property="associatedOrder"/>
    <result column="order_type" jdbcType="INTEGER" property="orderType"/>
    <result column="relate_type" jdbcType="INTEGER" property="relateType"/>
    <result column="order_remark" jdbcType="VARCHAR" property="orderRemark"/>
    <result column="no_deductibles_flag" jdbcType="DECIMAL" property="noDeductiblesFlag"/>
    <result column="driver_name" jdbcType="VARCHAR" property="driverName"/>
    <result column="driver_tel" jdbcType="VARCHAR" property="driverTel"/>
    <result column="routing_inspection_name" jdbcType="VARCHAR" property="routingInspectionName"/>
    <result column="routing_inspection_tel" jdbcType="VARCHAR" property="routingInspectionTel"/>
    <result column="damaged_part_describe" jdbcType="VARCHAR" property="damagedPartDescribe"/>
    <result column="accident_describe" jdbcType="VARCHAR" property="accidentDescribe"/>
    <result column="trailer_flag" jdbcType="DECIMAL" property="trailerFlag"/>
    <result column="repair_review_total_amount" jdbcType="DECIMAL" property="repairReviewTotalAmount"/>
    <result column="repair_replace_total_amount" jdbcType="DECIMAL" property="repairReplaceTotalAmount"/>
    <result column="repair_repair_total_amount" jdbcType="DECIMAL" property="repairRepairTotalAmount"/>
    <result column="repair_insurance_total_amount" jdbcType="DECIMAL" property="repairInsuranceTotalAmount"/>
    <result column="vehicle_replace_total_amount" jdbcType="DECIMAL" property="vehicleReplaceTotalAmount"/>
    <result column="vehicle_repair_total_amount" jdbcType="DECIMAL" property="vehicleRepairTotalAmount"/>
    <result column="vehicle_insurance_total_amount" jdbcType="DECIMAL" property="vehicleInsuranceTotalAmount"/>
    <result column="vehicle_manage_view_flag" jdbcType="DECIMAL" property="vehicleManageViewFlag"/>
    <result column="resurvey_flag" jdbcType="DECIMAL" property="resurveyFlag"/>
    <result column="resurvey_part" jdbcType="VARCHAR" property="resurveyPart"/>
    <result column="duty_situation" jdbcType="DECIMAL" property="dutySituation"/>
    <result column="recovery_amount" jdbcType="DECIMAL" property="recoveryAmount"/>
    <result column="insurance_amount" jdbcType="DECIMAL" property="insuranceAmount"/>
    <result column="acc_dep_amount" jdbcType="DECIMAL" property="accDepAmount"/>
    <result column="outage_loss_amount" jdbcType="DECIMAL" property="outageLossAmount"/>
    <result column="vehicle_loss_amount" jdbcType="DECIMAL" property="vehicleLossAmount"/>
    <result column="trailer_rescue_amount" jdbcType="DECIMAL" property="trailerRescueAmount"/>
    <result column="maintain_amount" jdbcType="DECIMAL" property="maintainAmount"/>
    <result column="reassignment_repair_org_id" jdbcType="VARCHAR" property="reassignmentRepairOrgId"/>
    <result column="reassignment_reasons" jdbcType="VARCHAR" property="reassignmentReasons"/>
    <result column="reassignment_reject_reasons" jdbcType="VARCHAR" property="reassignmentRejectReasons"/>
    <result column="verification_reject_reasons" jdbcType="VARCHAR" property="verificationRejectReasons"/>
    <result column="verification_reject_reasons_detail" jdbcType="VARCHAR" property="verificationRejectReasonsDetail"/>
    <result column="check_result_flag" jdbcType="DECIMAL" property="checkResultFlag"/>
    <result column="check_unqualified_reason" jdbcType="VARCHAR" property="checkUnqualifiedReason"/>
    <result column="vehicle_transfer_task_schedule" jdbcType="BIGINT" property="vehicleTransferTaskSchedule"/>
    <result column="insurance_quote_task_schedule" jdbcType="BIGINT" property="insuranceQuoteTaskSchedule"/>
    <result column="verification_loss_task_schedule" jdbcType="BIGINT" property="verificationLossTaskSchedule"/>
    <result column="reassignment_task_schedule" jdbcType="BIGINT" property="reassignmentTaskSchedule"/>
    <result column="vehicle_repair_task_schedule" jdbcType="BIGINT" property="vehicleRepairTaskSchedule"/>
    <result column="vehicle_check_task_schedule" jdbcType="BIGINT" property="vehicleCheckTaskSchedule"/>
    <result column="material_collection_task_schedule" jdbcType="INTEGER" property="materialCollectionTaskSchedule"/>
    <result column="loss_registration_task_schedule" jdbcType="INTEGER" property="lossRegistrationTaskSchedule"/>
    <result column="settlement_task_schedule" jdbcType="INTEGER" property="settlementTaskSchedule"/>
    <result column="insurance_pre_review_task_schedule" jdbcType="BIGINT" property="insurancePreReviewTaskSchedule"/>
    <result column="current_tache" jdbcType="BIGINT" property="currentTache"/>
    <result column="maintain_to_repair_flag" jdbcType="DECIMAL" property="maintainToRepairFlag"/>
    <result column="verification_loss_task_oper_id" jdbcType="BIGINT" property="verificationLossTaskOperId"/>
    <result column="examine_level" jdbcType="DECIMAL" property="examineLevel"/>
    <result column="verification_loss_check_time" jdbcType="TIMESTAMP" property="verificationLossCheckTime"/>
    <result column="verification_loss_check_id" jdbcType="BIGINT" property="verificationLossCheckId"/>
    <result column="over_time_reasons" jdbcType="VARCHAR" property="overTimeReasons"/>
    <result column="repair_total_amount_first" jdbcType="DECIMAL" property="repairTotalAmountFirst"/>
    <result column="nuclear_loss_reversion_flag" jdbcType="DECIMAL" property="nuclearLossReversionFlag"/>
    <result column="status" jdbcType="DECIMAL" property="status"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="tireNumber" jdbcType="BIGINT" property="tirenumber"/>
    <result column="associated_task_no" jdbcType="VARCHAR" property="associatedTaskNo"/>
    <result column="insurance_flag" jdbcType="INTEGER" property="insuranceFlag"/>
    <result column="advanced_audit_leve" jdbcType="BIT" property="advancedAuditLeve"/>
    <result column="already_incoming_amount" jdbcType="DECIMAL" property="alreadyIncomingAmount"/>
    <result column="already_pay_amount" jdbcType="DECIMAL" property="alreadyPayAmount"/>
    <result column="wait_pay_amount" jdbcType="DECIMAL" property="waitPayAmount"/>
    <result column="must_claim_amount" jdbcType="DECIMAL" property="mustClaimAmount"/>
    <result column="must_pay_amount" jdbcType="DECIMAL" property="mustPayAmount"/>
    <result column="transfer_flag" jdbcType="INTEGER" property="transferFlag"/>
    <result column="rational_indemnity_cnt" jdbcType="INTEGER" property="rationalIndemnityCnt"/>
    <result column="accident_day_time" jdbcType="TIMESTAMP" property="accidentDayTime"/>
    <result column="deduct_flag" jdbcType="INTEGER" property="deductFlag"/>
    <result column="origin" jdbcType="INTEGER" property="origin"/>
    <result column="renttype" jdbcType="INTEGER" property="renttype"/>
    <result column="fact_operate_tag" jdbcType="INTEGER" property="factOperateTag"/>
    <result column="take_user_name" jdbcType="VARCHAR" property="takeUserName"/>
    <result column="take_user_phone" jdbcType="VARCHAR" property="takeUserPhone"/>
    <result column="take_voucher" jdbcType="VARCHAR" property="takeVoucher"/>
    <result column="syn_take_time" jdbcType="TIMESTAMP" property="synTakeTime"/>
    <result column="close_reason" jdbcType="VARCHAR" property="closeReason"/>
    <result column="review_to_sel_fee_flag" jdbcType="INTEGER" property="reviewToSelFeeFlag"/>
    <result column="confirm_car_damage_type" jdbcType="INTEGER" property="confirmCarDamageType"/>
    <result column="accident_no" jdbcType="VARCHAR" property="accidentNo"/>
    <result column="insurance_pre_review_time" jdbcType="TIMESTAMP" property="insurancePreReviewTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId"/>
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId"/>
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName"/>
    <result column="claims_flag" jdbcType="BIGINT" property="claimsFlag"/>
    <result column="estimated_claim_amount" jdbcType="DECIMAL" property="estimatedClaimAmount"/>
    <result column="sync_claim_flag" jdbcType="INTEGER" property="syncClaimFlag"/>
    <result column="cust_pays_direct" jdbcType="INTEGER" property="custPaysDirect"/>
    <result column="cust_amount" jdbcType="DECIMAL" property="custAmount"/>
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate"/>
    <result column="declare_no" jdbcType="VARCHAR" property="declareNo"/>
    <result column="declare_status" jdbcType="INTEGER" property="declareStatus"/>
    <result column="declare_settlement_status" jdbcType="INTEGER" property="declareSettlementStatus"/>
    <result column="settlement_no" jdbcType="VARCHAR" property="settlementNo"/>
    <result column="settlement_status" jdbcType="INTEGER" property="settlementStatus"/>
    <result column="send_repair_time" jdbcType="TIMESTAMP" property="sendRepairTime"/>
    <result column="property_status" jdbcType="INTEGER" property="propertyStatus"/>
    <result column="product_line" jdbcType="INTEGER" property="productLine"/>
    <result column="sub_product_line" jdbcType="INTEGER" property="subProductLine"/>
    <result column="self_funded_amount" jdbcType="DECIMAL" property="selfFundedAmount"/>
    <result column="continue_days" jdbcType="VARCHAR" property="continueDays"/>
    <result column="is_proxy_operable" jdbcType="INTEGER" property="isProxyOperable"/>
  </resultMap>
  
  <resultMap id="maintenanceLossAuditPriceResultMap" type="com.extracme.evcard.mtc.bo.MaintenanceLossAuditPriceBO">
    <result column="id" jdbcType="VARCHAR" property="id"/>
    <result column="task_no" jdbcType="VARCHAR" property="taskNo"/>
    <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
    <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo"/>
    <result column="vehicle_model_info" jdbcType="VARCHAR" property="vehicleModelInfo"/>
    <result column="vin" jdbcType="VARCHAR" property="vin"/>
    <result column="insurance_company_name" jdbcType="VARCHAR" property="insuranceCompanyName"/>
    <result column="repair_type_id" jdbcType="VARCHAR" property="repairTypeId"/>
    <result column="repair_type_name" jdbcType="VARCHAR" property="repairTypeName"/>
    <result column="task_inflow_time" jdbcType="TIMESTAMP" property="taskInflowTime"/>
    <result column="vehicle_recive_time" jdbcType="TIMESTAMP" property="vehicleReciveTime"/>
    <result column="verification_loss_task_schedule" jdbcType="VARCHAR" property="verificationLossTaskSchedule"/>
    <result column="verification_loss_task_name" jdbcType="VARCHAR" property="verificationLossTaskName"/>
    <result column="repair_grade" jdbcType="VARCHAR" property="repairGrade"/>
    <result column="repair_depot_name" jdbcType="VARCHAR" property="repairDepotName"/>
    <result column="resurvey_flag" jdbcType="VARCHAR" property="resurveyFlag"/>
    <result column="examine_level" jdbcType="DECIMAL" property="examineLevel"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="cost_no" jdbcType="VARCHAR" property="costNo"/>
    <result column="advanced_audit_leve" jdbcType="VARCHAR" property="advancedAuditLeve"/>
    <result column="update_end_time" jdbcType="VARCHAR" property="updateEndTime"/>
    <result column="renttype" jdbcType="INTEGER" property="renttype"/>
    <result column="fact_operate_tag" jdbcType="INTEGER" property="factOperateTag"/>
    <result column="review_to_sel_fee_flag" jdbcType="INTEGER" property="reviewToSelFeeFlag"/>
  </resultMap>
  
  <resultMap id="ViewResultMap" type="com.extracme.evcard.mtc.bo.RepairTaskViewBO">
    <result column="id" jdbcType="BIGINT" property="id"/>
    <result column="task_no" jdbcType="VARCHAR" property="taskNo"/>
    <result column="advanced_audit_leve" jdbcType="VARCHAR" property="advancedAuditLeve"/>
    <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
    <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
    <result column="operate_org_id" jdbcType="VARCHAR" property="operateOrgId"/>
    <result column="operate_org_name" jdbcType="VARCHAR" property="operateOrgName"/>
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo"/>
    <result column="vehicle_model_seq" jdbcType="BIGINT" property="vehicleModelSeq"/>
    <result column="vehicle_model_info" jdbcType="VARCHAR" property="vehicleModelInfo"/>
    <result column="vin" jdbcType="VARCHAR" property="vin"/>
    <result column="insurance_company_name" jdbcType="VARCHAR" property="insuranceCompanyName"/>
    <result column="repair_type_id" jdbcType="DECIMAL" property="repairTypeId"/>
    <result column="repair_type_name" jdbcType="VARCHAR" property="repairTypeName"/>
    <result column="repair_grade" jdbcType="VARCHAR" property="repairGrade"/>
    <result column="repair_depot_org_id" jdbcType="VARCHAR" property="repairDepotOrgId"/>
    <result column="repair_depot_id" jdbcType="VARCHAR" property="repairDepotId"/>
    <result column="repair_depot_name" jdbcType="VARCHAR" property="repairDepotName"/>
    <result column="repair_depot_sap_code" jdbcType="VARCHAR" property="repairDepotSapCode"/>
    <result column="repair_depot_type" jdbcType="INTEGER" property="repairDepotType"/>
    <result column="task_inflow_time" jdbcType="TIMESTAMP" property="taskInflowTime"/>
    <result column="verification_loss_check_time" jdbcType="TIMESTAMP" property="verificationLossCheckTime"/>
    <result column="vehicle_recive_time" jdbcType="TIMESTAMP" property="vehicleReciveTime"/>
    <result column="tireNumber" jdbcType="VARCHAR" property="tireNumber"/>
    <result column="tire_unit_price" jdbcType="DECIMAL" property="tireUnitPrice"/>
    <result column="tire_brand" jdbcType="VARCHAR" property="tireBrand"/>
    <result column="vehicle_repair_time" jdbcType="TIMESTAMP" property="vehicleRepairTime"/>
    <result column="vehicle_check_time" jdbcType="TIMESTAMP" property="vehicleCheckTime"/>
    <result column="expected_repair_days" jdbcType="BIGINT" property="expectedRepairDays"/>
    <result column="expected_repair_complete" jdbcType="TIMESTAMP" property="expectedRepairComplete"/>
    <result column="accident_report_number" jdbcType="VARCHAR" property="accidentReportNumber"/>
    <result column="repair_flag" jdbcType="VARCHAR" property="repairFlag"/>
    <result column="terminal_id" jdbcType="VARCHAR" property="terminalId"/>
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage"/>
    <result column="mileage" jdbcType="VARCHAR" property="mileage"/>
    <result column="associated_order" jdbcType="VARCHAR" property="associatedOrder"/>
    <result column="order_type" jdbcType="INTEGER" property="orderType"/>
    <result column="relate_type" jdbcType="INTEGER" property="relateType"/>
    <result column="order_remark" jdbcType="VARCHAR" property="orderRemark"/>
    <result column="no_deductibles_flag" jdbcType="VARCHAR" property="noDeductiblesFlag"/>
    <result column="driver_name" jdbcType="VARCHAR" property="driverName"/>
    <result column="driver_tel" jdbcType="VARCHAR" property="driverTel"/>
    <result column="routing_inspection_name" jdbcType="VARCHAR" property="routingInspectionName"/>
    <result column="routing_inspection_tel" jdbcType="VARCHAR" property="routingInspectionTel"/>
    <result column="damaged_part_describe" jdbcType="VARCHAR" property="damagedPartDescribe"/>
    <result column="accident_describe" jdbcType="VARCHAR" property="accidentDescribe"/>
    <result column="trailer_flag" jdbcType="VARCHAR" property="trailerFlag"/>
    <result column="repair_review_total_amount" jdbcType="DECIMAL" property="repairReviewTotalAmount"/>
    <result column="repair_replace_total_amount" jdbcType="DECIMAL" property="repairReplaceTotalAmount"/>
    <result column="repair_repair_total_amount" jdbcType="DECIMAL" property="repairRepairTotalAmount"/>
    <result column="repair_insurance_total_amount" jdbcType="DECIMAL" property="repairInsuranceTotalAmount"/>
    <result column="vehicle_replace_total_amount" jdbcType="DECIMAL" property="vehicleReplaceTotalAmount"/>
    <result column="vehicle_repair_total_amount" jdbcType="DECIMAL" property="vehicleRepairTotalAmount"/>
    <result column="vehicle_insurance_total_amount" jdbcType="DECIMAL" property="vehicleInsuranceTotalAmount"/>
    <result column="vehicle_manage_view_flag" jdbcType="VARCHAR" property="vehicleManageViewFlag"/>
    <result column="vehicle_transfer_task_schedule" jdbcType="BIGINT" property="vehicleTransferTaskSchedule"/>
    <result column="insurance_quote_task_schedule" jdbcType="BIGINT" property="insuranceQuoteTaskSchedule"/>
    <result column="verification_loss_task_schedule" jdbcType="BIGINT" property="verificationLossTaskSchedule"/>
    <result column="reassignment_task_schedule" jdbcType="BIGINT" property="reassignmentTaskSchedule"/>
    <result column="vehicle_repair_task_schedule" jdbcType="BIGINT" property="vehicleRepairTaskSchedule"/>
    <result column="vehicle_check_task_schedule" jdbcType="BIGINT" property="vehicleCheckTaskSchedule"/>
    <result column="material_collection_task_schedule" jdbcType="BIGINT" property="materialCollectionTaskSchedule"/>
    <result column="loss_registration_task_schedule" jdbcType="BIGINT" property="lossRegistrationTaskSchedule"/>
    <result column="tire_task_schedule" jdbcType="INTEGER" property="tireTaskSchedule"/>
    <result column="insurance_pre_review_task_schedule" jdbcType="BIGINT" property="insurancePreReviewTaskSchedule"/>
    <result column="vehicle_manage_view_flag" jdbcType="VARCHAR" property="vehicleManageViewFlag"/>
    <result column="resurvey_flag" jdbcType="VARCHAR" property="resurveyFlag"/>
    <result column="resurvey_part" jdbcType="VARCHAR" property="resurveyPart"/>
    <result column="duty_situation" jdbcType="VARCHAR" property="dutySituation"/>
    <result column="recovery_amount" jdbcType="DECIMAL" property="recoveryAmount"/>
    <result column="insurance_amount" jdbcType="DECIMAL" property="insuranceAmount"/>
    <result column="acc_dep_amount" jdbcType="DECIMAL" property="accDepAmount"/>
    <result column="outage_loss_amount" jdbcType="DECIMAL" property="outageLossAmount"/>
    <result column="vehicle_loss_amount" jdbcType="DECIMAL" property="vehicleLossAmount"/>
    <result column="trailer_rescue_amount" jdbcType="DECIMAL" property="trailerRescueAmount"/>
    <result column="maintain_amount" jdbcType="DECIMAL" property="maintainAmount"/>
    <result column="loss_order_amount" jdbcType="DECIMAL" property="lossOrderAmount"/>
    <result column="maintain_to_repair_flag" jdbcType="VARCHAR" property="maintainToRepairFlag"/>
    <result column="verification_loss_task_oper_id" jdbcType="BIGINT" property="verificationLossTaskOperId"/>
    <result column="current_tache" jdbcType="BIGINT" property="currentTache"/>
    <result column="confirm_type" jdbcType="INTEGER" property="confirmType"/>
    <result column="examine_level" jdbcType="VARCHAR" property="examineLevel"/>
    <result column="check_result_flag" jdbcType="VARCHAR" property="checkResultFlag"/>
    <result column="vehicle_manage_view_flag" jdbcType="VARCHAR" property="vehicleManageViewFlag"/>
    <result column="nuclear_loss_reversion_flag" jdbcType="VARCHAR" property="nuclearLossReversionFlag"/>
    <result column="verification_reject_reasons" jdbcType="VARCHAR" property="verificationRejectReasons"/>
    <result column="verification_reject_reasons_detail" jdbcType="VARCHAR" property="verificationRejectReasonsDetail"/>
    <result column="over_time_reasons" jdbcType="VARCHAR" property="overTimeReasons"/>
    <result column="repair_total_amount_first" jdbcType="DECIMAL" property="repairTotalAmountFirst"/>
    <result column="verification_loss_check_id" jdbcType="BIGINT" property="verificationLossCheckId"/>
    <result column="check_unqualified_reason" jdbcType="VARCHAR" property="checkUnqualifiedReason"/>
    <result column="accident_day_time" jdbcType="VARCHAR" property="accidentDayTime"/>
    <result column="origin" jdbcType="INTEGER" property="origin"/>
    <result column="renttype" jdbcType="INTEGER" property="renttype"/>
    <result column="take_user_name" jdbcType="VARCHAR" property="takeUserName"/>
    <result column="verification_reject_level" jdbcType="VARCHAR" property="verificationRejectLevel"/>
    <result column="take_user_phone" jdbcType="VARCHAR" property="takeUserPhone"/>
    <result column="take_voucher" jdbcType="VARCHAR" property="takeVoucher"/>
    <result column="syn_take_time" jdbcType="TIMESTAMP" property="synTakeTime"/>
    <result column="close_reason" jdbcType="VARCHAR" property="closeReason"/>
    <result column="claims_flag" jdbcType="INTEGER" property="claimsFlag"/>
    <result column="estimated_claim_amount" jdbcType="DECIMAL" property="estimatedClaimAmount"/>
    <result column="car_damage_type" jdbcType="INTEGER" property="carDamageType"/>
    <result column="confirm_car_damage_type" jdbcType="INTEGER" property="confirmCarDamageType"/>
    <result column="review_to_sel_fee_flag" jdbcType="INTEGER" property="reviewToSelFeeFlag"/>
    <result column="accident_no" jdbcType="VARCHAR" property="accidentNo"/>
    <result column="insurance_pre_review_level" jdbcType="INTEGER" property="insurancePreReviewLevel"/>
    <result column="vehicle_scrape_value" jdbcType="DECIMAL" property="vehicleScrapeValue"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="fact_operate_tag" jdbcType="INTEGER" property="factOperateTag"/>
    <result column="pre_review_vehicle_scrape_value" jdbcType="DECIMAL" property="preReviewVehicleScrapeValue"/>
    <result column="pre_review_vehicle_scrape_time" jdbcType="TIMESTAMP" property="preReviewVehicleScrapeTime"/>
    <result column="cust_pays_direct" jdbcType="INTEGER" property="custPaysDirect"/>
    <result column="cust_amount" jdbcType="DECIMAL" property="custAmount"/>
    <result column="user_assumed_amount" jdbcType="DECIMAL" property="userAssumedAmount"/>
    <result column="not_user_assumed_amount" jdbcType="DECIMAL" property="notUserAssumedAmount"/>
    <result column="send_repair_time" jdbcType="TIMESTAMP" property="sendRepairTime"/>
    <result column="self_funded_amount" jdbcType="DECIMAL" property="selfFundedAmount"/>
    <result column="property_status" jdbcType="INTEGER" property="propertyStatus"/>
    <result column="product_line" jdbcType="INTEGER" property="productLine"/>
    <result column="sub_product_line" jdbcType="INTEGER" property="subProductLine"/>
    <collection property="drivingLicensePicture" column="taskNo" ofType="String">
      <result column="drivingLicense" jdbcType="VARCHAR"/>
    </collection>
    <collection property="policyPicture" javaType="java.util.List" ofType="String">
      <result column="policy" jdbcType="VARCHAR"/>
    </collection>
    <collection property="accidentPicture" javaType="java.util.List" ofType="String">
      <result column="accident" jdbcType="VARCHAR"/>
    </collection>
    <collection property="damageAPicture" javaType="java.util.List" ofType="String">
      <result column="damageA" jdbcType="VARCHAR"/>
    </collection>
    <collection property="damageBPicture" javaType="java.util.List" ofType="String">
      <result column="damageB" jdbcType="VARCHAR"/>
    </collection>
    <collection property="claimsPicture" javaType="java.util.List" ofType="String">
      <result column="claims" jdbcType="VARCHAR"/>
    </collection>
    <collection property="otherPicture" javaType="java.util.List" ofType="String">
      <result column="other" jdbcType="VARCHAR"/>
    </collection>
    <collection property="repairPicture" javaType="java.util.List" ofType="String">
      <result column="repair" jdbcType="VARCHAR"/>
    </collection>
    <collection property="damagedPartPicture" javaType="java.util.List" ofType="String">
      <result column="damagedPart" jdbcType="VARCHAR"/>
    </collection>
    <collection property="checkVideo" javaType="java.util.List" ofType="String">
      <result column="checkVideo"  jdbcType="VARCHAR"/>
    </collection>
    <collection property="afterPic" javaType="java.util.List" ofType="String">
      <result column="afterPic" jdbcType="VARCHAR"/>
    </collection>
    <collection property="accidentLiabilityConfirmationPicture" javaType="java.util.List" ofType="String">
      <result column="accidentLiabilityConfirmationPicture" jdbcType="VARCHAR"/>
    </collection>
    <collection property="insuranceCompanyLossOrderPicture" javaType="java.util.List" ofType="String">
      <result column="insuranceCompanyLossOrderPicture" jdbcType="VARCHAR"/>
    </collection>
    <collection property="ourDriverLicensePicture" javaType="java.util.List" ofType="String">
      <result column="ourDriverLicensePicture" jdbcType="VARCHAR"/>
    </collection>
    <collection property="custPicture" javaType="java.util.List" ofType="String">
      <result column="custPicture" jdbcType="VARCHAR"/>
    </collection>
    <collection property="damagedPartVideo" javaType="java.util.List" ofType="String">
      <result column="damagedPartV"  jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
    
    <resultMap type="com.extracme.evcard.mtc.bo.ViewRepairItemDetailBO" id="RepairItemDetailResultMap">
            <result column="repairId" property="id"  jdbcType="VARCHAR"/>
            <result column="repair_task_no" jdbcType="VARCHAR" property="taskNo" />      
            <result column="price_programme" property="priceProgramme"  jdbcType="VARCHAR"/>
            <result column="grouping_id" property="groupingId"  jdbcType="VARCHAR"/>
            <result column="grouping_name" property="groupingName"  jdbcType="VARCHAR"/> 
            <result column="repair_name" property="repairName"  jdbcType="VARCHAR"/> 
            <result column="repair_amount" property="repairAmount"  jdbcType="VARCHAR"/>
            <result column="view_amount" property="viewAmount"  jdbcType="VARCHAR"/>
            <result column="remark" property="remark"  jdbcType="VARCHAR"/>   
    </resultMap>
    
   <resultMap type="com.extracme.evcard.mtc.bo.ViewReplaceItemDetailBO" id="ReplaceItemDetailResultMap">
            <result column="replaceId" property="id"  jdbcType="VARCHAR"/>
            <result column="replace_task_no" jdbcType="VARCHAR" property="taskNo" />     
            <result column="replace_price_programme" property="priceProgramme"  jdbcType="VARCHAR"/>
            <result column="replace_grouping_id" property="groupingId"  jdbcType="VARCHAR"/>
            <result column="replace_grouping_name" property="groupingName"  jdbcType="VARCHAR"/> 
            <result column="part_name" property="partName"  jdbcType="VARCHAR"/> 
            <result column="part_number" property="partNumber"  jdbcType="VARCHAR"/>
            <result column="unit_price" property="unitPrice"  jdbcType="VARCHAR"/>
            <result column="insurance_quote_amount" property="insuranceQuoteAmount"  jdbcType="VARCHAR"/>
            <result column="replace_view_amount" property="viewAmount"  jdbcType="VARCHAR"/>
            <result column="replace_remark" property="remark"  jdbcType="VARCHAR"/>  
            <result column="original_factory_part_no" property="originalFactoryPartNo"  jdbcType="VARCHAR"/>    
    </resultMap>
    
   
        <select id="getViewReplaceItemDetailList" resultMap="RepairItemDetailResultMap">
    SELECT  
	 c.task_no AS repair_task_no,
	 c.id AS repairId,
	 c.price_programme,
	 c.grouping_id,
	 c.grouping_name,
	 c.repair_name,
	 c.repair_amount,
	 c.view_amount,
	 c.remark
	 from ${mtcSchema}.mtc_repair_item_detail c 
	 WHERE c.task_no = #{taskNo}  and c.status = 1   
    </select>
    
    
    
    <select id="getReplaceItemDetailList" resultMap="ReplaceItemDetailResultMap">
	SELECT
		d.task_no AS replace_task_no,
		d.id AS replaceId,
		d.price_programme AS replace_price_programme,
		d.grouping_id AS replace_grouping_id,
		d.grouping_name AS replace_grouping_name,
		d.part_name,
		d.part_number,
		d.unit_price,
		d.insurance_quote_amount,
		d.view_amount AS replace_view_amount,
		d.remark AS replace_remark,
		d.original_factory_part_no
	FROM
		${mtcSchema}.mtc_replace_item_detail d
	WHERE
		d.task_no = #{taskNo}
	AND d. STATUS = 1
    </select>
  <!--数据列-->
  <sql id="Base_Column_List">
    id,
    task_no,
    task_type,
    org_id,
    org_name,
    operate_org_id,
    operate_org_name,
    vehicle_no,
    vehicle_model_seq,
    vehicle_model_info,
    vin,
    insurance_company_name,
    repair_type_id,
    repair_type_name,
    repair_grade,
    repair_depot_id,
    repair_depot_name,
    repair_depot_org_id,
    repair_depot_sap_code,
    repair_depot_type,
    task_inflow_time,
    vehicle_recive_time,
    vehicle_check_time,
    vehicle_repair_time,
    reassignment_pass_time,
    settle_closing_time,
    expected_repair_days,
    expected_repair_complete,
    accident_report_number,
    repair_flag,
    terminal_id,
    total_mileage,
    associated_order,
    order_type,
    relate_type,
    order_remark,
    no_deductibles_flag,
    driver_name,
    driver_tel,
    routing_inspection_name,
    routing_inspection_tel,
    damaged_part_describe,
    accident_describe,
    trailer_flag,
    repair_review_total_amount,
    repair_replace_total_amount,
    repair_repair_total_amount,
    repair_insurance_total_amount,
    vehicle_replace_total_amount,
    vehicle_repair_total_amount,
    vehicle_insurance_total_amount,
    vehicle_manage_view_flag,
    resurvey_flag,
    resurvey_part,
    duty_situation,
    recovery_amount,
    insurance_amount,
    acc_dep_amount,
    outage_loss_amount,
    vehicle_loss_amount,
    trailer_rescue_amount,
    maintain_amount,
    reassignment_repair_org_id,
    reassignment_reasons,
    reassignment_reject_reasons,
    verification_reject_reasons,
    verification_reject_reasons_detail,
    check_result_flag,
    check_unqualified_reason,
    vehicle_transfer_task_schedule,
    insurance_quote_task_schedule,
    verification_loss_task_schedule,
    reassignment_task_schedule,
    vehicle_repair_task_schedule,
    vehicle_check_task_schedule,
    material_collection_task_schedule,
    loss_registration_task_schedule,
    settlement_task_schedule,
    insurance_pre_review_task_schedule,
    current_tache,
    maintain_to_repair_flag,
    verification_loss_task_oper_id,
    examine_level,
    verification_loss_check_time,
    verification_loss_check_id,
    over_time_reasons,
    repair_total_amount_first,
    nuclear_loss_reversion_flag,
    status,
    remark,
    tireNumber,
    associated_task_no,
    insurance_flag,
    advanced_audit_leve,
    already_incoming_amount,
    already_pay_amount,
    wait_pay_amount,
    must_claim_amount,
    must_pay_amount,
    transfer_flag,
    rational_indemnity_cnt,
    accident_day_time,
    deduct_flag,
    origin,
    renttype,
    fact_operate_tag,
    take_user_name,
    take_user_phone,
    take_voucher,
    syn_take_time,
    close_reason,
    create_time,
    create_oper_id,
    create_oper_name,
    update_time,
    update_oper_id,
    update_oper_name,
    vehicle_repair_cost_amount,
    repair_settle_amount,
    repair_fax_settle_amount,
    claims_flag,
    estimated_claim_amount,
    review_to_sel_fee_flag,
    confirm_car_damage_type,
    accident_no,
    insurance_pre_review_time,
    sync_claim_flag,
    cust_pays_direct,
    cust_amount,
    user_assumed_amount,
    not_user_assumed_amount,
    sync_claim_flag,
    tax_rate,
    declare_no,
    declare_status,
    declare_settlement_status,
    settlement_no,
    settlement_status,
    send_repair_time,
    property_status,
    product_line,
    sub_product_line,
    self_funded_amount,
    continue_days
  </sql>
    
    <!-- 保存数据 -->
    <insert id="save" parameterType="com.extracme.evcard.mtc.model.RepairTask" useGeneratedKeys="true" keyProperty="id">
        insert into ${mtcSchema}.mtc_repair_task (
            <if test="id != null">
             id,
            </if>
            task_no,
            task_type,
            org_id,
            org_name,
            vehicle_no,
            vehicle_model_seq,
            vehicle_model_info,
            vin,
            insurance_company_name,
            repair_type_id,
            repair_type_name,
            repair_grade,
            repair_depot_id,
            repair_depot_name,
            repair_depot_org_id,
            task_inflow_time,
            vehicle_recive_time,
            vehicle_check_time,
            vehicle_repair_time,
            reassignment_pass_time,
            settle_closing_time,
            expected_repair_days,
            expected_repair_complete,
            accident_report_number,
            repair_flag,
            terminal_id,
            total_mileage,
            associated_order,
            no_deductibles_flag,
            driver_name,
            driver_tel,
            routing_inspection_name,
            routing_inspection_tel,
            damaged_part_describe,
            accident_describe,
            trailer_flag,
            repair_replace_total_amount,
            repair_repair_total_amount,
            repair_insurance_total_amount,
            vehicle_replace_total_amount,
            vehicle_repair_total_amount,
            vehicle_insurance_total_amount,
            vehicle_manage_view_flag,
            resurvey_flag,
            resurvey_part,
            duty_situation,
            recovery_amount,
            insurance_amount,
            acc_dep_amount,
            outage_loss_amount,
            vehicle_loss_amount,
            trailer_rescue_amount,
            maintain_amount,
            reassignment_repair_org_id,
            reassignment_reasons,
            reassignment_reject_reasons,
            verification_reject_reasons,
            verification_reject_reasons_detail,
            check_result_flag,
            check_unqualified_reason,
            vehicle_transfer_task_schedule,
            insurance_quote_task_schedule,
            verification_loss_task_schedule,
            reassignment_task_schedule,
            vehicle_repair_task_schedule,
            vehicle_check_task_schedule,
            material_collection_task_schedule,
            loss_registration_task_schedule,
            settlement_task_schedule,
            current_tache,
            maintain_to_repair_flag,
            verification_loss_task_oper_id,
            examine_level,
            verification_loss_check_time,
            verification_loss_check_id,
            over_time_reasons,
            repair_total_amount_first,
            nuclear_loss_reversion_flag,
            status,
            remark,
            tireNumber,
            associated_task_no,
            insurance_flag,
            advanced_audit_leve,
            already_incoming_amount,
            already_pay_amount,
            wait_pay_amount,
            must_claim_amount,
            must_pay_amount,
            transfer_flag,
            rational_indemnity_cnt,
            accident_day_time,
            deduct_flag,
            create_time,
            create_oper_id,
            create_oper_name,
            update_time,
            update_oper_id,
            update_oper_name
        ) values (
            <if test="id != null">
             #{id,jdbcType=BIGINT},
            </if>
            #{taskNo,jdbcType=VARCHAR},
            #{taskType,jdbcType=DECIMAL},
            #{orgId,jdbcType=VARCHAR},
            #{orgName,jdbcType=VARCHAR},
            #{vehicleNo,jdbcType=VARCHAR},
            #{vehicleModelSeq,jdbcType=BIGINT},
            #{vehicleModelInfo,jdbcType=VARCHAR},
            #{vin,jdbcType=VARCHAR},
            #{insuranceCompanyName,jdbcType=VARCHAR},
            #{repairTypeId,jdbcType=DECIMAL},
            #{repairTypeName,jdbcType=VARCHAR},
            #{repairGrade,jdbcType=VARCHAR},
            #{repairDepotId,jdbcType=VARCHAR},
            #{repairDepotName,jdbcType=VARCHAR},
            #{repairDepotOrgId,jdbcType=VARCHAR},
            #{taskInflowTime,jdbcType=TIMESTAMP},
            #{vehicleReciveTime,jdbcType=TIMESTAMP},
            #{vehicleCheckTime,jdbcType=TIMESTAMP},
            #{vehicleRepairTime,jdbcType=TIMESTAMP},
            #{reassignmentPassTime,jdbcType=TIMESTAMP},
            #{settleClosingTime,jdbcType=TIMESTAMP},
            #{expectedRepairDays,jdbcType=BIGINT},
            #{expectedRepairComplete,jdbcType=TIMESTAMP},
            #{accidentReportNumber,jdbcType=VARCHAR},
            #{repairFlag,jdbcType=DECIMAL},
            #{terminalId,jdbcType=VARCHAR},
            #{totalMileage,jdbcType=DECIMAL},
            #{associatedOrder,jdbcType=VARCHAR},
            #{noDeductiblesFlag,jdbcType=DECIMAL},
            #{driverName,jdbcType=VARCHAR},
            #{driverTel,jdbcType=VARCHAR},
            #{routingInspectionName,jdbcType=VARCHAR},
            #{routingInspectionTel,jdbcType=VARCHAR},
            #{damagedPartDescribe,jdbcType=VARCHAR},
            #{accidentDescribe,jdbcType=VARCHAR},
            #{trailerFlag,jdbcType=DECIMAL},
            #{repairReplaceTotalAmount,jdbcType=DECIMAL},
            #{repairRepairTotalAmount,jdbcType=DECIMAL},
            #{repairInsuranceTotalAmount,jdbcType=DECIMAL},
            #{vehicleReplaceTotalAmount,jdbcType=DECIMAL},
            #{vehicleRepairTotalAmount,jdbcType=DECIMAL},
            #{vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
            #{vehicleManageViewFlag,jdbcType=DECIMAL},
            #{resurveyFlag,jdbcType=DECIMAL},
            #{resurveyPart,jdbcType=VARCHAR},
            #{dutySituation,jdbcType=DECIMAL},
            #{recoveryAmount,jdbcType=DECIMAL},
            #{insuranceAmount,jdbcType=DECIMAL},
            #{accDepAmount,jdbcType=DECIMAL},
            #{outageLossAmount,jdbcType=DECIMAL},
            #{vehicleLossAmount,jdbcType=DECIMAL},
            #{trailerRescueAmount,jdbcType=DECIMAL},
            #{maintainAmount,jdbcType=DECIMAL},
            #{reassignmentRepairOrgId,jdbcType=VARCHAR},
            #{reassignmentReasons,jdbcType=VARCHAR},
            #{reassignmentRejectReasons,jdbcType=VARCHAR},
            #{verificationRejectReasons,jdbcType=VARCHAR},
            #{verificationRejectReasonsDetail,jdbcType=VARCHAR},
            #{checkResultFlag,jdbcType=DECIMAL},
            #{checkUnqualifiedReason,jdbcType=VARCHAR},
            #{vehicleTransferTaskSchedule,jdbcType=BIGINT},
            #{insuranceQuoteTaskSchedule,jdbcType=BIGINT},
            #{verificationLossTaskSchedule,jdbcType=BIGINT},
            #{reassignmentTaskSchedule,jdbcType=BIGINT},
            #{vehicleRepairTaskSchedule,jdbcType=BIGINT},
            #{vehicleCheckTaskSchedule,jdbcType=BIGINT},
            #{materialCollectionTaskSchedule,jdbcType=INTEGER},
            #{lossRegistrationTaskSchedule,jdbcType=INTEGER},
            #{settlementTaskSchedule,jdbcType=INTEGER},
            #{currentTache,jdbcType=BIGINT},
            #{maintainToRepairFlag,jdbcType=DECIMAL},
            #{verificationLossTaskOperId,jdbcType=BIGINT},
            #{examineLevel,jdbcType=DECIMAL},
            #{verificationLossCheckTime,jdbcType=TIMESTAMP},
            #{verificationLossCheckId,jdbcType=BIGINT},
            #{overTimeReasons,jdbcType=VARCHAR},
            #{repairTotalAmountFirst,jdbcType=DECIMAL},
            #{nuclearLossReversionFlag,jdbcType=DECIMAL},
            #{status,jdbcType=DECIMAL},
            #{remark,jdbcType=VARCHAR},
            #{tirenumber,jdbcType=BIGINT},
            #{associatedTaskNo,jdbcType=VARCHAR},
            #{insuranceFlag,jdbcType=INTEGER},
            #{advancedAuditLeve,jdbcType=BIT},
            #{alreadyIncomingAmount,jdbcType=DECIMAL},
            #{alreadyPayAmount,jdbcType=DECIMAL},
            #{waitPayAmount,jdbcType=DECIMAL},
            #{mustClaimAmount,jdbcType=DECIMAL},
            #{mustPayAmount,jdbcType=DECIMAL},
            #{transferFlag,jdbcType=INTEGER},
            #{rationalIndemnityCnt,jdbcType=INTEGER},
            #{accidentDayTime,jdbcType=TIMESTAMP},
            #{deductFlag,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP},
            #{createOperId,jdbcType=BIGINT},
            #{createOperName,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{updateOperId,jdbcType=BIGINT},
            #{updateOperName,jdbcType=VARCHAR}
        )
    </insert>
    
    <!-- 根据主键取得数据 -->
    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
         from ${mtcSchema}.mtc_repair_task where id = #{id}
    </select>

  <select id="selectListByTaskNoList" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from ${mtcSchema}.mtc_repair_task
    WHERE
      status != 0
      and task_no in
    <foreach collection="taskNoList" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
    
    <!--分页获取获取所有数据，分页使用(实际项目中需要自己改造，自己需要几个条件则添加几个条件)-->
    <select id="selectAllPage" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select 
        <include refid="Base_Column_List" />
        from ${mtcSchema}.mtc_repair_task where status = 1
     </select>
  
    <!-- 根据主键删除数据 -->
    <delete id="deleteById" parameterType="long">
        delete from ${mtcSchema}.mtc_repair_task where id = #{id,jdbcType=BIGINT}
    </delete>

  <!-- 更新数据 -->
  <update id="updateByIdSelective" parameterType="com.extracme.evcard.mtc.model.RepairTask">
    update ${mtcSchema}.mtc_repair_task
    <set>
      <if test="taskNo != null">
        task_no = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=DECIMAL},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelSeq != null">
        vehicle_model_seq = #{vehicleModelSeq,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelInfo != null">
        vehicle_model_info = #{vehicleModelInfo,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="insuranceCompanyName != null">
        insurance_company_name = #{insuranceCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="repairTypeId != null">
        repair_type_id = #{repairTypeId,jdbcType=DECIMAL},
      </if>
      <if test="repairTypeName != null">
        repair_type_name = #{repairTypeName,jdbcType=VARCHAR},
      </if>
      <if test="repairGrade != null">
        repair_grade = #{repairGrade,jdbcType=VARCHAR},
      </if>
      <if test="repairDepotId != null">
        repair_depot_id = #{repairDepotId,jdbcType=VARCHAR},
      </if>
      <if test="repairDepotName != null">
        repair_depot_name = #{repairDepotName,jdbcType=VARCHAR},
      </if>
      <if test="repairDepotOrgId != null">
        repair_depot_org_id = #{repairDepotOrgId,jdbcType=VARCHAR},
      </if>
      <if test="taskInflowTime != null">
        task_inflow_time = #{taskInflowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleReciveTime != null">
        vehicle_recive_time = #{vehicleReciveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleCheckTime != null">
        vehicle_check_time = #{vehicleCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleRepairTime != null">
        vehicle_repair_time = #{vehicleRepairTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reassignmentPassTime != null">
        reassignment_pass_time = #{reassignmentPassTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleClosingTime != null">
        settle_closing_time = #{settleClosingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedRepairDays != null">
        expected_repair_days = #{expectedRepairDays,jdbcType=BIGINT},
      </if>
      <if test="expectedRepairComplete != null">
        expected_repair_complete = #{expectedRepairComplete,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentReportNumber != null">
        accident_report_number = #{accidentReportNumber,jdbcType=VARCHAR},
      </if>
      <if test="repairFlag != null">
        repair_flag = #{repairFlag,jdbcType=DECIMAL},
      </if>
      <if test="terminalId != null">
        terminal_id = #{terminalId,jdbcType=VARCHAR},
      </if>
      <if test="totalMileage != null">
        total_mileage = #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="associatedOrder != null">
        associated_order = #{associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType, jdbcType=INTEGER},
      </if>
      <if test="relateType != null">
        relate_type = #{relateType, jdbcType=INTEGER},
      </if>
      <if test="orderRemark != null">
        order_remark = #{orderRemark, jdbcType=VARCHAR},
      </if>
      <if test="noDeductiblesFlag != null">
        no_deductibles_flag = #{noDeductiblesFlag,jdbcType=DECIMAL},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverTel != null">
        driver_tel = #{driverTel,jdbcType=VARCHAR},
      </if>
      <if test="routingInspectionName != null">
        routing_inspection_name = #{routingInspectionName,jdbcType=VARCHAR},
      </if>
      <if test="routingInspectionTel != null">
        routing_inspection_tel = #{routingInspectionTel,jdbcType=VARCHAR},
      </if>
      <if test="damagedPartDescribe != null">
        damaged_part_describe = #{damagedPartDescribe,jdbcType=VARCHAR},
      </if>
      <if test="accidentDescribe != null">
        accident_describe = #{accidentDescribe,jdbcType=VARCHAR},
      </if>
      <if test="trailerFlag != null">
        trailer_flag = #{trailerFlag,jdbcType=DECIMAL},
      </if>
      <if test="repairReviewTotalAmount != null">
        repair_review_total_amount = #{repairReviewTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="repairReplaceTotalAmount != null">
        repair_replace_total_amount = #{repairReplaceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="repairRepairTotalAmount != null">
        repair_repair_total_amount = #{repairRepairTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="repairInsuranceTotalAmount != null">
        repair_insurance_total_amount = #{repairInsuranceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleReplaceTotalAmount != null">
        vehicle_replace_total_amount = #{vehicleReplaceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleRepairTotalAmount != null">
        vehicle_repair_total_amount = #{vehicleRepairTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleInsuranceTotalAmount != null">
        vehicle_insurance_total_amount = #{vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleManageViewFlag != null">
        vehicle_manage_view_flag = #{vehicleManageViewFlag,jdbcType=DECIMAL},
      </if>
      <if test="resurveyFlag != null">
        resurvey_flag = #{resurveyFlag,jdbcType=DECIMAL},
      </if>
      <if test="resurveyPart != null">
        resurvey_part = #{resurveyPart,jdbcType=VARCHAR},
      </if>
      <if test="dutySituation != null">
        duty_situation = #{dutySituation,jdbcType=DECIMAL},
      </if>
      <if test="recoveryAmount != null">
        recovery_amount = #{recoveryAmount,jdbcType=DECIMAL},
      </if>
      <if test="insuranceAmount != null">
        insurance_amount = #{insuranceAmount,jdbcType=DECIMAL},
      </if>
      <if test="accDepAmount != null">
        acc_dep_amount = #{accDepAmount,jdbcType=DECIMAL},
      </if>
      <if test="outageLossAmount != null">
        outage_loss_amount = #{outageLossAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleLossAmount != null">
        vehicle_loss_amount = #{vehicleLossAmount,jdbcType=DECIMAL},
      </if>
      <if test="trailerRescueAmount != null">
        trailer_rescue_amount = #{trailerRescueAmount,jdbcType=DECIMAL},
      </if>
      <if test="maintainAmount != null">
        maintain_amount = #{maintainAmount,jdbcType=DECIMAL},
      </if>
      <if test="reassignmentRepairOrgId != null">
        reassignment_repair_org_id = #{reassignmentRepairOrgId,jdbcType=VARCHAR},
      </if>
      <if test="reassignmentReasons != null">
        reassignment_reasons = #{reassignmentReasons,jdbcType=VARCHAR},
      </if>
      <if test="reassignmentRejectReasons != null">
        reassignment_reject_reasons = #{reassignmentRejectReasons,jdbcType=VARCHAR},
      </if>
      <if test="verificationRejectReasons != null">
        verification_reject_reasons = #{verificationRejectReasons,jdbcType=VARCHAR},
      </if>
      <if test="verificationRejectReasonsDetail != null">
        verification_reject_reasons_detail = #{verificationRejectReasonsDetail,jdbcType=VARCHAR},
      </if>
      <if test="checkResultFlag != null">
        check_result_flag = #{checkResultFlag,jdbcType=DECIMAL},
      </if>
      <if test="checkUnqualifiedReason != null">
        check_unqualified_reason = #{checkUnqualifiedReason,jdbcType=VARCHAR},
      </if>
      <if test="vehicleTransferTaskSchedule != null">
        vehicle_transfer_task_schedule = #{vehicleTransferTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="insuranceQuoteTaskSchedule != null">
        insurance_quote_task_schedule = #{insuranceQuoteTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="verificationLossTaskSchedule != null">
        verification_loss_task_schedule = #{verificationLossTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="reassignmentTaskSchedule != null">
        reassignment_task_schedule = #{reassignmentTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="vehicleRepairTaskSchedule != null">
        vehicle_repair_task_schedule = #{vehicleRepairTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="vehicleCheckTaskSchedule != null">
        vehicle_check_task_schedule = #{vehicleCheckTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="materialCollectionTaskSchedule != null">
        material_collection_task_schedule = #{materialCollectionTaskSchedule,jdbcType=INTEGER},
      </if>
      <if test="lossRegistrationTaskSchedule != null">
        loss_registration_task_schedule = #{lossRegistrationTaskSchedule,jdbcType=INTEGER},
      </if>
      <if test="settlementTaskSchedule != null">
        settlement_task_schedule = #{settlementTaskSchedule,jdbcType=INTEGER},
      </if>
      <if test="currentTache != null">
        current_tache = #{currentTache,jdbcType=BIGINT},
      </if>
      <if test="maintainToRepairFlag != null">
        maintain_to_repair_flag = #{maintainToRepairFlag,jdbcType=DECIMAL},
      </if>
      <if test="verificationLossTaskOperId != null">
        verification_loss_task_oper_id = #{verificationLossTaskOperId,jdbcType=BIGINT},
      </if>
      <if test="examineLevel != null">
        examine_level = #{examineLevel,jdbcType=DECIMAL},
      </if>
      <if test="verificationLossCheckTime != null">
        verification_loss_check_time = #{verificationLossCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verificationLossCheckId != null">
        verification_loss_check_id = #{verificationLossCheckId,jdbcType=BIGINT},
      </if>
      <if test="overTimeReasons != null">
        over_time_reasons = #{overTimeReasons,jdbcType=VARCHAR},
      </if>
      <if test="repairTotalAmountFirst != null">
        repair_total_amount_first = #{repairTotalAmountFirst,jdbcType=DECIMAL},
      </if>
      <if test="nuclearLossReversionFlag != null">
        nuclear_loss_reversion_flag = #{nuclearLossReversionFlag,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tirenumber != null">
        tireNumber = #{tirenumber,jdbcType=BIGINT},
      </if>
      <if test="associatedTaskNo != null">
        associated_task_no = #{associatedTaskNo,jdbcType=VARCHAR},
      </if>
      <if test="insuranceFlag != null">
        insurance_flag = #{insuranceFlag,jdbcType=INTEGER},
      </if>
      <if test="advancedAuditLeve != null">
        advanced_audit_leve = #{advancedAuditLeve,jdbcType=BIT},
      </if>
      <if test="alreadyIncomingAmount != null">
        already_incoming_amount = #{alreadyIncomingAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadyPayAmount != null">
        already_pay_amount = #{alreadyPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="waitPayAmount != null">
        wait_pay_amount = #{waitPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="mustClaimAmount != null">
        must_claim_amount = #{mustClaimAmount,jdbcType=DECIMAL},
      </if>
      <if test="mustPayAmount != null">
        must_pay_amount = #{mustPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="rationalIndemnityCnt != null">
        rational_indemnity_cnt = #{rationalIndemnityCnt,jdbcType=INTEGER},
      </if>
      <if test="deductFlag != null">
        deduct_flag = #{deductFlag,jdbcType=INTEGER},
      </if>
      <if test="entityMap.synTakeTime != null">
        syn_take_time = #{entityMap.synTakeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.closeReason != null">
        close_reason = #{entityMap.closeReason,jdbcType=VARCHAR},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="confirmCarDamageType != null">
        confirm_car_damage_type = #{confirmCarDamageType, jdbcType=INTEGER},
      </if>
      <if test="estimatedClaimAmount != null">
        estimated_claim_amount = #{estimatedClaimAmount, jdbcType=DECIMAL},
      </if>
      <if test="custAmount != null">
        cust_amount = #{custAmount, jdbcType=DECIMAL},
      </if>
      <if test="userAssumedAmount != null">
        user_assumed_amount = #{userAssumedAmount, jdbcType=DECIMAL},
      </if>
      <if test="notUserAssumedAmount != null">
        not_user_assumed_amount = #{notUserAssumedAmount, jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate, jdbcType=VARCHAR},
      </if>
    </set>
    where task_no = #{taskNo,jdbcType=VARCHAR}
  </update>
    
    <!-- 批量插入 -->
     <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${mtcSchema}.mtc_repair_task (
               id,
               task_no,
               task_type,
               org_id,
               org_name,
               vehicle_no,
               vehicle_model_seq,
               vehicle_model_info,
               vin,
               insurance_company_name,
               repair_type_id,
               repair_type_name,
               repair_grade,
               repair_depot_id,
               repair_depot_name,
               repair_depot_org_id,
               task_inflow_time,
               vehicle_recive_time,
               vehicle_check_time,
               vehicle_repair_time,
               reassignment_pass_time,
               settle_closing_time,
               expected_repair_days,
               expected_repair_complete,
               accident_report_number,
               repair_flag,
               terminal_id,
               total_mileage,
               associated_order,
               no_deductibles_flag,
               driver_name,
               driver_tel,
               routing_inspection_name,
               routing_inspection_tel,
               damaged_part_describe,
               accident_describe,
               trailer_flag,
               repair_replace_total_amount,
               repair_repair_total_amount,
               repair_insurance_total_amount,
               vehicle_replace_total_amount,
               vehicle_repair_total_amount,
               vehicle_insurance_total_amount,
               vehicle_manage_view_flag,
               resurvey_flag,
               resurvey_part,
               duty_situation,
               recovery_amount,
               insurance_amount,
               acc_dep_amount,
               outage_loss_amount,
               vehicle_loss_amount,
               trailer_rescue_amount,
               maintain_amount,
               reassignment_repair_org_id,
               reassignment_reasons,
               reassignment_reject_reasons,
               verification_reject_reasons,
               verification_reject_reasons_detail,
               check_result_flag,
               check_unqualified_reason,
               vehicle_transfer_task_schedule,
               insurance_quote_task_schedule,
               verification_loss_task_schedule,
               reassignment_task_schedule,
               vehicle_repair_task_schedule,
               vehicle_check_task_schedule,
               material_collection_task_schedule,
               loss_registration_task_schedule,
               settlement_task_schedule,
               current_tache,
               maintain_to_repair_flag,
               verification_loss_task_oper_id,
               examine_level,
               verification_loss_check_time,
               verification_loss_check_id,
               over_time_reasons,
               repair_total_amount_first,
               nuclear_loss_reversion_flag,
               status,
               remark,
               tireNumber,
               associated_task_no,
               insurance_flag,
               advanced_audit_leve,
               already_incoming_amount,
               already_pay_amount,
               wait_pay_amount,
               must_claim_amount,
               must_pay_amount,
               create_time,
               create_oper_id,
               create_oper_name,
               update_time,
               update_oper_id,
               update_oper_name
        ) 
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT 
                  #{item.id,jdbcType=BIGINT},
                  #{item.taskNo,jdbcType=VARCHAR},
                  #{item.taskType,jdbcType=DECIMAL},
                  #{item.orgId,jdbcType=VARCHAR},
                  #{item.orgName,jdbcType=VARCHAR},
                  #{item.vehicleNo,jdbcType=VARCHAR},
                  #{item.vehicleModelSeq,jdbcType=BIGINT},
                  #{item.vehicleModelInfo,jdbcType=VARCHAR},
                  #{item.vin,jdbcType=VARCHAR},
                  #{item.insuranceCompanyName,jdbcType=VARCHAR},
                  #{item.repairTypeId,jdbcType=DECIMAL},
                  #{item.repairTypeName,jdbcType=VARCHAR},
                  #{item.repairGrade,jdbcType=VARCHAR},
                  #{item.repairDepotId,jdbcType=VARCHAR},
                  #{item.repairDepotName,jdbcType=VARCHAR},
                  #{item.repairDepotOrgId,jdbcType=VARCHAR},
                  #{item.taskInflowTime,jdbcType=TIMESTAMP},
                  #{item.vehicleReciveTime,jdbcType=TIMESTAMP},
                  #{item.vehicleCheckTime,jdbcType=TIMESTAMP},
                  #{item.vehicleRepairTime,jdbcType=TIMESTAMP},
                  #{item.reassignmentPassTime,jdbcType=TIMESTAMP},
                  #{item.expectedRepairDays,jdbcType=BIGINT},
                  #{item.expectedRepairComplete,jdbcType=TIMESTAMP},
                  #{item.accidentReportNumber,jdbcType=VARCHAR},
                  #{item.repairFlag,jdbcType=DECIMAL},
                  #{item.terminalId,jdbcType=VARCHAR},
                  #{item.totalMileage,jdbcType=DECIMAL},
                  #{item.associatedOrder,jdbcType=VARCHAR},
                  #{item.noDeductiblesFlag,jdbcType=DECIMAL},
                  #{item.driverName,jdbcType=VARCHAR},
                  #{item.driverTel,jdbcType=VARCHAR},
                  #{item.routingInspectionName,jdbcType=VARCHAR},
                  #{item.routingInspectionTel,jdbcType=VARCHAR},
                  #{item.damagedPartDescribe,jdbcType=VARCHAR},
                  #{item.accidentDescribe,jdbcType=VARCHAR},
                  #{item.trailerFlag,jdbcType=DECIMAL},
                  #{item.repairReplaceTotalAmount,jdbcType=DECIMAL},
                  #{item.repairRepairTotalAmount,jdbcType=DECIMAL},
                  #{item.repairInsuranceTotalAmount,jdbcType=DECIMAL},
                  #{item.vehicleReplaceTotalAmount,jdbcType=DECIMAL},
                  #{item.vehicleRepairTotalAmount,jdbcType=DECIMAL},
                  #{item.vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
                  #{item.vehicleManageViewFlag,jdbcType=DECIMAL},
                  #{item.resurveyFlag,jdbcType=DECIMAL},
                  #{item.resurveyPart,jdbcType=VARCHAR},
                  #{item.dutySituation,jdbcType=DECIMAL},
                  #{item.recoveryAmount,jdbcType=DECIMAL},
                  #{item.insuranceAmount,jdbcType=DECIMAL},
                  #{item.accDepAmount,jdbcType=DECIMAL},
                  #{item.outageLossAmount,jdbcType=DECIMAL},
                  #{item.vehicleLossAmount,jdbcType=DECIMAL},
                  #{item.trailerRescueAmount,jdbcType=DECIMAL},
                  #{item.maintainAmount,jdbcType=DECIMAL},
                  #{item.reassignmentRepairOrgId,jdbcType=VARCHAR},
                  #{item.reassignmentReasons,jdbcType=VARCHAR},
                  #{item.reassignmentRejectReasons,jdbcType=VARCHAR},
                  #{item.verificationRejectReasons,jdbcType=VARCHAR},
                  #{item.verificationRejectReasonsDetail,jdbcType=VARCHAR},
                  #{item.checkResultFlag,jdbcType=DECIMAL},
                  #{item.checkUnqualifiedReason,jdbcType=VARCHAR},
                  #{item.vehicleTransferTaskSchedule,jdbcType=BIGINT},
                  #{item.insuranceQuoteTaskSchedule,jdbcType=BIGINT},
                  #{item.verificationLossTaskSchedule,jdbcType=BIGINT},
                  #{item.reassignmentTaskSchedule,jdbcType=BIGINT},
                  #{item.vehicleRepairTaskSchedule,jdbcType=BIGINT},
                  #{item.vehicleCheckTaskSchedule,jdbcType=BIGINT},
                  #{item.materialCollectionTaskSchedule,jdbcType=INTEGER},
                  #{item.lossRegistrationTaskSchedule,jdbcType=INTEGER},
                  #{item.settlementTaskSchedule,jdbcType=INTEGER},
                  #{item.currentTache,jdbcType=BIGINT},
                  #{item.maintainToRepairFlag,jdbcType=DECIMAL},
                  #{item.verificationLossTaskOperId,jdbcType=BIGINT},
                  #{item.examineLevel,jdbcType=DECIMAL},
                  #{item.verificationLossCheckTime,jdbcType=TIMESTAMP},
                  #{item.verificationLossCheckId,jdbcType=BIGINT},
                  #{item.overTimeReasons,jdbcType=VARCHAR},
                  #{item.repairTotalAmountFirst,jdbcType=DECIMAL},
                  #{item.nuclearLossReversionFlag,jdbcType=DECIMAL},
                  #{item.status,jdbcType=DECIMAL},
                  #{item.remark,jdbcType=VARCHAR},
                  #{item.tirenumber,jdbcType=BIGINT},
                  #{item.associatedTaskNo,jdbcType=VARCHAR},
                  #{item.insuranceFlag,jdbcType=INTEGER},
                  #{item.advancedAuditLeve,jdbcType=BIT},
                  #{item.alreadyIncomingAmount,jdbcType=DECIMAL},
                  #{item.alreadyPayAmount,jdbcType=DECIMAL},
                  #{item.waitPayAmount,jdbcType=DECIMAL},
                  #{item.mustClaimAmount,jdbcType=DECIMAL},
                  #{item.mustPayAmount,jdbcType=DECIMAL},
                  #{item.createTime,jdbcType=TIMESTAMP},
                  #{item.createOperId,jdbcType=BIGINT},
                  #{item.createOperName,jdbcType=VARCHAR},
                  #{item.updateTime,jdbcType=TIMESTAMP},
                  #{item.updateOperId,jdbcType=BIGINT},
                  #{item.updateOperName,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>
    
     <!-- 单条逻辑删除 -->
     <update id="logicalSelectById">
        update ${mtcSchema}.mtc_repair_task set status = 1 , update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR} where id = #{id,jdbcType=BIGINT} and status = 1
     </update>
     
     <!-- 批量逻辑删除 -->
     <update id="batchLogicalSelectById" parameterType="java.util.List">  
        update ${mtcSchema}.mtc_repair_task  set status = 1 , update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR}  where id in  
         <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">  
            ${item}
         </foreach> 
         and status = 1
     </update>
     
    <!-- 查询首页信息 -->
    <select id="getFirstPageInfo" parameterType="map" resultType="com.extracme.evcard.mtc.bo.VehicleRepairNumBO">
        SELECT
            a.repair_depot_id repairDepotId,
            a.repair_depot_name repairDepotName,
            IFNULL(b.currentRepairNum, 0) currentRepairNum,
            IFNULL(c.todayAddNum, 0) todayAddNum,
            IFNULL(b.aRepairNum, 0) aRepairNum,
            IFNULL(b.bRepairNum, 0) bRepairNum,
            IFNULL(b.cRepairNum, 0) cRepairNum,
            IFNULL(d.todayInrepairNum1,0) todayOutNum,
            IFNULL(f.noReciveNum,0) noReciveNum
        FROM
            ${mtcSchema}.mtc_repair_depot_info a
        LEFT JOIN (
        SELECT 
        a.repair_depot_id,
        count(*) currentRepairNum,
                SUM(
                    CASE a.repair_grade
                    WHEN 'A' THEN
                        1
                    ELSE
                        0
                    END
                ) aRepairNum,
                SUM(
                    CASE a.repair_grade
                    WHEN 'B' THEN
                        1
                    ELSE
                        0
                    END
                ) bRepairNum,
                SUM(
                    CASE a.repair_grade
                    WHEN 'C' THEN
                        1
                    ELSE
                        0
                    END
                ) cRepairNum
        FROM(
            SELECT
                repair_depot_id,
                repair_grade
            FROM
                mtc.mtc_repair_task
            WHERE
            --   repair_type_id IN (1, 2, 6,9)
            1=1
            AND status = 1
            AND check_result_flag != 0
            AND reassignment_task_schedule != 410 
            and maintain_to_repair_flag=0
            AND org_id LIKE CONCAT(#{orgId}, '%') 
            and (current_tache &lt;= 60 or current_tache = 110)
            and vehicle_transfer_task_schedule != 130
            and insurance_quote_task_schedule != 250
            and verification_loss_task_schedule != 340
            and reassignment_task_schedule != 420
            and vehicle_repair_task_schedule != 530
            and vehicle_check_task_schedule != 640
            and insurance_pre_review_task_schedule != 1150
          <if test="repairDepotIds!=null and repairDepotIds.size()>0">
            and  repair_depot_id in
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
          </if>
            ) AS a
                GROUP BY
                    a.repair_depot_id
        ) AS b ON a.repair_depot_id = b.repair_depot_id
        LEFT JOIN (
            SELECT
                repair_depot_id,
                count(*) todayAddNum
            FROM
                ${mtcSchema}.mtc_repair_task WHERE org_id like CONCAT(#{orgId},'%')
                and repair_type_id IN (1, 2, 6,9)
                and status = 1
                and (current_tache &lt;= 60 or current_tache = 110)
                and vehicle_transfer_task_schedule != 130
                and insurance_quote_task_schedule != 250
                and verification_loss_task_schedule != 340
                and reassignment_task_schedule != 420
                and vehicle_repair_task_schedule != 530
                and vehicle_check_task_schedule != 640
                and insurance_pre_review_task_schedule != 1150
                and create_time &gt;= concat(
            DATE_FORMAT(NOW(), '%Y-%m-%d'),
            ' 00:00:00.000'
        )
            AND create_time &lt;= #{currentTime}
      <if test="repairDepotIds!=null and repairDepotIds.size()>0">
        and  repair_depot_id in
        <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
      </if>
                GROUP BY repair_depot_id
        ) AS c ON a.repair_depot_id = c.repair_depot_id
        LEFT JOIN (
            SELECT
                count(*) as todayInrepairNum1,
                a.repair_depot_id
            FROM
                mtc.mtc_repair_task a
            WHERE
                a.repair_type_id IN (1, 2, 6,9)
            AND status = 1
            AND a.maintain_to_repair_flag = 0 
            and current_tache &gt;60 and current_tache != 110
            AND a.vehicle_check_time IS NOT NULL
            AND a.check_result_flag = 0
            AND a.vehicle_check_task_schedule = 610 
            AND a.vehicle_check_time &gt;= concat(
                DATE_FORMAT(NOW(), '%Y-%m-%d'),
                ' 00:00:00'
            ) AND a.vehicle_check_time &lt;= #{currentTime}
      <if test="repairDepotIds!=null and repairDepotIds.size()>0">
        and  repair_depot_id in
        <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
      </if>
            GROUP BY a.repair_depot_id
        ) as d ON a.repair_depot_id = d.repair_depot_id 
        LEFT JOIN (
            SELECT
                repair_depot_id,
                count(*) noReciveNum
            FROM
                 mtc_repair_task WHERE org_id like CONCAT(#{orgId},'%')
                 and vehicle_recive_time is null
                 and repair_type_id IN (1, 2, 6,9)
                 and status = 1
                 and vehicle_transfer_task_schedule = 100
      <if test="repairDepotIds!=null and repairDepotIds.size()>0">
        and  repair_depot_id in
        <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
      </if>
                GROUP BY repair_depot_id
        ) f ON a.repair_depot_id = f.repair_depot_id
        <if test="repairDepotIds!=null and repairDepotIds.size()>0">
             where a.repair_depot_id in 
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
        </if>
        ORDER BY a.repair_depot_id ASC

    </select>
    
    <!-- 首页信息条数 -->
    <select id="getFirstPageInfoNum" parameterType="map" resultType="integer">
        SELECT
           count(*)
        from ${mtcSchema}.mtc_repair_depot_info as a
        <if test="repairDepotIds!=null and repairDepotIds.size()>0">
            where a.repair_depot_id in 
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
        </if>
    </select>
    
    <!-- 查询当前在修总数量 -->
    <select id="getCurrentRepairTotalNum" parameterType="string" resultType="long">
      SELECT SUM(a.num)
      FROM (
        SELECT COUNT(*) AS num
        FROM mtc.mtc_repair_task a
        LEFT JOIN mtc.mtc_repair_depot_info b ON a.repair_depot_id = b.repair_depot_id
        WHERE a.repair_type_id IN (1, 2, 6,9)
          AND a.maintain_to_repair_flag = 0
          AND a.vehicle_check_task_schedule != 610
          AND a.check_result_flag != 0
          AND a.reassignment_task_schedule != 410
          AND a.create_time IS NOT NULL
          AND b.sso_flag = 1
          AND (a.current_tache &lt;= 60 or a.current_tache = 110)
          AND vehicle_transfer_task_schedule != 130
          AND insurance_quote_task_schedule != 250
          AND verification_loss_task_schedule != 340
          AND reassignment_task_schedule != 420
          AND vehicle_repair_task_schedule != 530
          AND vehicle_check_task_schedule != 640
          AND insurance_pre_review_task_schedule != 1150
          AND a.status = 1
          <if test="orgId!=null and orgId!=''">
            and a.org_id like CONCAT(#{orgId},'%')
          </if>
      ) as a
    </select>
    
    <!-- 查询今日进修理厂数量 -->
    <select id="getTodayInFactoryNum" parameterType="map" resultType="long">
        SELECT
            COUNT(*)
        FROM
            ${mtcSchema}.mtc_repair_task a
        LEFT JOIN ${mtcSchema}.mtc_repair_depot_info b ON a.repair_depot_id=b.repair_depot_id
        where
        a.status = 1 and
        a.repair_type_id in(1,2,6) and
        a.maintain_to_repair_flag = 0
        AND b.sso_flag=1
        AND a.create_time IS NOT NULL
        and (a.current_tache  &lt;= 60 or a.current_tache = 110)
        AND a.create_time &gt;= concat(
            DATE_FORMAT(NOW(), '%Y-%m-%d'),
            ' 00:00:00.000'
        ) AND a.create_time &lt;= #{currentTime}
        <if test="orgId!=null and orgId!=''">
            and a.org_id like CONCAT(#{orgId},'%')
        </if>
    </select>
    
    <!-- 查询今日出修理厂数量 -->
    <select id="getTodayOutFactoryNum" parameterType="map" resultType="long">
    SELECT sum(a.num)
    from(
        SELECT
            count(*) num
        FROM
            ${mtcSchema}.mtc_repair_task a
        LEFT JOIN ${mtcSchema}.mtc_repair_depot_info b ON a.repair_depot_id=b.repair_depot_id
        WHERE
            a.repair_type_id in (1,2,6,9) and
            a.maintain_to_repair_flag = 0
            and a.check_result_flag=0 AND 
            a.vehicle_check_task_schedule = 610 
            AND b.sso_flag=1
            and a.status = 1
            and a.current_tache  &gt;60 and a.current_tache != 110
            AND a.vehicle_check_time &gt;= concat(
                DATE_FORMAT(NOW(), '%Y-%m-%d'),
                ' 00:00:00'
            ) AND a.vehicle_check_time &lt;= #{currentTime}
        <if test="orgId!=null and orgId!=''">
            and a.org_id like CONCAT(#{orgId},'%')
        </if>
        ) as a
    </select>
    
    <!-- 查询修理厂在修车辆明细 -->
    <select id="getRepairDepotInRepairingInfo" parameterType="map" resultType="com.extracme.evcard.mtc.bo.RepairDepotInRepairingBO">
        SELECT
            a.vehicleFinishTime,
            a.org_name orgName,
            a.task_no taskNo,
            a.vehicle_no vehicleNo,
            a.vehicle_model_info vehicleModelName,
            a.vin,
            a.insurance_company_name insuranceCompanyName,
            case when (a.maintain_to_repair_flag=1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）' else a.repair_type_name end as repairTypeName,
            a.repair_grade repairGrade,
            a.vehicle_recive_time vehicleReciveTime,
            a.renttype renttype
        FROM(
        SELECT
            date_add(vehicle_recive_time, INTERVAL expected_repair_days DAY) vehicleFinishTime,
            org_name,
            task_no,
            vehicle_no,
            vehicle_model_info,
            vin,
            insurance_company_name,
            repair_type_name,
            repair_grade,
            vehicle_recive_time,
            maintain_to_repair_flag,
            update_time,
            renttype,
            repair_type_id
        FROM
            ${mtcSchema}.mtc_repair_task
        where
            repair_type_id in(1,2,6)
            AND status = 1
            AND vehicle_check_task_schedule !=610 
            AND check_result_flag !=0 
            AND reassignment_task_schedule!=410 
            AND (current_tache  &lt;=60 or current_tache = 110)
            and vehicle_transfer_task_schedule != 130
            and insurance_quote_task_schedule != 250
            and verification_loss_task_schedule != 340
            and reassignment_task_schedule != 420
            and vehicle_repair_task_schedule != 530
            and vehicle_check_task_schedule != 640
            AND insurance_pre_review_task_schedule != 1150
        <if test="orgId!=null and orgId!=''">
            and org_id like CONCAT(#{orgId},'%')
        </if>   
        <if test="repairDepotId!=null and repairDepotId!=''">
            and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
        </if>
        UNION ALL
        SELECT
            date_add(vehicle_recive_time, INTERVAL expected_repair_days DAY),
            org_name,
            task_no,
            vehicle_no,
            vehicle_model_info,
            vin,
            insurance_company_name,
            repair_type_name,
            repair_grade,
            vehicle_recive_time,
            maintain_to_repair_flag,
            update_time,
            renttype,
            repair_type_id
        FROM
            ${mtcSchema}.mtc_repair_task
            WHERE repair_type_id =3
            AND status = 1
            AND insurance_quote_task_schedule !=230 
            AND reassignment_task_schedule!=410 
            AND current_tache  &lt;=60
            and vehicle_transfer_task_schedule != 130
            and insurance_quote_task_schedule != 250
        <if test="orgId!=null and orgId!=''">
            and org_id like CONCAT(#{orgId},'%')
        </if>   
        <if test="repairDepotId!=null and repairDepotId!=''">
            and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
        </if>) as a
        ORDER BY a.update_time desc
    </select>
    
    <!-- 查询修理厂在修车辆明细条数 -->
    <select id="getRepairDepotInRepairingInfoNum" parameterType="map" resultType="integer">
    select sum(num)
    from(
        SELECT
            count(*) num
        FROM
            ${mtcSchema}.mtc_repair_task 
        where
            repair_type_id in(1,2,6)
             AND status = 1
             AND vehicle_check_task_schedule !=610 
             AND check_result_flag !=0 
             AND reassignment_task_schedule!=410 
             AND create_time is not null 
        <if test="orgId!=null and orgId!=''">
            and org_id like CONCAT(#{orgId},'%')
        </if>   
        <if test="repairDepotId!=null and repairDepotId!=''">
            and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
        </if>
          ) as a
    </select>
    
    <select id="selectByTaskNo" parameterType="string" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
         from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo} and status != 0 and reassignment_task_schedule != 410
    </select>
  
    <select id="selectByTaskNoAll" parameterType="string" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo} 
    </select>
    
    <select id="selectByTaskNoV" parameterType="string" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
         from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo} and status != 0
    </select>
    
    <insert id="saveByRepairTaskModel" parameterType="com.extracme.evcard.mtc.bo.RepairTaskModelBO" useGeneratedKeys="true" keyProperty="id">
        insert into ${mtcSchema}.mtc_repair_task (
            id,
            task_no,
            repair_type_id,
            repair_grade,
            vehicle_no,
            vehicle_model_seq,
            vehicle_model_info,
            org_id,
            org_name,
            operate_org_id,
            operate_org_name,
            vin,
            repair_depot_id,
            repair_depot_org_id,
            repair_depot_name,
            repair_depot_sap_code,
            repair_depot_type,
            is_proxy_operable,
            <if test="terminalId!=null and terminalId!=''">
            terminal_id,
            </if>
            <if test="insuranceCompanyName!=null and insuranceCompanyName!=''">
            insurance_company_name,
            </if>                                    
            total_mileage,
            <if test="mileage !=null and mileage !=''">
              mileage,
            </if>
            task_inflow_time,
            associated_order,
            <if test="orderType!=null">
              order_type,
            </if>
            <if test="relateType!=null">
              relate_type,
            </if>
            <if test="orderRemark!=null and orderRemark!=''">
              order_remark,
            </if>
            no_deductibles_flag,
            driver_name,
            driver_tel,
            routing_inspection_name,
            routing_inspection_tel,
            damaged_part_describe,
            accident_describe,
            trailer_flag,
            accident_report_number,
            create_time,
            create_oper_id,
            create_oper_name,
            update_time,
            update_oper_id,
            update_oper_name,
            repair_type_name,
            <if test="tireNumber!=null and tireNumber!=''">
            tireNumber,
            </if>
            <if test="accidentNo!=null and accidentNo!=''">
              accident_no,
            </if>
            <if test="taxRate != null and taxRate != ''">
              tax_rate,
            </if>
            <if test="sendRepairTime!=null and sendRepairTime!=''">
              send_repair_time,
            </if>
            current_tache,
            origin,
            renttype,
            fact_operate_tag,
            <if test="propertyStatus!=null">
              property_status,
            </if>
            <if test="productLine!=null">
              product_line,
            </if>
            <if test="subProductLine!=null">
              sub_product_line,
            </if>
            <if test="violationName != null">
              violation_name,
            </if>
            <if test="violationTelNo != null">
              violation_tel_no,
            </if>
            <if test="vehicleClientMaintenanceFee != null">
              vehicle_client_maintenance_fee,
            </if>
            <if test="clientInspectTag != null">
              client_inspect_tag,
            </if>
            <if test="clientUpkeepTag != null">
              client_upkeep_tag,
            </if>
            <if test="serviceType != null">
              service_type,
            </if>
            <if test="serviceContent != null">
              service_content,
            </if>
            auth_id
        ) values (
            #{id,jdbcType=BIGINT},
            #{dispatchTaskSeq,jdbcType=VARCHAR},
            #{repairTypeId,jdbcType=DECIMAL},
            #{repairGrade,jdbcType=VARCHAR},
            #{vehicleNo,jdbcType=VARCHAR},
            #{vehicleModelSeq,jdbcType=BIGINT},
            #{vehicleModelInfo,jdbcType=VARCHAR},
            #{orgId,jdbcType=VARCHAR},
            #{orgName,jdbcType=VARCHAR},
            #{operateOrgId,jdbcType=VARCHAR},
            #{operateOrgName,jdbcType=VARCHAR},
            #{vin,jdbcType=VARCHAR},
            #{repairDepotId,jdbcType=BIGINT},
            #{repairDepotOrgId,jdbcType=VARCHAR},
            #{repairDepotName,jdbcType=VARCHAR},
            #{repairDepotSapCode,jdbcType=VARCHAR},
            #{repairDepotType,jdbcType=INTEGER},
            #{isProxyOperable,jdbcType=INTEGER},
            <if test="terminalId!=null and terminalId!=''">
            #{terminalId,jdbcType=VARCHAR},
            </if>
            <if test="insuranceCompanyName!=null and insuranceCompanyName!=''">
            #{insuranceCompanyName,jdbcType=VARCHAR},
            </if>
            #{totalMileage,jdbcType=DECIMAL},
            <if test="mileage !=null and mileage !=''">
              #{mileage,jdbcType=VARCHAR},
            </if>
            #{taskCreateTime,jdbcType=TIMESTAMP},            
            #{associatedOrder,jdbcType=VARCHAR},
            <if test="orderType!=null">
              #{orderType,jdbcType=INTEGER},
            </if>
            <if test="relateType!=null">
              #{relateType,jdbcType=INTEGER},
            </if>
            <if test="orderRemark!=null and orderRemark!=''">
              #{orderRemark,jdbcType=VARCHAR},
            </if>
            #{noDeductiblesFlag,jdbcType=DECIMAL},
            #{driverName,jdbcType=VARCHAR},
            #{driverTel,jdbcType=VARCHAR},
            #{routingInspectionName,jdbcType=VARCHAR},
            #{routingInspectionTel,jdbcType=VARCHAR},
            #{damagedPartDescribe,jdbcType=VARCHAR},
            #{accidentDescribe,jdbcType=VARCHAR},
            #{trailerFlag,jdbcType=DECIMAL},
            #{accidentReportNumber,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{createOperId,jdbcType=BIGINT},
            #{createOperName,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{updateOperId,jdbcType=BIGINT},
            #{updateOperName,jdbcType=VARCHAR},
            #{repairTypeName,jdbcType=VARCHAR},
            <if test="tireNumber!=null and tireNumber!=''">
              #{tireNumber,jdbcType=BIGINT},
            </if>
            <if test="accidentNo!=null and accidentNo!=''">
              #{accidentNo,jdbcType=VARCHAR},
            </if>
            <if test="taxRate != null and taxRate != ''">
              #{taxRate,jdbcType=VARCHAR},
            </if>
            <if test="sendRepairTime!=null and sendRepairTime!=''">
              #{sendRepairTime,jdbcType=TIMESTAMP},
            </if>
            #{currentTache,jdbcType=BIGINT},
            #{origin,jdbcType=INTEGER},
            #{renttype,jdbcType=INTEGER},
            #{factOperateTag, jdbcType=INTEGER},
            <if test="propertyStatus!=null">
              #{propertyStatus, jdbcType=INTEGER},
            </if>
            <if test="productLine!=null">
              #{productLine, jdbcType=INTEGER},
            </if>
            <if test="subProductLine!=null">
              #{subProductLine, jdbcType=INTEGER},
            </if>
            <if test="violationName != null">
              #{violationName, jdbcType=VARCHAR},
            </if>
            <if test="violationTelNo != null">
              #{violationTelNo, jdbcType=VARCHAR},
            </if>
            <if test="vehicleClientMaintenanceFee != null">
              #{vehicleClientMaintenanceFee, jdbcType=TINYINT},
            </if>
            <if test="clientInspectTag != null">
              #{clientInspectTag, jdbcType=TINYINT},
            </if>
            <if test="clientUpkeepTag != null">
              #{clientUpkeepTag, jdbcType=TINYINT},
            </if>
            <if test="serviceType != null">
              #{serviceType, jdbcType=INTEGER},
            </if>
            <if test="serviceContent != null">
              #{serviceContent, jdbcType=VARCHAR},
            </if>
            #{authId,jdbcType=VARCHAR}
        )
    </insert>
  <!-- 流程查询一览 -->
  <select id="getAllProcessResultInfo" parameterType="map" resultType="com.extracme.evcard.mtc.bo.ProcessResultBO">
    SELECT
      id,
      task_no taskNo,
      org_name orgName,
      vehicle_no vehicleNo,
      vehicle_model_info vehicleModelName,
      vin,
      insurance_company_name insuranceCompanyName,
      case when (maintain_to_repair_flag = 1 and repair_type_id != 3) then N'自费维修（原车辆保养）' else repair_type_name end as
      repairTypeName,
      repair_grade repairGrade,
      repair_depot_id repairDepotId,
      repair_depot_name repairDepotName,
      task_inflow_time taskCreateTime,
      create_time taskInflowTime,
      vehicle_recive_time vehicleReciveTime,
      vehicle_check_time vehicleCheckTime,
      case current_tache
      when 10 then N'车辆交接'
      when 20 then N'维修报价'
      when 30 then N'核损核价'
      when 40 then N'改派中'
      when 50 then N'车辆维修'
      when 60 then N'车辆验收'
      when 70 then N'资料收集'
      when 80 then N'损失登记'
      when 90 then N'结算管理'
      when 100 then N'保养待维修'
      when 110 then N'进保预审'
      else '' end currentTache,
      current_tache currentTacheId,
      vehicle_transfer_task_schedule vehicleTransferTaskSchedule,
      insurance_quote_task_schedule insuranceQuoteTaskSchedule,
      verification_loss_task_schedule verificationLossTaskSchedule,
      reassignment_task_schedule reassignmentTaskSchedule,
      vehicle_repair_task_schedule vehicleRepairTaskSchedule,
      vehicle_check_task_schedule vehicleCheckTaskSchedule,
      material_collection_task_schedule materialCollectionTaskSchedule,
      loss_registration_task_schedule lossRegistrationTaskSchedule,
      settlement_task_schedule settlementTaskSchedule,
      insurance_pre_review_task_schedule insurancePreReviewTaskSchedule,
      examine_level examineLevel,
      repair_type_id repairTypeId,
      renttype renttype,
      fact_operate_tag as factOperateTag,
      review_to_sel_fee_flag as reviewToSelFeeFlag,
      is_used_applets as isUsedApplets,
      cust_pays_direct as custPaysDirect,
      cust_amount as custAmount,

      associated_order associatedOrder,
      order_type orderType,
      relate_type relateType,
      order_remark orderRemark,
      violation_name violationName,
      violation_tel_no violationTelNo,
      vehicle_client_maintenance_fee vehicleClientMaintenanceFee,
      client_inspect_tag clientInspectTag,
      client_upkeep_tag clientUpkeepTag,
      service_type serviceType,
      service_content serviceContent,
      property_status propertyStatus,
      product_line productLine,
      sub_product_line subProductLine
    FROM
    ${mtcSchema}.mtc_repair_task
    <where>
      current_tache!=-1 and status=1
      <if test="orgId!=null and orgId!=''">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no = #{vehicleNo}
      </if>
      <if test="vin!=null and vin!=''">
        and vin = #{vin}
      </if>
      <if test="taskNo!=null and taskNo!=''">
        and task_no = #{taskNo}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotOrgName!=null and repairDepotOrgName!=''">
        and repair_depot_name like concat('%',#{repairDepotOrgName},'%')
      </if>
      <if test="taskCreateStartTime!=null and taskCreateStartTime!=''">
        and task_inflow_time &gt;= #{taskCreateStartTime}
      </if>
      <if test="taskCreateEndTime!=null and taskCreateEndTime!=''">
        and task_inflow_time &lt;= #{taskCreateEndTime}
      </if>
      <if test="taskInflowStartTime!=null and taskInflowStartTime!=''">
        and create_time &gt;= #{taskInflowStartTime}
      </if>
      <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
        and create_time &lt;= #{taskInflowEndTime}
      </if>
      <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
        and vehicle_recive_time &gt;= #{vehicleReciveStartTime}
      </if>
      <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
        and vehicle_recive_time &lt;= #{vehicleReciveEndTime}
      </if>
      <if test="vehicleCheckStartTime!=null and vehicleCheckStartTime!=''">
        and vehicle_check_time &gt;= #{vehicleCheckStartTime}
      </if>
      <if test="vehicleCheckEndTime!=null and vehicleCheckEndTime!=''">
        and vehicle_check_time &lt;= #{vehicleCheckEndTime}
      </if>
      <if test="currentTacheId!=null">
        and current_tache = #{currentTacheId}
      </if>
      <if test="vehicleModelId!=null">
        and vehicle_model_seq = #{vehicleModelId}
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo!=''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    ORDER BY create_time DESC
  </select>
  
  <!-- 流程查询总条数 -->
  <select id="getAllProcessNum" parameterType="map" resultType="integer">
    SELECT
    count(*)
    FROM
    ${mtcSchema}.mtc_repair_task
    <where>
      current_tache!=-1 and status=1
      <if test="orgId!=null and orgId!=''">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no = #{vehicleNo}
      </if>
      <if test="vin!=null and vin!=''">
        and vin = #{vin}
      </if>
      <if test="taskNo!=null and taskNo!=''">
        and task_no = #{taskNo}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotOrgName!=null and repairDepotOrgName!=''">
        and repair_depot_name like concat('%',#{repairDepotOrgName},'%')
      </if>
      <if test="taskCreateStartTime!=null and taskCreateStartTime!=''">
        and task_inflow_time &gt;= #{taskCreateStartTime}
      </if>
      <if test="taskCreateEndTime!=null and taskCreateEndTime!=''">
        and task_inflow_time &lt;= #{taskCreateEndTime}
      </if>
      <if test="taskInflowStartTime!=null and taskInflowStartTime!=''">
        and create_time &gt;= #{taskInflowStartTime}
      </if>
      <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
        and create_time &lt;= #{taskInflowEndTime}
      </if>
      <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
        and vehicle_recive_time &gt;= #{vehicleReciveStartTime}
      </if>
      <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
        and vehicle_recive_time &lt;= #{vehicleReciveEndTime}
      </if>
      <if test="vehicleCheckStartTime!=null and vehicleCheckStartTime!=''">
        and vehicle_check_time &gt;= #{vehicleCheckStartTime}
      </if>
      <if test="vehicleCheckEndTime!=null and vehicleCheckEndTime!=''">
        and vehicle_check_time &lt;= #{vehicleCheckEndTime}
      </if>
      <if test="currentTacheId!=null">
        and current_tache = #{currentTacheId}
      </if>
      <if test="vehicleModelId!=null">
        and vehicle_model_seq = #{vehicleModelId}
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo!=''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
  </select>
    
    <!-- 查看车辆验收列表 -->
  <select id="querySchedule" parameterType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleQueryBO"
          resultType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleBO">
    SELECT
    a.id as id,
    a.task_no as taskNo,
    a.org_id as orgId,
    a.org_name as orgName,
    a.vehicle_no as vehicleNo,
    a.vehicle_model_seq as vehicleModelSeq,
    a.vehicle_model_info as vehicleModelInfo,
    a.vin as vin,
    a.repair_type_id as repairTypeId,
    case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）'else a.repair_type_name end
    as repairTypeName,
    a.repair_depot_org_id as repairDepotOrgId,
    a.repair_depot_name as repairDepotName,
    a.repair_depot_sap_code as repairDepotSapCode,
    a.sap_send_status as sapSendStatus,
    a.sap_sync_flag as sapSyncFlag,
    a.task_inflow_time as taskInflowTime,
    a.vehicle_recive_time as vehicleReciveTime,
    a.resurvey_flag as resurveyFlag,
    a.vehicle_check_time as vehicleCheckTime,
    a.vehicle_check_task_schedule as vehicleCheckTaskSchedule,
    a.vehicle_insurance_total_amount as totalMoney,
    a.repair_grade as repairGrade,
    b.subtask_no as subtaskNo,
    case when b.subtask_no!='' and a.vehicle_check_task_schedule=610 THEN 1
    WHEN date_add(
    a.verification_loss_check_time,
    INTERVAL a.expected_repair_days DAY
    ) > (
    CASE
    WHEN a.vehicle_repair_time IS NOT NULL THEN
    a.vehicle_repair_time
    ELSE
    now()
    END
    ) THEN
    1
    ELSE
    0
    END
    as timeOut,
    renttype as renttype,
    a.fact_operate_tag as factOperateTag,
    a.review_to_sel_fee_flag as reviewToSelFeeFlag,
    a.declare_no as declareNo,
    a.declare_status as declareStatus,
    a.declare_settlement_status as declareSettlementStatus,
    a.settlement_no as settlementNo,
    a.settlement_status as settlementStatus
    FROM
    ${mtcSchema}.mtc_repair_task a
    LEFT JOIN ${mtcSchema}.mtc_vehicle_advance_check_task b ON a.task_no = b.task_no AND b.`status` =1
    <where>
      a.`status` = 1 AND
      a.vehicle_check_task_schedule = #{vehicleCheckTaskSchedule}
      <if test="taskNo != null and taskNo != ''">
        and a.task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and a.vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and a.vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and a.vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and a.maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and a.repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and a.resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add( a.vehicle_recive_time,INTERVAL a.expected_repair_days DAY ) >
        (CASE WHEN a.vehicle_repair_time IS NOT NULL THEN a.vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="startVehicleCheckTime != null and startVehicleCheckTime != ''">
        and a.vehicle_check_time >= DATE_FORMAT(#{startVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleCheckTime != null and endVehicleCheckTime != ''">
        and a.vehicle_check_time &lt;= DATE_FORMAT(#{endVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="sapSendStatus != null">
        and a.sap_send_status = #{sapSendStatus, jdbcType=INTEGER}
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and a.accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    order by a.create_time DESC
  </select>
  <!-- 查看车辆验收列表 -->
  <select id="queryScheduleExport" parameterType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleQueryBO"
          resultType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleBO">
    SELECT
    a.id as id,
    a.task_no as taskNo,
    a.org_id as orgId,
    a.org_name as orgName,
    a.vehicle_no as vehicleNo,
    a.vehicle_model_seq as vehicleModelSeq,
    a.vehicle_model_info as vehicleModelInfo,
    a.vin as vin,
    a.repair_type_id as repairTypeId,
    case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）'else a.repair_type_name end
    as repairTypeName,
    a.repair_depot_org_id as repairDepotOrgId,
    a.repair_depot_name as repairDepotName,
    a.repair_depot_sap_code as repairDepotSapCode,
    a.sap_send_status as sapSendStatus,
    a.sap_sync_flag as sapSyncFlag,
    a.task_inflow_time as taskInflowTime,
    a.vehicle_recive_time as vehicleReciveTime,
    a.resurvey_flag as resurveyFlag,
    a.vehicle_check_time as vehicleCheckTime,
    a.vehicle_check_task_schedule as vehicleCheckTaskSchedule,
    a.vehicle_insurance_total_amount as totalMoney,
    a.repair_grade as repairGrade,
    b.subtask_no as subtaskNo,
    case when b.subtask_no!='' and a.vehicle_check_task_schedule=610 THEN 1
    WHEN date_add(
    a.verification_loss_check_time,
    INTERVAL a.expected_repair_days DAY
    ) > (
    CASE
    WHEN a.vehicle_repair_time IS NOT NULL THEN
    a.vehicle_repair_time
    ELSE
    now()
    END
    ) THEN
    1
    ELSE
    0
    END
    as timeOut,
    renttype as renttype,
    a.fact_operate_tag as factOperateTag,
    a.review_to_sel_fee_flag as reviewToSelFeeFlag,
    ifnull(a.repair_review_total_amount,0) as repairReviewTotalAmount,
    ifnull(a.repair_insurance_total_amount,0)  as repairInsuranceTotalAmount,
    ifnull(a.vehicle_insurance_total_amount,0)  as vehicleInsuranceTotalAmount,
    a.declare_no as declareNo,
    a.declare_status as declareStatus,
    a.declare_settlement_status as declareSettlementStatus, 
    a.settlement_no as settlementNo,
    a.settlement_status as settlementStatus
    FROM
    ${mtcSchema}.mtc_repair_task a
    LEFT JOIN ${mtcSchema}.mtc_vehicle_advance_check_task b ON a.task_no = b.task_no AND b.`status` =1
    <where>
      a.`status` = 1 AND
      a.vehicle_check_task_schedule = #{vehicleCheckTaskSchedule}
      <if test="taskNo != null and taskNo != ''">
        and a.task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and a.vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and a.vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and a.vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and a.maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and a.repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and a.resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add( a.vehicle_recive_time,INTERVAL a.expected_repair_days DAY ) >
        (CASE WHEN a.vehicle_repair_time IS NOT NULL THEN a.vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="startVehicleCheckTime != null and startVehicleCheckTime != ''">
        and a.vehicle_check_time >= DATE_FORMAT(#{startVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleCheckTime != null and endVehicleCheckTime != ''">
        and a.vehicle_check_time &lt;= DATE_FORMAT(#{endVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="sapSendStatus != null">
        and a.sap_send_status = #{sapSendStatus, jdbcType=INTEGER}
      </if>
      <if test="lastUpdateTime != null and lastId != null">
        and a.update_time &lt; #{lastUpdateTime} or (a.update_time = #{lastUpdateTime} and a.id &lt; #{lastId})
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and a.accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    order by a.update_time DESC
    limit #{exportSize}
  </select>
  
  <!-- 查看车辆验收列表 -->
  <select id="queryScheduleInfo" parameterType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleQueryBO"
          resultType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleBO">
    SELECT
    a.id as id,
    a.task_no as taskNo,
    a.org_id as orgId,
    a.org_name as orgName,
    a.vehicle_no as vehicleNo,
    a.vehicle_model_seq as vehicleModelSeq,
    a.vehicle_model_info as vehicleModelInfo,
    a.vin as vin,
    a.repair_type_id as repairTypeId,
    case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）'else a.repair_type_name end
    as repairTypeName,
    a.repair_depot_org_id as repairDepotOrgId,
    a.repair_depot_name as repairDepotName,
    a.task_inflow_time as taskInflowTime,
    a.vehicle_recive_time as vehicleReciveTime,
    a.resurvey_flag as resurveyFlag,
    a.vehicle_check_time as vehicleCheckTime,
    a.vehicle_check_task_schedule as vehicleCheckTaskSchedule,
    a.repair_grade as repairGrade,
    b.subtask_no as subtaskNo,
    CASE
    WHEN date_add(
    a.vehicle_recive_time,
    INTERVAL a.expected_repair_days DAY
    ) > (
    CASE
    WHEN a.vehicle_repair_time IS NOT NULL THEN
    a.vehicle_repair_time
    ELSE
    now()
    END
    ) THEN
    1
    ELSE
    0
    END
    as timeOut
    FROM
    ${mtcSchema}.mtc_repair_task a
    LEFT JOIN ${mtcSchema}.mtc_vehicle_advance_check_task b ON a.task_no = b.task_no AND b.`status` =1
    <where>
      a.`status` = 1 AND
      a.vehicle_check_task_schedule in('600','620')
      <if test="taskNo != null and taskNo != ''">
        and a.task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and a.vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and a.vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and a.vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and a.repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and a.resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add( a.vehicle_recive_time,INTERVAL a.expected_repair_days DAY ) >
        (CASE WHEN a.vehicle_repair_time IS NOT NULL THEN a.vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and a.accident_no = #{accidentNo}
      </if>
    </where>
    order by a.update_time DESC
  </select>
  
  <select id="queryApplyCount" parameterType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleQueryBO"
          resultType="java.lang.Long">
    SELECT
    count(*)
    FROM
    ${mtcSchema}.mtc_repair_task
    <where>
      vehicle_check_task_schedule = 620 and status = 1
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add(vehicle_recive_time,INTERVAL expected_repair_days DAY ) >
        (CASE WHEN vehicle_repair_time IS NOT NULL THEN vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
        <if test="accidentNo != null and accidentNo != ''">
          and accident_no = #{accidentNo}
        </if>
        <if test="reviewToSelFeeFlag != null">
          and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
        </if>
      </if>
    </where>
  </select>
  
  <select id="queryWaitCount" parameterType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleQueryBO"
          resultType="java.lang.Long">
    SELECT
    count(*)
    FROM
    ${mtcSchema}.mtc_repair_task
    <where>
      vehicle_check_task_schedule = 600 and status = 1
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add(vehicle_recive_time,INTERVAL expected_repair_days DAY ) >
        (CASE WHEN vehicle_repair_time IS NOT NULL THEN vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
  </select>
  
  <select id="queryFinishCount" parameterType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleQueryBO"
          resultType="java.lang.Long">
    SELECT
    count(*)
    FROM
    ${mtcSchema}.mtc_repair_task
    <where>
      vehicle_check_task_schedule = 610 and status = 1
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add(vehicle_recive_time,INTERVAL expected_repair_days DAY ) >
        (CASE WHEN vehicle_repair_time IS NOT NULL THEN vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
  </select>
  
  <select id="queryClosedCount" parameterType="com.extracme.evcard.mtc.bo.VehicleCheckTaskScheduleQueryBO"
          resultType="java.lang.Long">
    SELECT
    count(*)
    FROM
    ${mtcSchema}.mtc_repair_task
    <where>
      vehicle_check_task_schedule = 640 and status = 1
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add(vehicle_recive_time,INTERVAL expected_repair_days DAY ) >
        (CASE WHEN vehicle_repair_time IS NOT NULL THEN vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
  </select>

  <!-- 验收状态  通过-->
  <update id="updateByTaskNo" parameterType="com.extracme.evcard.mtc.bo.CheckAcceptancePassBO">
    <selectKey keyProperty='id' resultType='long' order='BEFORE'>
      select id FROM
      ${mtcSchema}.mtc_repair_task
      WHERE
      task_no = #{taskNo,jdbcType=VARCHAR}
    </selectKey>
    update ${mtcSchema}.mtc_repair_task
    <set>
      <if test="vehicleCheckTime != null">
        vehicle_check_time = #{vehicleCheckTime},
      </if>
      <if test="vehicleCheckTaskSchedule != null">
        vehicle_check_task_schedule = #{vehicleCheckTaskSchedule},
      </if>
      <if test="currentTache != null">
        current_tache = #{currentTache},
      </if>
      <if test="checkResultFlag != null">
        check_result_flag = #{checkResultFlag},
      </if>
      <if test="materialCollectionTaskSchedule != null">
        material_collection_task_schedule = #{materialCollectionTaskSchedule,jdbcType=INTEGER},
      </if>
      <if test="updateUserId != null">
        update_oper_id = #{updateUserId},
      </if>
      <if test="updateUserName != null">
        update_oper_name = #{updateUserName},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="claimsFlag != null">
        claims_flag = #{claimsFlag},
      </if>
      <if test="estimatedClaimAmount != null">
        estimated_claim_amount = #{estimatedClaimAmount},
      </if>
      <if test="confirmType != null">
        confirm_type = #{confirmType},
      </if>
      <if test="maintainToRepairFlag != null">
        maintain_to_repair_flag = #{maintainToRepairFlag},
      </if>
      <if test="carDamageType != null">
        car_damage_type = #{carDamageType},
      </if>
      <if test="carDamageType != null">
        car_damage_type = #{carDamageType},
      </if>
      <if test="custPaysDirect != null">
        cust_pays_direct = #{custPaysDirect,jdbcType=INTEGER},
      </if>
      <if test="custAmount != null">
        cust_amount = #{custAmount,jdbcType=DECIMAL},
      </if>
      <if test="userAssumedAmount != null">
        user_assumed_amount = #{userAssumedAmount,jdbcType=DECIMAL},
      </if>
      <if test="notUserAssumedAmount != null">
        not_user_assumed_amount = #{notUserAssumedAmount,jdbcType=DECIMAL},
      </if>
      <if test="selfFundedAmount != null">
        self_funded_amount = #{selfFundedAmount,jdbcType=DECIMAL},
      </if>
      <if test="repairFlag != null">
        repair_flag = #{repairFlag,jdbcType=INTEGER},
      </if>
      verification_loss_task_oper_id = null
    </set>
    where task_no = #{taskNo}
  </update>

  <!-- 提前提车 修改状态 -->
     <update id="updateStatus" parameterType="com.extracme.evcard.mtc.bo.RepairTaskAdviceBO">
      update  ${mtcSchema}.mtc_repair_task 
      <set>
           <if test="vehicleRepairTime != null and vehicleRepairTime !=''">
               vehicle_repair_time = #{vehicleRepairTime},
           </if>
           vehicle_check_task_schedule = 620
     </set> 
     <where>
         task_no = #{taskNo}
     </where>
    </update>
  
  <select id="allMaintenanceLossAuditPrice" resultMap="maintenanceLossAuditPriceResultMap">
    select
    a.id,
    a.task_no,
    a.org_id,
    a.org_name,
    a.vehicle_no,
    a.vehicle_model_info,
    a.vin,
    a.insurance_company_name,
    a.repair_type_id,
    case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）'else a.repair_type_name end
    as repair_type_name,
    a.repair_grade,
    a.repair_depot_name,
    a.vehicle_check_time,
    a.task_inflow_time,
    a.vehicle_recive_time,
    a.resurvey_flag,
    a.update_time,
    a.examine_level,
    a.advanced_audit_leve,
    c.name,
    a.verification_loss_task_schedule,
    REPLACE(CONCAT(UNIX_TIMESTAMP(a.update_time),''),'.','') as update_end_time,
    b.cost_no,
    <if test=" lAPricelevel==0 or lAPricelevel==1">
      case when verification_loss_task_schedule = 300 and examine_level = 0 then N'未处理'
      when verification_loss_task_schedule = 310 and examine_level = 0 then N'处理中'
      when verification_loss_task_schedule = 320 then N'已完成'
      when verification_loss_task_schedule = 340 then N'已关闭'
      when verification_loss_task_schedule in (300,310) and examine_level = 1 then N'提交上级'
    </if>
    <if test=" lAPricelevel>=2">
      case when verification_loss_task_schedule = 300 then N'未处理'
      when verification_loss_task_schedule = 310 then N'处理中'
      when verification_loss_task_schedule = 320 then N'已完成'
      when verification_loss_task_schedule = 340 then N'已关闭'
    </if>
    else '' end as verification_loss_task_name,
    renttype,
    a.fact_operate_tag,
    a.review_to_sel_fee_flag
    from ${mtcSchema}.mtc_repair_task a
    left join mtc_cost_recourse_task b on a.id = b.repair_task_id
    left join ${mtcSchema}.mtc_user c on c.id = a.verification_loss_task_oper_id
    <where>
      a.status = 1 and a.org_id LIKE CONCAT(#{orgId,jdbcType=VARCHAR}, '%')
      and verification_loss_task_schedule in (300,310,315,320,340)
      <!--<if test=" lAPricelevel==0 or lAPricelevel==1">-->
      <!--and verification_loss_task_schedule in (300,310,315,320,340)-->
      <!--</if>-->
      <!---->
      <!--<if test=" lAPricelevel>=2 and subordinate ==0 ">-->
      <!--and verification_loss_task_schedule in (300,310,320,340) and examine_level = 1-->
      <!--</if>-->
      <!--<if test=" lAPricelevel>=2 and subordinate ==1 ">-->
      <!--and verification_loss_task_schedule in (300,310,320,340)-->
      <!--</if>-->
      <if test=" orgId != null and orgId != ''">
        and a.org_id like CONCAT(#{orgId,jdbcType=VARCHAR},'%')
      </if>
      <if test=" vehicleNo != null and vehicleNo != ''">
        and vehicle_no like CONCAT(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test=" vin != null and vin !=''">
        and vin = #{vin,jdbcType=VARCHAR}
      </if>
      <if test=" taskNo != null and taskNo !=''">
        and task_no = #{taskNo,jdbcType=VARCHAR}
      </if>
      <if test=" vehicleModelSeq != null">
        and vehicle_model_seq = #{vehicleModelSeq,jdbcType=BIGINT}
      </if>
      <if test=" repairDepotOrgName != null and repairDepotOrgName != ''">
        and repair_depot_name LIKE CONCAT('%',#{repairDepotOrgName,jdbcType=VARCHAR},'%')
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test=" createTimeStart != null and createTimeStart != ''">
        and task_inflow_time <![CDATA[ >= ]]> #{createTimeStart,jdbcType=TIMESTAMP}
      </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
        and task_inflow_time <![CDATA[ <= ]]> #{createTimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test=" vehicleReciveTimeStart != null and vehicleReciveTimeStart !=''">
        and vehicle_recive_time <![CDATA[ >= ]]> #{vehicleReciveTimeStart,jdbcType=TIMESTAMP}
      </if>
      <if test=" vehicleReciveTimeEnd !='' and  vehicleReciveTimeEnd != null">
        and vehicle_recive_time <![CDATA[ <= ]]> #{vehicleReciveTimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test=" verificationLossTaskSchedule != null and verificationLossTaskSchedule != 330">
        and verification_loss_task_schedule = #{verificationLossTaskSchedule,jdbcType=BIGINT}
      </if>
      <if test=" verificationLossTaskSchedule != null and verificationLossTaskSchedule == 330">
        and examine_level = 1
      </if>
      <if test=" lAPricelevel>=2 and verificationLossTaskSchedule != null and verificationLossTaskSchedule == 330">
        and examine_level = 2
      </if>
      <if
        test=" (lAPricelevel==1 or lAPricelevel==0) and verificationLossTaskSchedule != null and verificationLossTaskSchedule == 330">
        and verification_loss_task_schedule != 320
      </if>
      <if
        test=" (lAPricelevel==1 or lAPricelevel==0) and verificationLossTaskSchedule != null and (verificationLossTaskSchedule == 300 or verificationLossTaskSchedule == 310)">
        and examine_level != 1
      </if>
      <if test=" resurveyFlag != null">
        and resurvey_flag = #{resurveyFlag,jdbcType=DECIMAL}
      </if>
      <if test=" advancedAuditLeve != null and advancedAuditLeve != ''">
        <!--<choose>-->
        <!--<when test="advancedAuditLeve == 7">-->
        <!--and verification_loss_task_schedule = 320-->
        <!--</when>-->
        <!--<when test="advancedAuditLeve == 1">-->
        <!--and advanced_audit_leve = 1 and verification_loss_task_schedule &lt;= 310-->
        <!--</when>-->
        <!--<otherwise>-->
        and advanced_audit_leve = #{advancedAuditLeve,jdbcType=VARCHAR}
        <!--</otherwise>-->
        <!--</choose>-->
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and a.accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    order by a.create_time DESC
  </select>
  <select id="getMaintenanceLossAuditPriceCount"
          resultType="com.extracme.evcard.mtc.bo.MaintenanceLossAuditPriceCountBO">
    SELECT
    <if test=" lAPricelevel==0 or lAPricelevel==1">
      IFNULL(sum(case when verification_loss_task_schedule = 300 then 1 else 0 end),0) as untreate,
      IFNULL(sum(case when verification_loss_task_schedule = 310 then 1 else 0 end),0) as processing,
      IFNULL(sum(case when examine_level = 1 and verification_loss_task_schedule in (300,310) then 1 else 0 end),0) as
      submit,
    </if>
    <if test=" lAPricelevel>=2">
      IFNULL(sum(case when verification_loss_task_schedule = 300 then 1 else 0 end),0) as untreate,
      IFNULL(sum(case when verification_loss_task_schedule = 310 then 1 else 0 end),0) as processing,
      0 as submit,
    </if>
    <if test=" lAPricelevel>=2 and subordinate ==1 ">
      IFNULL(sum(case when verification_loss_task_schedule = 300 then 1 else 0 end),0) as untreate,
      IFNULL(sum(case when verification_loss_task_schedule = 310 then 1 else 0 end),0) as processing,
      0 as submit,
    </if>
    IFNULL(sum(case when verification_loss_task_schedule = 315 then 1 else 0 end),0) as back,
    IFNULL(sum(case when verification_loss_task_schedule = 320 then 1 else 0 end),0) as completed,
    IFNULL(sum(case when verification_loss_task_schedule = 340 then 1 else 0 end),0) as closed
    from ${mtcSchema}.mtc_repair_task
    <where>
      status = 1 and org_id LIKE CONCAT(#{orgId,jdbcType=VARCHAR}, '%')
      and verification_loss_task_schedule in (300,310,315,320,340)
      <!--<if test=" lAPricelevel==0 or lAPricelevel==1">-->
      <!--and verification_loss_task_schedule in (300,310,320,340)-->
      <!--</if>-->
      <!--<if test=" lAPricelevel>=2 and subordinate ==0 ">-->
      <!--and verification_loss_task_schedule in (300,310,320,340) and examine_level = 1-->
      <!--</if>-->
      <!--<if test=" lAPricelevel>=2 and subordinate ==1 ">-->
      <!--and verification_loss_task_schedule in (300,310,320,340)-->
      <!--</if>-->
      <if test=" orgId != null and orgId != ''">
        and org_id like CONCAT(#{orgId,jdbcType=VARCHAR},'%')
      </if>
      <if test=" vehicleNo != null and vehicleNo != ''">
        and vehicle_no like CONCAT(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test=" vin != null and vin !=''">
        and vin = #{vin,jdbcType=VARCHAR}
      </if>
      <if test=" taskNo != null and taskNo !=''">
        and task_no = #{taskNo,jdbcType=VARCHAR}
      </if>
      <if test=" vehicleModelSeq != null">
        and vehicle_model_seq = #{vehicleModelSeq,jdbcType=BIGINT}
      </if>
      <if test=" repairDepotOrgName != null and repairDepotOrgName != ''">
        and repair_depot_name LIKE CONCAT('%',#{repairDepotOrgName,jdbcType=VARCHAR},'%')
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test=" createTimeStart != null and createTimeStart != ''">
        and task_inflow_time <![CDATA[ >= ]]> #{createTimeStart,jdbcType=TIMESTAMP}
      </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
        and task_inflow_time <![CDATA[ <= ]]> #{createTimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test=" vehicleReciveTimeStart != null and vehicleReciveTimeStart !=''">
        and vehicle_recive_time <![CDATA[ >= ]]> #{vehicleReciveTimeStart,jdbcType=TIMESTAMP}
      </if>
      <if test=" vehicleReciveTimeEnd !='' and  vehicleReciveTimeEnd != null">
        and vehicle_recive_time <![CDATA[ <= ]]> #{vehicleReciveTimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test=" resurveyFlag != null">
        and resurvey_flag = #{resurveyFlag,jdbcType=DECIMAL}
      </if>
      <if test=" advancedAuditLeve != null and advancedAuditLeve != ''">
        <!--<choose>-->
        <!--<when test="advancedAuditLeve == 7">-->
        <!--and verification_loss_task_schedule = 320-->
        <!--</when>-->
        <!--<when test="advancedAuditLeve == 1">-->
        <!--and advanced_audit_leve = 1 and verification_loss_task_schedule &lt;= 310-->
        <!--</when>-->
        <!--<otherwise>-->
        and advanced_audit_leve = #{advancedAuditLeve,jdbcType=VARCHAR}
        <!--</otherwise>-->
        <!--</choose>-->
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
  </select>
      
    
    
    <select id="getRepairTaskView" resultMap="ViewResultMap">
    select
    a.id,
    a.task_no,
    a.current_tache,
    a.confirm_type,
    a.advanced_audit_leve,
    a.org_id,
    a.org_name,
    a.operate_org_id,
    a.operate_org_name,
    a.vehicle_no,
    a.vehicle_model_seq,
    a.vehicle_model_info,
    a.vin,
    a.insurance_company_name,
    a.repair_type_id,
    case when a.repair_type_id = 1 then '事故维修' when  a.repair_type_id=2 then '自费维修' when a.repair_type_id = 3 then '车辆保养' when a.repair_type_id = 7 then '终端维修' when a.repair_type_id = 9 then '短租包修' end as repair_type_name,
    a.repair_grade,
    a.repair_depot_org_id,
    a.repair_depot_id,
    a.repair_depot_name,
    a.repair_depot_sap_code,
    a.repair_depot_type,
    a.task_inflow_time,
    a.verification_loss_check_time,
    a.vehicle_recive_time,
    a.vehicle_repair_time,
    a.vehicle_check_time,
    a.send_repair_time,
    a.expected_repair_days,
    a.expected_repair_complete,
    a.accident_report_number,
    a.repair_flag,
    a.terminal_id,
    a.total_mileage,
    a.mileage,
    a.associated_order,
    a.order_type,
    a.relate_type,
    a.order_remark,
    a.no_deductibles_flag,
    a.driver_name,
    a.driver_tel,
    a.routing_inspection_name,
    a.routing_inspection_tel,
    a.damaged_part_describe,
    a.accident_describe,
    a.trailer_flag,
    a.repair_review_total_amount,
    a.repair_replace_total_amount,
    a.repair_repair_total_amount,
    a.repair_insurance_total_amount,
    a.vehicle_replace_total_amount,
    a.vehicle_repair_total_amount,
    a.vehicle_insurance_total_amount,
    a.resurvey_flag,
    a.resurvey_part,
    a.duty_situation,
    a.recovery_amount,
    a.insurance_amount,
    a.acc_dep_amount,
    a.outage_loss_amount,
    a.vehicle_loss_amount,
    a.trailer_rescue_amount,
    a.maintain_amount,
    a.loss_order_amount,
    a.reassignment_repair_org_id,
    a.reassignment_reasons,
    a.reassignment_reject_reasons,
    a.check_result_flag,
    a.check_unqualified_reason,
    a.maintain_to_repair_flag,
    a.vehicle_transfer_task_schedule,
    a.insurance_quote_task_schedule,
    a.verification_loss_task_schedule,
    a.reassignment_task_schedule,
    a.vehicle_repair_task_schedule,
    a.vehicle_check_task_schedule,
    a.material_collection_task_schedule,
    a.loss_registration_task_schedule,
    a.insurance_pre_review_task_schedule,
    a.settlement_task_schedule,
    a.current_tache,
    a.verification_loss_task_oper_id,
    a.examine_level,
    a.nuclear_loss_reversion_flag,
    a.verification_reject_reasons,
    a.verification_reject_reasons_detail,
    a.over_time_reasons,
    a.repair_total_amount_first,
    a.verification_loss_check_id,
    a.accident_day_time,
    a.origin,
    a.renttype,
    a.take_user_name,
    a.take_user_phone,
    a.take_voucher,
    a.syn_take_time,
    a.close_reason,
    a.create_time,
    a.claims_flag,
    a.estimated_claim_amount,
    a.car_damage_type,
    a.fact_operate_tag,
    a.vehicle_scrape_value,
    a.review_to_sel_fee_flag,
    a.confirm_car_damage_type,
    a.accident_no,
    a.insurance_pre_review_level,
      CASE
      WHEN b.pic_type = 10 THEN
      (

      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS drivingLicense,
     
      CASE
     WHEN b.pic_type = 11 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS policy,
     
     CASE
     WHEN b.pic_type = 12 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS accident,
     
     CASE
     WHEN b.pic_type = 13 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS damageA,
     CASE
     WHEN b.pic_type = 14 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS damageB,
     
     CASE
     WHEN b.pic_type = 15 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS claims,
     
     CASE
     WHEN b.pic_type = 16 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS other,
     
     CASE
     WHEN b.pic_type = 2 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE 
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS repair,
     
     CASE
     WHEN b.pic_type = 1 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE 
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS damagedPart,
     
    CASE
     WHEN b.pic_type = 17 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS checkVideo,
     CASE
     WHEN b.pic_type = 18 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS afterPic,
           
    CASE
      WHEN b.pic_type = 19 THEN
      (
      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS accidentLiabilityConfirmationPicture,
  
    CASE
      WHEN b.pic_type = 20 THEN
      (
      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS insuranceCompanyLossOrderPicture,
      CASE
      WHEN b.pic_type = 21 THEN
      (
      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS ourDriverLicensePicture,
      CASE
      WHEN b.pic_type = 22 THEN
      (
      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS custPicture,
      CASE
      WHEN b.pic_type = 23 THEN
      (

      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS damagedPartV,
      a.pre_review_vehicle_scrape_value,
      a.pre_review_vehicle_scrape_time,
      a.cust_pays_direct,
      a.cust_amount,
      a.user_assumed_amount,
      a.not_user_assumed_amount,
      a.send_repair_time,
      a.self_funded_amount,
      a.property_status,
      a.product_line,
      a.sub_product_line,
      a.verification_reject_level
    from  ${mtcSchema}.mtc_repair_task a
    left join ${mtcSchema}.mtc_vehicle_repair_pic b on a.task_no = b.task_no
    <where>
     a.id= #{id,jdbcType=BIGINT} and a.status = 1
    </where>
    </select>  
     
    <update id="updateByTaskNoReject" parameterType="com.extracme.evcard.mtc.bo.CheckAcceptanceBO">
     update ${mtcSchema}.mtc_repair_task
        <set>  
                         <if test="currentTache != null">
                                current_tache = #{currentTache},
                        </if>
                         <if test="checkResultFlag != null">
                                check_result_flag = #{checkResultFlag},
                        </if>
                         <if test="updateUserId != null">
                                update_oper_id = #{updateUserId},
                        </if>
                         <if test="updateUserName != null">
                                update_oper_name = #{updateUserName},
                        </if>
                         <if test="updateTime != null">
                                update_time = #{updateTime},
                        </if>
                        <if test="remark != null">
                                check_unqualified_reason = #{remark},
                        </if>
                        <if test="vehicleRepairTaskSchedule != null">
                                vehicle_repair_task_schedule = #{vehicleRepairTaskSchedule},
                        </if>
                        <if test="insuranceQuoteTaskSchedule != null">
                                insurance_quote_task_schedule = #{insuranceQuoteTaskSchedule},
                        </if>
                        <if test="carDamageType != null">
                                car_damage_type = #{carDamageType},
                        </if>
                        <if test="vehicleCheckTaskSchedule != null">
                                vehicle_check_task_schedule = #{vehicleCheckTaskSchedule},
                        </if>
                        <if test="custPaysDirect != null">
                                cust_pays_direct = #{custPaysDirect,jdbcType=INTEGER},
                        </if>
                        <if test="custAmount != null">
                                cust_amount = #{custAmount,jdbcType=DECIMAL},
                        </if>
        </set>
        where task_no = #{taskNo} 
    </update>   
    
    <!-- 报表统计一览 -->
    <select id="getAllSettlementReportInfo" parameterType="map" resultType="com.extracme.evcard.mtc.bo.SettlementReportBO">
        SELECT
            b.repair_depot_id,
            b.repair_depot_name repairDepotName,
            b.org_name orgName,
            b.task_no taskNo,
            case when (maintain_to_repair_flag = 1 and repair_type_id != 3) then N'自费维修（原车辆保养）' else b.repair_type_name end as repairTypeName,
            b.repair_type_id repairTypeId,
            b.repair_grade repairGrade,
            b.vehicle_no vehicleNo,
            b.vehicle_model_info vehicleModelName,
            b.vin,
            b.create_time taskInflowTime,
            b.vehicle_recive_time vehicleReciveTime,
            b.vehicle_repair_time vehicleRepairFinishTime,
            b.vehicle_check_time vehicleCheckFinishTime,
            b.partName,
            b.partTotalMoney,
            b.repairName,
            b.repairTotalMoney,
            b.totalMoney,
            b.maintain_to_repair_flag,
            b.renttype
        FROM(
            SELECT
                a.repair_depot_id,
                a.repair_depot_name,
                a.org_name,
                a.task_no,
                a.repair_type_name,
                a.repair_grade,
                a.vehicle_no,
                a.vehicle_model_info,
                a.vin,
                a.create_time,
                a.vehicle_recive_time,
                a.vehicle_repair_time,
                a.vehicle_check_time,
                GROUP_CONCAT(DISTINCT b.part_name) partName,
                CASE a.repair_type_id WHEN 1 THEN a.repair_replace_total_amount ELSE a.vehicle_replace_total_amount END partTotalMoney,
                GROUP_CONCAT(DISTINCT c.repair_name) repairName,
                CASE a.repair_type_id WHEN 1 THEN a.repair_repair_total_amount ELSE a.vehicle_repair_total_amount END repairTotalMoney,
                CASE a.repair_type_id WHEN 1 THEN a.repair_insurance_total_amount ELSE a.vehicle_insurance_total_amount END totalMoney,
              a.org_id,
              a.repair_type_id,
              a.vehicle_model_seq,
              a.update_time,
              a.maintain_to_repair_flag,
              a.renttype
            FROM
                ${mtcSchema}.mtc_repair_task a
            LEFT JOIN ${mtcSchema}.mtc_replace_item_detail b FORCE INDEX (idx_task_no) ON a.task_no = b.task_no
            LEFT JOIN ${mtcSchema}.mtc_repair_item_detail c FORCE INDEX (idx_task_no) ON a.task_no = c.task_no
            WHERE
                a.`status` = 1
            AND a.vehicle_check_time IS NOT NULL
            AND a.check_result_flag = 0
            AND a.repair_type_id IN (1, 2, 6,9)
            GROUP BY a.id
            UNION
                SELECT
                    a.repair_depot_id,
                    a.repair_depot_name,
                    a.org_name,
                    a.task_no,
                    a.repair_type_name ,
                    a.repair_grade,
                    a.vehicle_no,
                    a.vehicle_model_info,
                    a.vin,
                    a.create_time,
                    a.vehicle_recive_time,
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    IFNULL(a.maintain_amount,0),
                    a.org_id,
                    a.repair_type_id,
                    a.vehicle_model_seq,
                    a.update_time,
                    a.maintain_to_repair_flag,
                    a.renttype
                FROM
                    ${mtcSchema}.mtc_repair_task a
                WHERE
                    a.`status` = 1
                AND a.insurance_quote_task_schedule = 230

                AND a.repair_type_id = 3
             UNION
                SELECT
                    a.repair_depot_id,
                    a.repair_depot_name,
                    a.org_name,
                    a.task_no,
                    '更换轮胎' ,
                    a.repair_grade,
                    a.vehicle_no,
                    a.vehicle_model_info,
                    a.vin,
                    a.create_time,
                    '',
                    '',
                    '',
                    concat('轮胎*',a.tireNumber),
                    '',
                    '',
                    '',
                    '',
                    a.org_id,
                    a.repair_type_id,
                    a.vehicle_model_seq,
                    a.update_time,
                    a.maintain_to_repair_flag,
                    a.renttype
                FROM
                    ${mtcSchema}.mtc_repair_task a
                WHERE
                    a.`status` = 1
                AND a.repair_type_id = 4
                ) AS b
        <where>
        <if test="orgId!=null and orgId!=''">
            b.org_id like concat(#{orgId},'%')
        </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and b.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and b.maintain_to_repair_flag = 1 and b.repair_type_id != 3
            </if>
        <if test="taskNo!=null and taskNo!=''">
            and b.task_no=#{taskNo}
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and b.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and b.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime!=null and taskInflowStartTime!=''">
            and b.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and b.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and b.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and b.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime!=null and vehicleRepairStartTime!=''">
            and b.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime!=null and vehicleRepairEndTime!=''">
            and b.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime!=null and vehicleCheckStartTime!=''">
            and b.vehicle_check_time &gt;=#{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime!=null and vehicleCheckEndTime!=''">
            and b.vehicle_check_time &lt;=#{vehicleCheckEndTime}
        </if>
        <if test="repairDepotIds!=null and repairDepotIds.size()>0">
             and b.repair_depot_id in 
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
        </if>
        </where>
        ORDER BY
            b.update_time DESC
    </select>
     <!-- 拆分报表一览   3个  开始-->
     <select id="getAllSettlementReportInfoFirst" parameterType="map" resultType="com.extracme.evcard.mtc.bo.SettlementReportBO">
        
            SELECT
                a.repair_depot_id,
                a.repair_depot_name repairDepotName,
                a.org_name orgName,
                a.task_no taskNo,
                case when (maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）' else a.repair_type_name end as repairTypeName,
                a.repair_grade repairGrade,
                a.vehicle_no vehicleNo,
                a.vehicle_model_info vehicleModelName,
                a.vin,
                a.create_time taskInflowTime,
                a.vehicle_recive_time vehicleReciveTime,
                a.vehicle_repair_time vehicleRepairFinishTime,
                a.vehicle_check_time vehicleCheckFinishTime,
                GROUP_CONCAT(DISTINCT b.part_name) partName,
                CASE a.repair_type_id WHEN 1 THEN a.repair_replace_total_amount ELSE a.vehicle_replace_total_amount END partTotalMoney,
                GROUP_CONCAT(DISTINCT c.repair_name) repairName,
                CASE a.repair_type_id WHEN 1 THEN a.repair_repair_total_amount ELSE a.vehicle_repair_total_amount END repairTotalMoney,
                CASE a.repair_type_id WHEN 1 THEN a.repair_insurance_total_amount ELSE a.vehicle_insurance_total_amount END totalMoney,
              a.org_id,
              a.repair_type_id repairTypeId,
              a.vehicle_model_seq,
              a.update_time updateTime,
              a.maintain_to_repair_flag,
              a.renttype renttype
            FROM
                ${mtcSchema}.mtc_repair_task a
            LEFT JOIN ${mtcSchema}.mtc_replace_item_detail b FORCE INDEX (idx_task_no) ON a.task_no = b.task_no
            LEFT JOIN ${mtcSchema}.mtc_repair_item_detail c FORCE INDEX (idx_task_no) ON a.task_no = c.task_no
            WHERE
                a.`status` = 1
            AND a.vehicle_check_time IS NOT NULL
            AND a.check_result_flag = 0
            AND a.repair_type_id IN (1, 2, 6,9)
            

        <if test="orgId!=null and orgId!=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
        <if test="taskNo!=null and taskNo!=''">
            and a.task_no=#{taskNo}
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and a.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime!=null and taskInflowStartTime!=''">
            and a.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and a.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and a.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and a.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime!=null and vehicleRepairStartTime!=''">
            and a.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime!=null and vehicleRepairEndTime!=''">
            and a.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime!=null and vehicleCheckStartTime!=''">
            and a.vehicle_check_time &gt;=#{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime!=null and vehicleCheckEndTime!=''">
            and a.vehicle_check_time &lt;=#{vehicleCheckEndTime}
        </if>
         <if test="repairDepotIds!=null and repairDepotIds.size()>0">
             and a.repair_depot_id in 
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
        </if>
         <if test="renttype != null">
             and a.renttype = #{renttype}
         </if>

        GROUP BY a.id
        ORDER BY
            a.update_time DESC
    </select>
    
    <select id="getAllSettlementReportInfoTwo" parameterType="map" resultType="com.extracme.evcard.mtc.bo.SettlementReportBO">
        
                SELECT
                    a.repair_depot_id,
                    a.repair_depot_name repairDepotName,
                    a.org_name orgName,
                    a.task_no taskNo,
                    case when (maintain_to_repair_flag = 1 and repair_type_id != 3) then N'自费维修（原车辆保养）' else a.repair_type_name end as repairTypeName ,
                    a.repair_grade repairGrade,
                    a.vehicle_no vehicleNo,
                    a.vehicle_model_info vehicleModelName,
                    a.vin,
                    a.create_time taskInflowTime,
                    a.vehicle_recive_time vehicleReciveTime,
                    '' as vehicleRepairFinishTime,
                    '' as vehicleCheckFinishTime,
                    '' as partName,
                    '' as partTotalMoney,
                    '' as repairName,
                    '' as repairTotalMoney,
                    IFNULL(a.maintain_amount,0) as totalMoney,
                    a.org_id,
                    a.repair_type_id repairTypeId,
                    a.vehicle_model_seq,
                    a.update_time updateTime,
                    a.maintain_to_repair_flag,
                    a.renttype renttype
                FROM
                    ${mtcSchema}.mtc_repair_task a
                WHERE
                    a.`status` = 1
                AND a.insurance_quote_task_schedule = 230

                AND a.repair_type_id = 3

        <if test="orgId!=null and orgId!=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
        <if test="taskNo!=null and taskNo!=''">
            and a.task_no=#{taskNo}
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and a.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime!=null and taskInflowStartTime!=''">
            and a.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and a.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and a.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and a.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime!=null and vehicleRepairStartTime!=''">
            and a.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime!=null and vehicleRepairEndTime!=''">
            and a.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime!=null and vehicleCheckStartTime!=''">
            and a.vehicle_check_time &gt;=#{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime!=null and vehicleCheckEndTime!=''">
            and a.vehicle_check_time &lt;=#{vehicleCheckEndTime}
        </if>
        <if test="repairDepotIds!=null and repairDepotIds.size()>0">
             and a.repair_depot_id in 
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        ORDER BY
            a.update_time DESC
    </select>
    
    <select id="getAllSettlementReportInfoThree" parameterType="map" resultType="com.extracme.evcard.mtc.bo.SettlementReportBO">
        
                SELECT
                    a.repair_depot_id,
                    a.repair_depot_name repairDepotName,
                    a.org_name orgName,
                    a.task_no taskNo,
                    case when (maintain_to_repair_flag = 1 and repair_type_id != 3) then N'自费维修（原车辆保养）' else '更换轮胎' end as repairTypeName,
                    a.repair_grade repairGrade,
                    a.vehicle_no vehicleNo,
                    a.vehicle_model_info vehicleModelName,
                    a.vin,
                    a.create_time taskInflowTime,
                    '' as vehicleReciveTime,
                    '' as vehicleRepairFinishTime,
                    '' as vehicleCheckFinishTime,
                    concat('轮胎*',a.tireNumber) as partName,
                    '' as partTotalMoney,
                    '' as repairName,
                    '' as repairTotalMoney,
                    '' as totalMoney,
                    a.org_id,
                    a.repair_type_id repairTypeId,
                    a.vehicle_model_seq,
                    a.update_time updateTime,
                    a.maintain_to_repair_flag,
                    a.renttype renttype
                FROM
                    ${mtcSchema}.mtc_repair_task a
                WHERE
                    a.`status` = 1
                AND a.repair_type_id = 4

        <if test="orgId!=null and orgId!=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
        <if test="taskNo!=null and taskNo!=''">
            and a.task_no=#{taskNo}
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and a.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime!=null and taskInflowStartTime!=''">
            and a.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and a.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and a.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and a.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime!=null and vehicleRepairStartTime!=''">
            and a.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime!=null and vehicleRepairEndTime!=''">
            and a.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime!=null and vehicleCheckStartTime!=''">
            and a.vehicle_check_time &gt;=#{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime!=null and vehicleCheckEndTime!=''">
            and a.vehicle_check_time &lt;=#{vehicleCheckEndTime}
        </if>
        <if test="repairDepotIds!=null and repairDepotIds.size()>0">
             and a.repair_depot_id in 
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        ORDER BY
            a.update_time DESC
    </select>
    
    
     <!-- 拆分报表一览   3个  结束-->
     
    <!-- 查询报表统计总条数 -->
    <select id="getAllSettlementReportInfoNum" parameterType="map" resultType="integer">
        SELECT
            count(*)
        FROM(
            SELECT
                a.repair_depot_id,
                a.org_name,
                a.task_no,
                a.repair_type_name,
                a.repair_grade,
                a.vehicle_no,
                a.vehicle_model_info,
                a.vin,
                a.create_time,
                a.vehicle_recive_time,
                a.vehicle_repair_time,
                a.vehicle_check_time,
                a.org_id,
                a.repair_type_id,
                a.vehicle_model_seq,
                a.maintain_to_repair_flag
            FROM
                ${mtcSchema}.mtc_repair_task a
            WHERE
                a.`status` = 1
            AND a.vehicle_check_time IS NOT NULL
            AND a.check_result_flag = 0
            AND a.repair_type_id IN (1, 2, 6,9)
            GROUP BY a.id
            UNION
                SELECT
                    a.repair_depot_id,
                    a.org_name,
                    a.task_no,
                    a.repair_type_name ,
                    a.repair_grade,
                    a.vehicle_no,
                    a.vehicle_model_info,
                    a.vin,
                    a.create_time,
                    a.vehicle_recive_time,
                    '',
                    '',
                    a.org_id,
                    a.repair_type_id,
                    a.vehicle_model_seq,
                    a.maintain_to_repair_flag
                FROM
                    ${mtcSchema}.mtc_repair_task a
                WHERE
                    a.`status` = 1
                AND a.insurance_quote_task_schedule = 230
                AND a.repair_type_id = 3
                UNION
                SELECT
                    a.repair_depot_id,
                    a.org_name,
                    a.task_no,
                    a.repair_type_name ,
                    a.repair_grade,
                    a.vehicle_no,
                    a.vehicle_model_info,
                    a.vin,
                    a.create_time,
                    '',
                    '',
                    '',
                    a.org_id,
                    a.repair_type_id,
                    a.vehicle_model_seq,
                    a.maintain_to_repair_flag
                FROM
                    ${mtcSchema}.mtc_repair_task a
                WHERE
                    a.`status` = 1
                AND a.repair_type_id = 4
                ) AS b
        <where>
        <if test="orgId!=null and orgId!=''">
            b.org_id like concat(#{orgId},'%')
        </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and b.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and b.maintain_to_repair_flag = 1 and b.repair_type_id != 3
            </if>
        <if test="taskNo!=null and taskNo!=''">
            and b.task_no=#{taskNo}
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and b.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and b.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime!=null and taskInflowStartTime!=''">
            and b.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and b.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and b.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and b.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime!=null and vehicleRepairStartTime!=''">
            and b.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime!=null and vehicleRepairEndTime!=''">
            and b.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime!=null and vehicleCheckStartTime!=''">
            and b.vehicle_check_time &gt;=#{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime!=null and vehicleCheckEndTime!=''">
            and b.vehicle_check_time &lt;=#{vehicleCheckEndTime}
        </if>
        <if test="repairDepotIds!=null and repairDepotIds.size()>0">
             and b.repair_depot_id in 
            <foreach item="id" index="index" collection="repairDepotIds" open="("  separator="," close=")">#{id}</foreach>
        </if>
        </where>
    </select>
  <!-- 查询结算报表 -->
  <select id="getSettlementReportInfo" parameterType="com.extracme.evcard.mtc.bo.SettlementReportInfoQueryBO"
          resultType="com.extracme.evcard.mtc.bo.SettlementReportBO">
    select
      a.repair_depot_id,
      b.repair_depot_name repairDepotName,
      a.org_name orgName,
      a.operate_org_name operateOrgName,
      a.task_no taskNo,
      if(maintain_to_repair_flag = 1 and repair_type_id != 3, '自费维修（原车辆保养）', a.repair_type_name) as repairTypeName,
      a.repair_grade repairGrade,
      a.vehicle_no vehicleNo,
      a.vehicle_model_info vehicleModelName,
      a.vin,
      a.create_time taskInflowTime,
      a.vehicle_recive_time vehicleReciveTime,
      a.verification_loss_check_time verificationLossCheckTime,
      a.vehicle_repair_time vehicleRepairFinishTime,
      a.vehicle_check_time vehicleCheckFinishTime,
      a.vehicle_replace_total_amount partTotalMoney,
      a.vehicle_repair_total_amount repairTotalMoney,
      a.vehicle_insurance_total_amount totalMoney,
      a.vehicle_insurance_total_amount - (a.vehicle_replace_total_amount + a.vehicle_repair_total_amount) as otherMoney,
      a.user_assumed_amount userAssumedAmount,
      a.self_funded_amount selfFundedAmount,
      a.repair_fax_settle_amount totalMoneyPreTax,
      a.org_id,
      a.repair_type_id repairTypeId,
      a.vehicle_model_seq,
      a.update_time updateTime,
      a.maintain_to_repair_flag,
      a.renttype renttype,
      ifnull(a.maintain_amount, 0) as maintainAmount,
      a.tireNumber tireNumber,
      a.total_mileage as totalMileage,
      a.no_deductibles_flag as noDeductiblesFlag,
      a.fact_operate_tag as factOperateTag,
      a.vehicle_scrape_value as vehicleScrapeValue,
      a.review_to_sel_fee_flag as reviewToSelFeeFlag,
      a.associated_order as associatedOrder,
      a.settlement_no as settlementNo
    from ${mtcSchema}.mtc_repair_task a
    left join ${mtcSchema}.mtc_repair_depot_info b on a.repair_depot_id = b.repair_depot_id
    where
    a.`status` = 1
    and (
    (a.repair_type_id in (1, 2, 3, 6, 7,9) and a.vehicle_check_time is not null and a.check_result_flag = 0)
    or
    (a.repair_type_id = 4)
    )
    <if test="orgId != null and orgId != ''">
      and a.org_id like concat(#{orgId},'%')
    </if>
    <if test="repairTypeId != null and repairTypeId != 5">
      and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
    </if>
    <if test="repairTypeId == 5">
      and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
    </if>
    <if test="taskNo != null and taskNo != ''">
      and a.task_no=#{taskNo}
    </if>
    <if test="vehicleNo != null and vehicleNo != ''">
      and a.vehicle_no like concat(#{vehicleNo},'%')
    </if>
    <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
      and a.vehicle_model_seq = #{vehicleModelSeq}
    </if>
    <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
      and a.create_time &gt;= #{taskInflowStartTime}
    </if>
    <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
      and a.create_time &lt;= #{taskInflowEndTime}
    </if>
    <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
      and a.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
    </if>
    <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
      and a.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
    </if>
    <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
      and a.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
    </if>
    <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
      and a.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
    </if>
    <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
      and a.vehicle_check_time &gt;=#{vehicleCheckStartTime}
    </if>
    <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
      and a.vehicle_check_time &lt;=#{vehicleCheckEndTime}
    </if>
    <if test="verificationLossCheckStartTime != null and verificationLossCheckStartTime != ''">
      and a.verification_loss_check_time &gt;=#{verificationLossCheckStartTime}
    </if>
    <if test="verificationLossCheckEndTime != null and verificationLossCheckStartTime != ''">
      and a.verification_loss_check_time &lt;=#{verificationLossCheckEndTime}
    </if>
    <if test="repairDepotIds != null and repairDepotIds.size() > 0">
      and a.repair_depot_id in
      <foreach item="item" index="index" collection="repairDepotIds" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="renttype != null">
      and a.renttype = #{renttype}
    </if>
    <if test="factOperateTag != null">
      and a.fact_operate_tag = #{factOperateTag}
    </if>
    <if test="renttypeList != null">
      <if test="renttypeList.size() > 0">
        and a.renttype in
        <foreach collection="renttypeList" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="renttypeList.size() == 0">
        and a.renttype = -2
      </if>
    </if>
    <if test="noDeductiblesFlag != null or noDeductiblesFlag == 0">
      and a.no_deductibles_flag = #{noDeductiblesFlag}
    </if>
    <if test="accidentNo != null and accidentNo != ''">
      and a.accident_no = #{accidentNo}
    </if>
    <if test="reviewToSelFeeFlag != null">
      and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
    </if>
    order by
    a.update_time desc
  </select>
    
    <update id="updateRepairTask" parameterType="com.extracme.evcard.mtc.model.RepairTask">
        update ${mtcSchema}.mtc_repair_task
        <set>
          <if test="entityMap.taskNo != null">
                  task_no = #{entityMap.taskNo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.orgId != null">
                  org_id = #{entityMap.orgId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.orgName != null">
                  org_name = #{entityMap.orgName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vehicleNo != null">
                  vehicle_no = #{entityMap.vehicleNo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vehicleModelSeq != null">
                  vehicle_model_seq = #{entityMap.vehicleModelSeq,jdbcType=BIGINT},
          </if>
          <if test="entityMap.vehicleModelInfo != null">
                  vehicle_model_info = #{entityMap.vehicleModelInfo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vin != null">
                  vin = #{entityMap.vin,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.insuranceCompanyName != null">
                  insurance_company_name = #{entityMap.insuranceCompanyName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairTypeId != null">
                  repair_type_id = #{entityMap.repairTypeId,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.repairTypeName != null">
                  repair_type_name = #{entityMap.repairTypeName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairGrade != null">
                  repair_grade = #{entityMap.repairGrade,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairDepotId != null">
                  repair_depot_id = #{entityMap.repairDepotId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.repairDepotName != null">
                  repair_depot_name = #{entityMap.repairDepotName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairDepotOrgId != null">
                  repair_depot_org_id = #{entityMap.repairDepotOrgId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.taskInflowTime != null">
                  task_inflow_time = #{entityMap.taskInflowTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.vehicleReciveTime != null">
                  vehicle_recive_time = #{entityMap.vehicleReciveTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.vehicleCheckTime != null">
                  vehicle_check_time = #{entityMap.vehicleCheckTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.vehicleRepairTime != null">
                  vehicle_repair_time = #{entityMap.vehicleRepairTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.expectedRepairDays != null">
                  expected_repair_days = #{entityMap.expectedRepairDays,jdbcType=BIGINT},
          </if>
          <if test="entityMap.expectedRepairComplete != null">
                  expected_repair_complete = #{entityMap.expectedRepairComplete,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.accidentReportNumber != null">
                  accident_report_number = #{entityMap.accidentReportNumber,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairFlag != null">
                  repair_flag = #{entityMap.repairFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.terminalId != null">
                  terminal_id = #{entityMap.terminalId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.totalMileage != null">
                  total_mileage = #{entityMap.totalMileage,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.associatedOrder != null">
                  associated_order = #{entityMap.associatedOrder,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.noDeductiblesFlag != null">
                  no_deductibles_flag = #{entityMap.noDeductiblesFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.driverName != null">
                  driver_name = #{entityMap.driverName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.driverTel != null">
                  driver_tel = #{entityMap.driverTel,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.routingInspectionName != null">
                  routing_inspection_name = #{entityMap.routingInspectionName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.routingInspectionTel != null">
                  routing_inspection_tel = #{entityMap.routingInspectionTel,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.damagedPartDescribe != null">
                  damaged_part_describe = #{entityMap.damagedPartDescribe,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.accidentDescribe != null">
                  accident_describe = #{entityMap.accidentDescribe,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.trailerFlag != null">
                  trailer_flag = #{entityMap.trailerFlag,jdbcType=DECIMAL},
          </if>
           <if test="entityMap.repairReplaceTotalAmount != null">
                  repair_replace_total_amount = #{entityMap.repairReplaceTotalAmount,jdbcType=DECIMAL},
           </if>
           <if test="entityMap.repairRepairTotalAmount != null">
                  repair_repair_total_amount = #{entityMap.repairRepairTotalAmount,jdbcType=DECIMAL},
           </if>
           <if test="entityMap.repairInsuranceTotalAmount != null">       
                  repair_insurance_total_amount = #{entityMap.repairInsuranceTotalAmount,jdbcType=DECIMAL},
           </if>
           <if test="entityMap.vehicleReplaceTotalAmount != null">        
                  vehicle_replace_total_amount = #{entityMap.vehicleReplaceTotalAmount,jdbcType=DECIMAL},
           </if>
           <if test="entityMap.vehicleRepairTotalAmount != null">       
                  vehicle_repair_total_amount = #{entityMap.vehicleRepairTotalAmount,jdbcType=DECIMAL},
           </if>
           <if test="entityMap.vehicleInsuranceTotalAmount != null">       
                  vehicle_insurance_total_amount = #{entityMap.vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
           </if>       
          <if test="entityMap.vehicleManageViewFlag != null">
                  vehicle_manage_view_flag = #{entityMap.vehicleManageViewFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.resurveyFlag != null">
                  resurvey_flag = #{entityMap.resurveyFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.resurveyPart != null">
                  resurvey_part = #{entityMap.resurveyPart,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.dutySituation != null">
                  duty_situation = #{entityMap.dutySituation,jdbcType=DECIMAL},
          </if>
                  recovery_amount = #{entityMap.recoveryAmount,jdbcType=DECIMAL},
                  insurance_amount = #{entityMap.insuranceAmount,jdbcType=DECIMAL},
                  acc_dep_amount = #{entityMap.accDepAmount,jdbcType=DECIMAL},
                  outage_loss_amount = #{entityMap.outageLossAmount,jdbcType=DECIMAL},
                  vehicle_loss_amount = #{entityMap.vehicleLossAmount,jdbcType=DECIMAL},
                  trailer_rescue_amount = #{entityMap.trailerRescueAmount,jdbcType=DECIMAL},
                  maintain_amount = #{entityMap.maintainAmount,jdbcType=DECIMAL},
          <if test="entityMap.lossOrderAmount != null">
                loss_order_amount = #{entityMap.lossOrderAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.reassignmentRepairOrgId != null">
                  reassignment_repair_org_id = #{entityMap.reassignmentRepairOrgId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.reassignmentReasons != null">
                  reassignment_reasons = #{entityMap.reassignmentReasons,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.reassignmentRejectReasons != null">
                  reassignment_reject_reasons = #{entityMap.reassignmentRejectReasons,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.checkResultFlag != null">
                  check_result_flag = #{entityMap.checkResultFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.checkUnqualifiedReason != null">
                  check_unqualified_reason = #{entityMap.checkUnqualifiedReason,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vehicleTransferTaskSchedule != null">
                  vehicle_transfer_task_schedule = #{entityMap.vehicleTransferTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.insuranceQuoteTaskSchedule != null">
                  insurance_quote_task_schedule = #{entityMap.insuranceQuoteTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.verificationLossTaskSchedule != null">
                  verification_loss_task_schedule = #{entityMap.verificationLossTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.reassignmentTaskSchedule != null">
                  reassignment_task_schedule = #{entityMap.reassignmentTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.vehicleRepairTaskSchedule != null">
                  vehicle_repair_task_schedule = #{entityMap.vehicleRepairTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.vehicleCheckTaskSchedule != null">
                  vehicle_check_task_schedule = #{entityMap.vehicleCheckTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.materialCollectionTaskSchedule != null">
                  material_collection_task_schedule = #{entityMap.materialCollectionTaskSchedule,jdbcType=INTEGER},
          </if>
          <if test="entityMap.lossRegistrationTaskSchedule != null">
                  loss_registration_task_schedule = #{entityMap.lossRegistrationTaskSchedule,jdbcType=INTEGER},
          </if>
          <if test="entityMap.settlementTaskSchedule != null">
                  settlement_task_schedule = #{entityMap.settlementTaskSchedule,jdbcType=INTEGER},
          </if>
          <if test="entityMap.currentTache != null">
                  current_tache = #{entityMap.currentTache,jdbcType=BIGINT},
          </if>
          <if test="entityMap.confirmType != null">
                  confirm_type = #{entityMap.confirmType,jdbcType=INTEGER},
          </if>
          <if test="entityMap.advancedAuditLeve != null">
                  advanced_audit_leve = #{entityMap.advancedAuditLeve,jdbcType=BIT},
          </if>
          <if test="entityMap.maintainToRepairFlag != null">
                  maintain_to_repair_flag = #{entityMap.maintainToRepairFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.verificationLossTaskOperId != null">
                  verification_loss_task_oper_id = #{entityMap.verificationLossTaskOperId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.verificationLossTaskOperId == null">
                  verification_loss_task_oper_id = null,
          </if>
          <if test="entityMap.repairTotalAmountFirst != null">
                  repair_total_amount_first = #{entityMap.repairTotalAmountFirst,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.nuclearLossReversionFlag != null">
                  nuclear_loss_reversion_flag = #{entityMap.nuclearLossReversionFlag,jdbcType=DECIMAL},
          </if>
                  over_time_reasons = #{entityMap.overTimeReasons,jdbcType=VARCHAR},
          <if test="entityMap.verificationLossCheckTime != null">
                  verification_loss_check_time = #{entityMap.verificationLossCheckTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.verificationLossCheckId != null">
                  verification_loss_check_id = #{entityMap.verificationLossCheckId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.vehicleScrapeValue != null">
            vehicle_scrape_value = #{entityMap.vehicleScrapeValue,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.status != null">
                  status = #{entityMap.status,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.remark != null">
                  remark = #{entityMap.remark,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.createOperId != null">
                  create_oper_id = #{entityMap.createOperId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.createOperName != null">
                  create_oper_name = #{entityMap.createOperName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.updateTime != null">
                  update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.updateOperId != null">
                  update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.updateOperName != null">
                  update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.estimatedClaimAmount != null">
            estimated_claim_amount = #{entityMap.estimatedClaimAmount, jdbcType=DECIMAL},
          </if>
          <if test="entityMap.confirmCarDamageType != null">
            confirm_car_damage_type = #{entityMap.confirmCarDamageType, jdbcType=INTEGER},
          </if>
          <if test="entityMap.accidentNo != null">
            accident_no = #{entityMap.accidentNo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.custPaysDirect != null">
            cust_pays_direct = #{entityMap.custPaysDirect,jdbcType=INTEGER},
          </if>
          <if test="entityMap.custAmount != null">
            cust_amount = #{entityMap.custAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.userAssumedAmount != null">
            user_assumed_amount = #{entityMap.userAssumedAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.notUserAssumedAmount != null">
            not_user_assumed_amount = #{entityMap.notUserAssumedAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.selfFundedAmount != null">
            self_funded_amount = #{entityMap.selfFundedAmount,jdbcType=DECIMAL},
          </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
        <if test="entityMap.controlStatusCurrentTache != null ">
            and current_tache = #{entityMap.controlStatusCurrentTache,jdbcType=BIGINT}
        </if>

    </update>

    <update id="updateRepairManual" parameterType="com.extracme.evcard.mtc.model.RepairTask">
    update ${mtcSchema}.mtc_repair_task
    <set>
        <if test="claimsFlag != null">
            claims_flag = #{claimsFlag,jdbcType=INTEGER},
        </if>
        <if test="estimatedClaimAmount != null">
            estimated_claim_amount = #{estimatedClaimAmount,jdbcType=DECIMAL},
        </if>
        <if test="vehicleRepairCostAmount != null">
            vehicle_repair_cost_amount = #{vehicleRepairCostAmount,jdbcType=DECIMAL},
        </if>
        <if test="repairSettleAmount != null">
            repair_settle_amount = #{repairSettleAmount,jdbcType=DECIMAL},
        </if>
        <if test="repairFaxSettleAmount != null">
            repair_fax_settle_amount = #{repairFaxSettleAmount,jdbcType=DECIMAL},
        </if>
        <if test="repairDepotSapCode != null">
            repair_depot_sap_code = #{repairDepotSapCode,jdbcType=VARCHAR},
        </if>
    </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    
    <update id="updateTransferTask" parameterType="com.extracme.evcard.mtc.model.RepairTask">
     update ${mtcSchema}.mtc_repair_task
         <set>
             
                        <if test="entityMap.taskNo != null">
                                task_no = #{entityMap.taskNo,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.orgId != null">
                                org_id = #{entityMap.orgId,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.orgName != null">
                                org_name = #{entityMap.orgName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehicleNo != null">
                                vehicle_no = #{entityMap.vehicleNo,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehicleModelSeq != null">
                                vehicle_model_seq = #{entityMap.vehicleModelSeq,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.vehicleModelInfo != null">
                                vehicle_model_info = #{entityMap.vehicleModelInfo,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vin != null">
                                vin = #{entityMap.vin,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.insuranceCompanyName != null">
                                insurance_company_name = #{entityMap.insuranceCompanyName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairTypeId != null">
                                repair_type_id = #{entityMap.repairTypeId,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.repairTypeName != null">
                                repair_type_name = #{entityMap.repairTypeName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairGrade != null">
                                repair_grade = #{entityMap.repairGrade,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairDepotId != null">
                                repair_depot_id = #{entityMap.repairDepotId,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.repairDepotName != null">
                                repair_depot_name = #{entityMap.repairDepotName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairDepotOrgId != null">
                                repair_depot_org_id = #{entityMap.repairDepotOrgId,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.taskInflowTime != null">
                                task_inflow_time = #{entityMap.taskInflowTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.vehicleReciveTime != null">
                                vehicle_recive_time = #{entityMap.vehicleReciveTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.vehicleCheckTime != null">
                                vehicle_check_time = #{entityMap.vehicleCheckTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.vehicleRepairTime != null">
                                vehicle_repair_time = #{entityMap.vehicleRepairTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.expectedRepairDays != null">
                                expected_repair_days = #{entityMap.expectedRepairDays,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.expectedRepairComplete != null">
                                expected_repair_complete = #{entityMap.expectedRepairComplete,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.accidentReportNumber != null">
                                accident_report_number = #{entityMap.accidentReportNumber,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.repairFlag != null">
                                repair_flag = #{entityMap.repairFlag,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.terminalId != null">
                                terminal_id = #{entityMap.terminalId,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.totalMileage != null">
                                total_mileage = #{entityMap.totalMileage,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.associatedOrder != null">
                                associated_order = #{entityMap.associatedOrder,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.noDeductiblesFlag != null">
                                no_deductibles_flag = #{entityMap.noDeductiblesFlag,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.driverName != null">
                                driver_name = #{entityMap.driverName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.driverTel != null">
                                driver_tel = #{entityMap.driverTel,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.routingInspectionName != null">
                                routing_inspection_name = #{entityMap.routingInspectionName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.routingInspectionTel != null">
                                routing_inspection_tel = #{entityMap.routingInspectionTel,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.damagedPartDescribe != null">
                                damaged_part_describe = #{entityMap.damagedPartDescribe,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.accidentDescribe != null">
                                accident_describe = #{entityMap.accidentDescribe,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.trailerFlag != null">
                                trailer_flag = #{entityMap.trailerFlag,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.repairReplaceTotalAmount != null">
                                repair_replace_total_amount = #{entityMap.repairReplaceTotalAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.repairRepairTotalAmount != null">
                                repair_repair_total_amount = #{entityMap.repairRepairTotalAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.repairInsuranceTotalAmount != null">
                                repair_insurance_total_amount = #{entityMap.repairInsuranceTotalAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.vehicleReplaceTotalAmount != null">
                                vehicle_replace_total_amount = #{entityMap.vehicleReplaceTotalAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.vehicleRepairTotalAmount != null">
                                vehicle_repair_total_amount = #{entityMap.vehicleRepairTotalAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.vehicleInsuranceTotalAmount != null">
                                vehicle_insurance_total_amount = #{entityMap.vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.vehicleManageViewFlag != null">
                                vehicle_manage_view_flag = #{entityMap.vehicleManageViewFlag,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.resurveyFlag != null">
                                resurvey_flag = #{entityMap.resurveyFlag,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.resurveyPart != null">
                                resurvey_part = #{entityMap.resurveyPart,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.dutySituation != null">
                                duty_situation = #{entityMap.dutySituation,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.recoveryAmount != null">
                                recovery_amount = #{entityMap.recoveryAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.insuranceAmount != null">
                                insurance_amount = #{entityMap.insuranceAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.accDepAmount != null">
                                acc_dep_amount = #{entityMap.accDepAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.outageLossAmount != null">
                                outage_loss_amount = #{entityMap.outageLossAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.vehicleLossAmount != null">
                                vehicle_loss_amount = #{entityMap.vehicleLossAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.trailerRescueAmount != null">
                                trailer_rescue_amount = #{entityMap.trailerRescueAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.maintainAmount != null">
                                maintain_amount = #{entityMap.maintainAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.reassignmentRepairOrgId != null">
                                reassignment_repair_org_id = #{entityMap.reassignmentRepairOrgId,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.reassignmentReasons != null">
                                reassignment_reasons = #{entityMap.reassignmentReasons,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.reassignmentRejectReasons != null">
                                reassignment_reject_reasons = #{entityMap.reassignmentRejectReasons,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.verificationRejectReasonsDetail != null">
                                verification_reject_reasons_detail = #{entityMap.verificationRejectReasonsDetail,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.checkResultFlag != null">
                                check_result_flag = #{entityMap.checkResultFlag,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.checkUnqualifiedReason != null">
                                check_unqualified_reason = #{entityMap.checkUnqualifiedReason,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.verificationRejectReasons != null">
                                verification_reject_reasons = #{entityMap.verificationRejectReasons,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.vehicleTransferTaskSchedule != null">
                                vehicle_transfer_task_schedule = #{entityMap.vehicleTransferTaskSchedule,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.insuranceQuoteTaskSchedule != null">
                                insurance_quote_task_schedule = #{entityMap.insuranceQuoteTaskSchedule,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.verificationLossTaskSchedule != null">
                                verification_loss_task_schedule = #{entityMap.verificationLossTaskSchedule,jdbcType=BIGINT},
                         </if>       
                        <if test="entityMap.reassignmentTaskSchedule != null">
                                reassignment_task_schedule = #{entityMap.reassignmentTaskSchedule,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.vehicleRepairTaskSchedule != null">
                                vehicle_repair_task_schedule = #{entityMap.vehicleRepairTaskSchedule,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.vehicleCheckTaskSchedule != null">
                                vehicle_check_task_schedule = #{entityMap.vehicleCheckTaskSchedule,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.currentTache != null">
                                current_tache = #{entityMap.currentTache,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.maintainToRepairFlag != null">
                                maintain_to_repair_flag = #{entityMap.maintainToRepairFlag,jdbcType=DECIMAL},
                        </if>
                        <choose>
                          <when test="entityMap.verificationLossTaskOperId !=null">
                            verification_loss_task_oper_id = #{entityMap.verificationLossTaskOperId,jdbcType=BIGINT},
                          </when>
                          <otherwise>
                            verification_loss_task_oper_id = null,
                          </otherwise>
                        </choose>
                        <if test="entityMap.examineLevel != null">
                                examine_level = #{entityMap.examineLevel,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.advancedAuditLeve != null">
                                advanced_audit_leve = #{entityMap.advancedAuditLeve,jdbcType=BIT},
                        </if>
                        <if test="entityMap.nuclearLossReversionFlag != null">
                                nuclear_loss_reversion_flag = #{entityMap.nuclearLossReversionFlag,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.verificationLossCheckTime != null">
                                verification_loss_check_time = #{entityMap.verificationLossCheckTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.verificationLossCheckId != null">
                                verification_loss_check_id = #{entityMap.verificationLossCheckId,jdbcType=BIGINT},
                        </if>         
                        <if test="entityMap.status != null">
                                status = #{entityMap.status,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.remark != null">
                                remark = #{entityMap.remark,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.custPaysDirect != null">
                                cust_pays_direct = #{entityMap.custPaysDirect,jdbcType=INTEGER},
                        </if>
                        <if test="entityMap.custAmount != null">
                                cust_amount = #{entityMap.custAmount,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.createOperId != null">
                                create_oper_id = #{entityMap.createOperId,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.createOperName != null">
                                create_oper_name = #{entityMap.createOperName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.updateTime != null">
                                update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.updateOperId != null">
                                update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.updateOperName != null">
                                update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.verificationRejectLevel != null">
                          verification_reject_level = #{entityMap.verificationRejectLevel,jdbcType=VARCHAR}
                        </if>
          </set>
          where id = #{id,jdbcType=BIGINT}
          <if test="entityMap.controlStatusCurrentTache != null ">
            and current_tache = #{entityMap.controlStatusCurrentTache,jdbcType=BIGINT}
          </if>
    </update>
    
    
    
    <select id="queryRepairTaskCompleted" parameterType="string" resultType="java.lang.Integer">
          SELECT
                COUNT(*)
            FROM
                ${mtcSchema}.mtc_repair_task
            WHERE
                repair_depot_id = #{repairId}
            AND vehicle_check_task_schedule = 620 and status = 1
    </select>
    
    <select id="queryReapirTask" parameterType="string" resultType="java.lang.Integer">
          SELECT
                COUNT(*)
            FROM
                ${mtcSchema}.mtc_repair_task
            WHERE
                repair_depot_id = #{repairId} and status = 1
    </select>
  <update id="acceptVehicleJoin" parameterType="com.extracme.evcard.mtc.bo.VehicleJoinRequestBO">
    UPDATE ${mtcSchema}.mtc_repair_task
    SET
    <if test="isUsedApplets != null and isUsedApplets != ''">
      is_used_applets = #{isUsedApplets,jdbcType=VARCHAR},
    </if>
    vehicle_transfer_task_schedule = 110,
    insurance_quote_task_schedule = 200,
    current_tache =20,
    vehicle_recive_time=NOW(),
    update_time = NOW(),
    update_oper_id = #{updateOperId},
    update_oper_name = #{updateOperName}
    WHERE
    task_no =#{taskNo}
  </update>
  <update id="acceptRepairVehicleJoin" parameterType="com.extracme.evcard.mtc.bo.VehicleJoinRequestBO">
    UPDATE ${mtcSchema}.mtc_repair_task
    SET
      <if test="isUsedApplets != null and isUsedApplets != ''">
        is_used_applets = #{isUsedApplets,jdbcType=VARCHAR},
      </if>
      vehicle_transfer_task_schedule = 110,
      insurance_pre_review_task_schedule = 1100,
      current_tache =110,
      vehicle_recive_time=NOW(),
      update_time = NOW(),
      update_oper_id = #{updateOperId},
      update_oper_name = #{updateOperName}
    WHERE
    task_no =#{taskNo}
  </update>
    
     <select id="queryVehicleJoin" parameterType="com.extracme.evcard.mtc.bo.VehicleJoinQueryBO" resultType="com.extracme.evcard.mtc.bo.VehicleJoinBO">
        select 
            id,
            task_no taskNo,
            org_name orgName,
            vehicle_no vehicleNo,
            vehicle_model_info vehicleModelName,
            vin,
            insurance_company_name insuranceCompanyName,
            repair_type_id repairTypeId,
            repair_type_name repairTypeName,
            repair_grade repairGrade,
            tireNumber,
            repair_depot_name repairDepotName,
            DATE_FORMAT(task_inflow_time,'%Y-%m-%d %H:%i:%s') createTime,
            DATE_FORMAT(vehicle_recive_time,'%Y-%m-%d %H:%i:%s') vehicleReciveTime,
            vehicle_transfer_task_schedule taskSchedule,
             case vehicle_transfer_task_schedule
             when 100 then N'未处理'
             when 110 then N'已完成'
             when 120 then N'已关闭'
             else '' end taskScheduleName,
             DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') as flowTaskTime,
            renttype renttype,
            fact_operate_tag as factOperateTag,
            repair_depot_type as repairDepotType
        from ${mtcSchema}.mtc_repair_task 
        WHERE `status`=1 and vehicle_transfer_task_schedule IN (100,110,130) and repair_type_id in (1,2,3,6,7,9)
        <!-- 修理厂权限 -->
        <if test="repairDepotId != null and repairDepotId != ''">
            and repair_depot_id = #{repairDepotId}
        </if>
        <!-- 运营人员权限 -->
        <if test="repairDepotId == null or repairDepotId == ''">
            and org_id like concat(#{loginOrgId},'%')
        </if>
        <if test="repairDepotId == null or repairDepotId == ''">
          <!-- 仅在 100 状态下要求 is_proxy_operable = 1，其它状态不受限制 -->
          <!-- 车辆接车环节 -->
          and (
            (vehicle_transfer_task_schedule = 100 and is_proxy_operable = 1)
            or (vehicle_transfer_task_schedule != 100)
          )
        </if>
        <if test="orgId!=null and orgId!=''">
            and org_id like concat(#{orgId},'%')
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vin!=null and vin!=''">
            and vin = #{vin}
        </if>
        <if test="taskNo!=null and taskNo!=''">
            and task_no = #{taskNo}
        </if>
        <if test="repairTypeId!=null and repairTypeId!=''">
            and repair_type_id = #{repairTypeId}
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="taskInflowCreateTime!=null and taskInflowCreateTime!=''">
            and task_inflow_time &gt;=DATE_FORMAT(#{taskInflowCreateTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and task_inflow_time &lt;=DATE_FORMAT(#{taskInflowEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="createStartTime!=null and createStartTime!=''">
            and create_time &gt;=DATE_FORMAT(#{createStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="createEndTime!=null and createEndTime!=''">
            and create_time &lt;=DATE_FORMAT(#{createEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and vehicle_recive_time &gt;=DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and vehicle_recive_time &lt;=DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="taskSchedule!=null and taskSchedule!=''">
            and vehicle_transfer_task_schedule = #{taskSchedule}
        </if>
        <if test="renttype != null">
            and renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and fact_operate_tag = #{factOperateTag}
        </if>
         <if test="renttypeList != null">
             <if test="renttypeList.size() > 0">
                 and renttype in
                 <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                     #{item}
                 </foreach>
             </if>
             <if test="renttypeList.size() == 0">
                 and renttype = -2
             </if>
         </if>
        <if test="taskSchedule == '100'.toString() or taskSchedule == '130'.toString()">
            ORDER BY create_time DESC
        </if>
        <if test="taskSchedule == '110'.toString() ">
            ORDER BY vehicle_recive_time DESC
        </if> 
    </select>
    
    <select id="queryReassignmentApply" resultType="com.extracme.evcard.mtc.bo.ReassignmentApplyBO" parameterType="com.extracme.evcard.mtc.bo.ReassignmentApplyQueryBO">
        SELECT
            a.id,
            a.task_no as taskNo,
            a.org_id as orgId,
            a.org_name as orgName,
            a.vehicle_no as vehicleNo,
            a.vehicle_model_seq as vehicleModelSeq,
            a.vehicle_model_info as vehicleModelInfo,
            a.vin as vin,
            a.repair_type_id as repairTypeId,
            case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）' else a.repair_type_name end as repairTypeName,
            a.repair_depot_org_id as repairDepotId,
            a.repair_depot_name as repairDepotName,
            a.vehicle_recive_time as vehicleReciveTime,
            a.task_inflow_time as taskInflowTime,
            a.reassignment_reasons as reassignmentReasons,
            a.reassignment_repair_org_id as reassignmentRepairOrgId,
            b.repair_depot_name as reassignmentRepairOrgName,
            a.reassignment_pass_time as reassignmentPassTime,
            a.reassignment_task_schedule as reassignmentTaskSchedule,
            a.repair_grade as repairGrade,
            a.renttype as renttype,
            a.fact_operate_tag as factOperateTag,
            a.origin as origin
        FROM
            ${mtcSchema}.mtc_repair_task a
        LEFT JOIN ${mtcSchema}.mtc_repair_depot_info b ON a.reassignment_repair_org_id = b.repair_depot_id
        <where>
           1=1  
           and a.status  = 1
           <if test="reassignmentTaskSchedule != null and reassignmentTaskSchedule !=''">
           and a.reassignment_task_schedule = #{reassignmentTaskSchedule}
           </if>
           <if test="orgId != null and orgId !=''">
           and a.org_id like concat(#{orgId},'%')
           </if>
           <if test="vehicleNo != null and vehicleNo !=''">
           and a.vehicle_no like concat(#{vehicleNo},'%')
           </if>
           <if test="taskNo != null and taskNo !=''">
           and a.task_no = #{taskNo}
           </if>
           <if test="vehicleModelSeq != null and vehicleModelSeq !=''">
           and a.vehicle_model_seq = #{vehicleModelSeq}
           </if>
           <if test="reassignmentRepairOrgName != null and reassignmentRepairOrgName !=''">
           and b.repair_depot_name like concat('%',#{reassignmentRepairOrgName},'%')
           </if>
           <if test="startVehicleReciveTime != null and startVehicleReciveTime !=''"> 
           and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endVehicleReciveTime != null and endVehicleReciveTime !=''">
           and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
           </if>
           <if test="vin != null and vin !=''">
           and a.vin = #{vin}
           </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
           <if test="repairDepotName != null and repairDepotName !=''">
           and a.repair_depot_name like concat('%',#{repairDepotName},'%')
           </if>
           <if test="startTaskInflowTime != null and startTaskInflowTime !=''">
           and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endTaskInflowTime != null and endTaskInflowTime !=''">
           and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
           </if>
            <if test="renttype != null">
                and renttype = #{renttype}
            </if>
            <if test="factOperateTag != null">
                and a.fact_operate_tag = #{factOperateTag}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and renttype in
                    <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and renttype = -2
                </if>
            </if>
        </where>
      ORDER BY a.create_time DESC
    </select>
    
    <select id="queryReassignmentApplyCount" parameterType="com.extracme.evcard.mtc.bo.ReassignmentApplyQueryBO" resultType="java.lang.Integer">
      SELECT
            count(*)
        FROM
            ${mtcSchema}.mtc_repair_task a
         LEFT JOIN ${mtcSchema}.mtc_repair_depot_info b ON a.reassignment_repair_org_id = b.repair_depot_id
        <where>
           1=1  and a.status  = 1
           <if test="reassignmentTaskSchedule != null and reassignmentTaskSchedule !=''">
           and a.reassignment_task_schedule = #{reassignmentTaskSchedule}
           </if>
           <if test="orgId != null and orgId !=''">
           and a.org_id like concat(#{orgId},'%')
           </if>
           <if test="vehicleNo != null and vehicleNo !=''">
           and a.vehicle_no like concat(#{vehicleNo},'%')
           </if>
           <if test="taskNo != null and taskNo !=''">
           and a.task_no = #{taskNo}
           </if>
           <if test="vehicleModelSeq != null and vehicleModelSeq !=''">
           and a.vehicle_model_seq = #{vehicleModelSeq}
           </if>
           <if test="reassignmentRepairOrgName != null and reassignmentRepairOrgName !=''">
           and b.repair_depot_name like concat('%',#{reassignmentRepairOrgName},'%')
           </if>
           <if test="startVehicleReciveTime != null and startVehicleReciveTime !=''"> 
           and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endVehicleReciveTime != null and endVehicleReciveTime !=''">
           and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
           </if>
           <if test="vin != null and vin !=''">
           and a.vin = #{vin}
           </if>
           <if test="repairTypeId != null and repairTypeId !='' and repairTypeId != 5 ">
           and a.repair_type_id = #{repairTypeId}
           </if>
           <if test="repairTypeId == 5">
           and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
           </if>
           <if test="repairDepotName != null and repairDepotName !=''">
           and a.repair_depot_name like concat('%',#{repairDepotName},'%')
           </if>
           <if test="startTaskInflowTime != null and startTaskInflowTime !=''">
           and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endTaskInflowTime != null and endTaskInflowTime !=''">
           and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
           </if>
        </where>
    </select>
  
  <update id="updatereassignmentApplyReject" parameterType="com.extracme.evcard.mtc.bo.ReassignmentRejectBO">
    update ${mtcSchema}.mtc_repair_task
    <set>
      <if test="currentTache != null">
        current_tache = #{currentTache},
      </if>
      <if test="reassignmentRejectReasons != null">
        reassignment_reject_reasons = #{reassignmentRejectReasons},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updateTime != null">
        reassignment_pass_time = #{updateTime},
      </if>
      <if test="reassignmentTaskSchedule != null">
        reassignment_task_schedule = #{reassignmentTaskSchedule},
      </if>
      <if test="insuranceQuoteTaskSchedule != null">
        insurance_quote_task_schedule = #{insuranceQuoteTaskSchedule},
      </if>
      <if test="insurancePreReviewTaskSchedule != null">
        insurance_pre_review_task_schedule = #{insurancePreReviewTaskSchedule},
      </if>
      <if test="reassignmentRepairOrgId != null and reassignmentRepairOrgId !=''">
        reassignment_repair_org_id = #{reassignmentRepairOrgId},
      </if>
    </set>
    where task_no = #{taskNo} and `status` != 0
  </update>
    
    <!-- 接车带处理 -->
    <select id="queryVehicleWaiting" parameterType="com.extracme.evcard.mtc.bo.VehicleJoinQueryBO" resultType="java.lang.Long">
          select 
               count(*)
        from ${mtcSchema}.mtc_repair_task 
        WHERE `status`=1 and vehicle_transfer_task_schedule=100 and repair_type_id in (1,2,3,6,7,9)
        <!-- 修理厂权限 -->
        <if test="repairDepotId != null and repairDepotId != ''">
            and repair_depot_id = #{repairDepotId}
        </if>
        <!-- 运营人员权限 -->
        <if test="repairDepotId == null or repairDepotId == ''">
            and org_id like concat(#{loginOrgId},'%')
        </if>
        <if test="repairDepotId == null or repairDepotId == ''">
            and is_proxy_operable = 1
        </if>
        <if test="orgId!=null and orgId!=''">
          and org_id like concat(#{orgId},'%')
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vin!=null and vin!=''">
            and vin = #{vin}
        </if>
        <if test="taskNo!=null and taskNo!=''">
            and task_no = #{taskNo}
        </if>
        <if test="repairTypeId!=null and repairTypeId!=''">
            and repair_type_id = #{repairTypeId}
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
         <if test="taskInflowCreateTime!=null and taskInflowCreateTime!=''">
            and task_inflow_time &gt;=DATE_FORMAT(#{taskInflowCreateTime},'%Y-%m-%d 00:00:00.000')
        </if>
         <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and task_inflow_time &lt;=DATE_FORMAT(#{taskInflowEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
           <if test="createStartTime!=null and createStartTime!=''">
            and create_time &gt;=DATE_FORMAT(#{createStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="createEndTime!=null and createEndTime!=''">
            and create_time &lt;=DATE_FORMAT(#{createEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and vehicle_recive_time &gt;=#{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and vehicle_recive_time &gt;=DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and vehicle_recive_time &lt;=DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="renttype != null">
            and renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and fact_operate_tag = #{factOperateTag}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
    </select>
    
    <!-- 接车已完成 -->
    <select id="queryVehicleFinish"  parameterType="com.extracme.evcard.mtc.bo.VehicleJoinQueryBO" resultType="java.lang.Long">
        select 
               count(*)
        from ${mtcSchema}.mtc_repair_task 
        WHERE `status`=1 and vehicle_transfer_task_schedule=110 and repair_type_id in (1,2,3,6,7,9)
        <!-- 修理厂权限 -->
        <if test="repairDepotId != null and repairDepotId != ''">
            and repair_depot_id = #{repairDepotId}
        </if>
        <!-- 运营人员权限 -->
        <if test="repairDepotId == null or repairDepotId == ''">
            and org_id like concat(#{loginOrgId},'%')
        </if>
        <if test="orgId!=null and orgId!=''">
          and org_id like concat(#{orgId},'%')
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vin!=null and vin!=''">
            and vin = #{vin}
        </if>
        <if test="taskNo!=null and taskNo!=''">
            and task_no = #{taskNo}
        </if>
        <if test="repairTypeId!=null and repairTypeId!=''">
            and repair_type_id = #{repairTypeId}
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
             <if test="taskInflowCreateTime!=null and taskInflowCreateTime!=''">
            and task_inflow_time &gt;=DATE_FORMAT(#{taskInflowCreateTime},'%Y-%m-%d 00:00:00.000')
        </if>
         <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and task_inflow_time &lt;=DATE_FORMAT(#{taskInflowEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
           <if test="createStartTime!=null and createStartTime!=''">
            and create_time &gt;=DATE_FORMAT(#{createStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="createEndTime!=null and createEndTime!=''">
            and create_time &lt;=DATE_FORMAT(#{createEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
       <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and vehicle_recive_time &gt;=DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and vehicle_recive_time &lt;=DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="renttype != null">
            and renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and fact_operate_tag = #{factOperateTag}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
    </select>

    <!-- 接车已关闭 -->
    <select id="queryVehicleClosed"  parameterType="com.extracme.evcard.mtc.bo.VehicleJoinQueryBO" resultType="java.lang.Long">
        select
        count(*)
        from ${mtcSchema}.mtc_repair_task
        WHERE `status`=1 and vehicle_transfer_task_schedule=130 and repair_type_id in (1,2,3,6,7,9)
        <!-- 修理厂权限 -->
        <if test="repairDepotId != null and repairDepotId != ''">
            and repair_depot_id = #{repairDepotId}
        </if>
        <!-- 运营人员权限 -->
        <if test="repairDepotId == null or repairDepotId == ''">
            and org_id like concat(#{loginOrgId},'%')
        </if>
        <if test="orgId!=null and orgId!=''">
          and org_id like concat(#{orgId},'%')
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vin!=null and vin!=''">
            and vin = #{vin}
        </if>
        <if test="taskNo!=null and taskNo!=''">
            and task_no = #{taskNo}
        </if>
        <if test="repairTypeId!=null and repairTypeId!=''">
            and repair_type_id = #{repairTypeId}
        </if>
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="taskInflowCreateTime!=null and taskInflowCreateTime!=''">
            and task_inflow_time &gt;=DATE_FORMAT(#{taskInflowCreateTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and task_inflow_time &lt;=DATE_FORMAT(#{taskInflowEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="createStartTime!=null and createStartTime!=''">
            and create_time &gt;=DATE_FORMAT(#{createStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="createEndTime!=null and createEndTime!=''">
            and create_time &lt;=DATE_FORMAT(#{createEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="vehicleReciveStartTime!=null and vehicleReciveStartTime!=''">
            and vehicle_recive_time &gt;=DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime!=null and vehicleReciveEndTime!=''">
            and vehicle_recive_time &lt;=DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="renttype != null">
            and renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and fact_operate_tag = #{factOperateTag}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
    </select>
    
    <select id="queryReassignmentApplyWaitCount" resultType="java.lang.Integer" parameterType="com.extracme.evcard.mtc.bo.ReassignmentApplyQueryBO">
        SELECT
            count(*)
        FROM
            ${mtcSchema}.mtc_repair_task a
        LEFT JOIN ${mtcSchema}.mtc_repair_depot_info b ON a.reassignment_repair_org_id = b.repair_depot_id
        <where>
           1=1   and a.status  = 1
           and a.reassignment_task_schedule = '400' 
           <if test="orgId != null and orgId !=''">
           and a.org_id like concat(#{orgId},'%')
           </if>
           <if test="vehicleNo != null and vehicleNo !=''">
           and a.vehicle_no like concat(#{vehicleNo},'%')
           </if>
           <if test="taskNo != null and taskNo !=''">
           and a.task_no = #{taskNo}
           </if>
           <if test="vehicleModelSeq != null and vehicleModelSeq !=''">
           and a.vehicle_model_seq = #{vehicleModelSeq}
           </if>
           <if test="reassignmentRepairOrgName != null and reassignmentRepairOrgName !=''">
           and b.repair_depot_name like concat('%',#{reassignmentRepairOrgName},'%')
           </if>
           <if test="startVehicleReciveTime != null and startVehicleReciveTime !=''"> 
           and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endVehicleReciveTime != null and endVehicleReciveTime !=''">
           and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
           </if>
           <if test="vin != null and vin !=''">
           and a.vin = #{vin}
           </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
           <if test="repairDepotName != null and repairDepotName !=''">
           and a.repair_depot_name like concat('%',#{repairDepotName},'%')
           </if>
           <if test="startTaskInflowTime != null and startTaskInflowTime !=''">
           and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endTaskInflowTime != null and endTaskInflowTime !=''">
           and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
           </if>
            <if test="renttype != null">
               and renttype = #{renttype}
            </if>
            <if test="factOperateTag != null">
                and fact_operate_tag = #{factOperateTag}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and renttype in
                    <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and renttype = -2
                </if>
            </if>
        </where>
    </select>

    <select id="queryReassignmentApplyClosedCount" resultType="java.lang.Integer" parameterType="com.extracme.evcard.mtc.bo.ReassignmentApplyQueryBO">
        SELECT
        count(*)
        FROM
        ${mtcSchema}.mtc_repair_task a
        LEFT JOIN ${mtcSchema}.mtc_repair_depot_info b ON a.reassignment_repair_org_id = b.repair_depot_id
        <where>
            1=1   and a.status  = 1
            and a.reassignment_task_schedule = '420'
            <if test="orgId != null and orgId !=''">
                and a.org_id like concat(#{orgId},'%')
            </if>
            <if test="vehicleNo != null and vehicleNo !=''">
                and a.vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="taskNo != null and taskNo !=''">
                and a.task_no = #{taskNo}
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq !=''">
                and a.vehicle_model_seq = #{vehicleModelSeq}
            </if>
            <if test="reassignmentRepairOrgName != null and reassignmentRepairOrgName !=''">
                and b.repair_depot_name like concat('%',#{reassignmentRepairOrgName},'%')
            </if>
            <if test="startVehicleReciveTime != null and startVehicleReciveTime !=''">
                and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
            </if>
            <if test="endVehicleReciveTime != null and endVehicleReciveTime !=''">
                and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
            </if>
            <if test="vin != null and vin !=''">
                and a.vin = #{vin}
            </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
                and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
                and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
            <if test="repairDepotName != null and repairDepotName !=''">
                and a.repair_depot_name like concat('%',#{repairDepotName},'%')
            </if>
            <if test="startTaskInflowTime != null and startTaskInflowTime !=''">
                and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
            </if>
            <if test="endTaskInflowTime != null and endTaskInflowTime !=''">
                and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
            </if>
            <if test="renttype != null">
                and renttype = #{renttype}
            </if>
            <if test="factOperateTag != null">
                and fact_operate_tag = #{factOperateTag}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and renttype in
                    <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and renttype = -2
                </if>
            </if>
        </where>
    </select>
    
    <select id="queryReassignmentApplyFinishCount" resultType="java.lang.Integer" parameterType="com.extracme.evcard.mtc.bo.ReassignmentApplyQueryBO">
        SELECT
            count(*)
        FROM
            ${mtcSchema}.mtc_repair_task a
        LEFT JOIN ${mtcSchema}.mtc_repair_depot_info b ON a.reassignment_repair_org_id = b.repair_depot_id
        <where>
           1=1   and a.status  = 1
           and a.reassignment_task_schedule = '410' 
           <if test="orgId != null and orgId !=''">
           and a.org_id like concat(#{orgId},'%')
           </if>
           <if test="vehicleNo != null and vehicleNo !=''">
           and a.vehicle_no like concat(#{vehicleNo},'%')
           </if>
           <if test="taskNo != null and taskNo !=''">
           and a.task_no like concat(#{vehicleNo},'%')
           </if>
           <if test="vehicleModelSeq != null and vehicleModelSeq !=''">
           and a.vehicle_model_seq = #{vehicleModelSeq}
           </if>
           <if test="reassignmentRepairOrgName != null and reassignmentRepairOrgName !=''">
           and b.repair_depot_name like concat('%',#{reassignmentRepairOrgName},'%')
           </if>
           <if test="startVehicleReciveTime != null and startVehicleReciveTime !=''"> 
           and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endVehicleReciveTime != null and endVehicleReciveTime !=''">
           and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
           </if>
           <if test="vin != null and vin !=''">
           and a.vin = #{vin}
           </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR} 
            </if>
            <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
           <if test="repairDepotName != null and repairDepotName !=''">
           and a.repair_depot_name like concat('%',#{repairDepotName},'%')
           </if>
           <if test="startTaskInflowTime != null and startTaskInflowTime !=''">
           and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
           </if>
           <if test="endTaskInflowTime != null and endTaskInflowTime !=''">
           and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
           </if>
        </where>
    </select>
    
    <update id="updateRepairTaskByTaskNo" parameterType="com.extracme.evcard.mtc.model.RepairTask">
        update ${mtcSchema}.mtc_repair_task
        <set>
          <if test="entityMap.orgId != null">
                  org_id = #{entityMap.orgId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.orgName != null">
                  org_name = #{entityMap.orgName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vehicleNo != null">
                  vehicle_no = #{entityMap.vehicleNo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vehicleModelSeq != null">
                  vehicle_model_seq = #{entityMap.vehicleModelSeq,jdbcType=BIGINT},
          </if>
          <if test="entityMap.vehicleModelInfo != null">
                  vehicle_model_info = #{entityMap.vehicleModelInfo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vin != null">
                  vin = #{entityMap.vin,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.insuranceCompanyName != null">
                  insurance_company_name = #{entityMap.insuranceCompanyName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairTypeId != null">
                  repair_type_id = #{entityMap.repairTypeId,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.repairTypeName != null">
                  repair_type_name = #{entityMap.repairTypeName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairGrade != null">
                  repair_grade = #{entityMap.repairGrade,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairDepotId != null">
                  repair_depot_id = #{entityMap.repairDepotId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.repairDepotName != null">
                  repair_depot_name = #{entityMap.repairDepotName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairDepotOrgId != null">
                  repair_depot_org_id = #{entityMap.repairDepotOrgId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.taskInflowTime != null">
                  task_inflow_time = #{entityMap.taskInflowTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.vehicleReciveTime != null">
                  vehicle_recive_time = #{entityMap.vehicleReciveTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.vehicleCheckTime != null">
                  vehicle_check_time = #{entityMap.vehicleCheckTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.vehicleRepairTime != null">
                  vehicle_repair_time = #{entityMap.vehicleRepairTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.expectedRepairDays != null">
                  expected_repair_days = #{entityMap.expectedRepairDays,jdbcType=BIGINT},
          </if>
          <if test="entityMap.expectedRepairComplete != null">
                  expected_repair_complete = #{entityMap.expectedRepairComplete,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.accidentReportNumber != null">
                  accident_report_number = #{entityMap.accidentReportNumber,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.repairFlag != null">
                  repair_flag = #{entityMap.repairFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.terminalId != null">
                  terminal_id = #{entityMap.terminalId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.totalMileage != null">
                  total_mileage = #{entityMap.totalMileage,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.associatedOrder != null">
                  associated_order = #{entityMap.associatedOrder,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.noDeductiblesFlag != null">
                  no_deductibles_flag = #{entityMap.noDeductiblesFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.driverName != null">
                  driver_name = #{entityMap.driverName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.driverTel != null">
                  driver_tel = #{entityMap.driverTel,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.routingInspectionName != null">
                  routing_inspection_name = #{entityMap.routingInspectionName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.routingInspectionTel != null">
                  routing_inspection_tel = #{entityMap.routingInspectionTel,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.damagedPartDescribe != null">
                  damaged_part_describe = #{entityMap.damagedPartDescribe,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.accidentDescribe != null">
                  accident_describe = #{entityMap.accidentDescribe,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.trailerFlag != null">
                  trailer_flag = #{entityMap.trailerFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.repairReplaceTotalAmount != null">
                  repair_replace_total_amount = #{entityMap.repairReplaceTotalAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.repairRepairTotalAmount != null">
                  repair_repair_total_amount = #{entityMap.repairRepairTotalAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.repairInsuranceTotalAmount != null">
                  repair_insurance_total_amount = #{entityMap.repairInsuranceTotalAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.vehicleReplaceTotalAmount != null">
                  vehicle_replace_total_amount = #{entityMap.vehicleReplaceTotalAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.vehicleRepairTotalAmount != null">
                  vehicle_repair_total_amount = #{entityMap.vehicleRepairTotalAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.vehicleInsuranceTotalAmount != null">
                  vehicle_insurance_total_amount = #{entityMap.vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.vehicleManageViewFlag != null">
                  vehicle_manage_view_flag = #{entityMap.vehicleManageViewFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.resurveyFlag != null">
                  resurvey_flag = #{entityMap.resurveyFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.resurveyPart != null">
                  resurvey_part = #{entityMap.resurveyPart,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.dutySituation != null">
                  duty_situation = #{entityMap.dutySituation,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.recoveryAmount != null">
                  recovery_amount = #{entityMap.recoveryAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.insuranceAmount != null">
                  insurance_amount = #{entityMap.insuranceAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.accDepAmount != null">
                  acc_dep_amount = #{entityMap.accDepAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.outageLossAmount != null">
                  outage_loss_amount = #{entityMap.outageLossAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.vehicleLossAmount != null">
                  vehicle_loss_amount = #{entityMap.vehicleLossAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.trailerRescueAmount != null">
                  trailer_rescue_amount = #{entityMap.trailerRescueAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.maintainAmount != null">
                  maintain_amount = #{entityMap.maintainAmount,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.reassignmentRepairOrgId != null">
                  reassignment_repair_org_id = #{entityMap.reassignmentRepairOrgId,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.reassignmentReasons != null">
                  reassignment_reasons = #{entityMap.reassignmentReasons,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.reassignmentRejectReasons != null">
                  reassignment_reject_reasons = #{entityMap.reassignmentRejectReasons,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.checkResultFlag != null">
                  check_result_flag = #{entityMap.checkResultFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.checkUnqualifiedReason != null">
                  check_unqualified_reason = #{entityMap.checkUnqualifiedReason,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.vehicleTransferTaskSchedule != null">
                  vehicle_transfer_task_schedule = #{entityMap.vehicleTransferTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.insuranceQuoteTaskSchedule != null">
                  insurance_quote_task_schedule = #{entityMap.insuranceQuoteTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.verificationLossTaskSchedule != null">
                  verification_loss_task_schedule = #{entityMap.verificationLossTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.reassignmentTaskSchedule != null">
                  reassignment_task_schedule = #{entityMap.reassignmentTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.vehicleRepairTaskSchedule != null">
                  vehicle_repair_task_schedule = #{entityMap.vehicleRepairTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.vehicleCheckTaskSchedule != null">
                  vehicle_check_task_schedule = #{entityMap.vehicleCheckTaskSchedule,jdbcType=BIGINT},
          </if>
          <if test="entityMap.currentTache != null">
                  current_tache = #{entityMap.currentTache,jdbcType=BIGINT},
          </if>
          <if test="entityMap.maintainToRepairFlag != null">
                  maintain_to_repair_flag = #{entityMap.maintainToRepairFlag,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.verificationLossTaskOperId != null">
                  verification_loss_task_oper_id = #{entityMap.verificationLossTaskOperId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.verificationLossTaskOperId == null">
                  verification_loss_task_oper_id = null,
          </if>
           <if test="entityMap.lossRegistrationTaskSchedule != null">
                  loss_registration_task_schedule = #{entityMap.lossRegistrationTaskSchedule,jdbcType=INTEGER},
          </if>
          <if test="entityMap.status != null">
                  status = #{entityMap.status,jdbcType=DECIMAL},
          </if>
          <if test="entityMap.remark != null">
                  remark = #{entityMap.remark,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.createOperId != null">
                  create_oper_id = #{entityMap.createOperId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.createOperName != null">
                  create_oper_name = #{entityMap.createOperName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.updateTime != null">
                  update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="entityMap.updateOperId != null">
                  update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
          </if>
          <if test="entityMap.updateOperName != null">
                  update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.materialCollectionTaskSchedule != null">
                  material_collection_task_schedule = #{entityMap.materialCollectionTaskSchedule,jdbcType=INTEGER},
          </if>
          <if test="entityMap.syncClaimFlag != null">
                sync_claim_flag = #{entityMap.syncClaimFlag,jdbcType=INTEGER},
          </if>
          <if test="entityMap.declareNo != null">
            declare_no = #{entityMap.declareNo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.declareStatus != null">
            declare_status = #{entityMap.declareStatus,jdbcType=INTEGER},
          </if>
          <if test="entityMap.declareSettlementStatus != null">
            declare_settlement_status = #{entityMap.declareSettlementStatus,jdbcType=INTEGER},
          </if>
          <if test="entityMap.settlementNo != null">
            settlement_no = #{entityMap.settlementNo,jdbcType=VARCHAR},
          </if>
          <if test="entityMap.settlementStatus != null">
            settlement_status = #{entityMap.settlementStatus,jdbcType=INTEGER},
          </if>
        </set>
        where task_no = #{entityMap.taskNo,jdbcType=VARCHAR}
    </update>

    <update id="updateManualInfoByTaskNo" parameterType="com.extracme.evcard.mtc.model.RepairTask">
        update ${mtcSchema}.mtc_repair_task
        <set>
            <if test="claimsFlag != null">
                claims_flag = #{claimsFlag,jdbcType=INTEGER},
            </if>
            <if test="estimatedClaimAmount != null">
                estimated_claim_amount = #{estimatedClaimAmount,jdbcType=DECIMAL},
            </if>
            <if test="vehicleInsuranceTotalAmount != null">
                vehicle_insurance_total_amount = #{vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
            </if>
            <if test="repairCostAmount != null">
                vehicle_repair_cost_amount = #{repairCostAmount,jdbcType=DECIMAL},
            </if>
            <if test="repairSettleAmount != null">
                repair_settle_amount = #{repairSettleAmount,jdbcType=DECIMAL},
            </if>
            <if test="repairFaxSettleAmount != null">
                repair_fax_settle_amount = #{repairFaxSettleAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where task_no = #{taskNo,jdbcType=VARCHAR}
    </update>
    
    
    <update id="updateStatusByTaskNo" parameterType="java.lang.String">
     update ${mtcSchema}.mtc_repair_task
       set status = '0'
        where task_no = #{taskNo}
    </update>
    
      <!-- 根据任务编号删除数据  --> 
    <delete id="deleteByTaskNoJoin" parameterType="java.lang.String">
        delete from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo}
    </delete>
    
     <!-- 根据任务编号删除数据 --> 
    <delete id="deleteByTaskNo" parameterType="java.lang.String">
        delete from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo} and reassignment_task_schedule = 410
    </delete>
    
    <!-- 核损核价占据任务--> 
    <update id="updatetaskOperId" parameterType="com.extracme.evcard.mtc.model.RepairTask">
    update ${mtcSchema}.mtc_repair_task
    <set>           
                        <if test="entityMap.verificationLossTaskSchedule != null">
                                verification_loss_task_schedule = #{entityMap.verificationLossTaskSchedule,jdbcType=BIGINT},
                         </if>
                                verification_loss_task_oper_id = #{entityMap.verificationLossTaskOperId,jdbcType=BIGINT},     
                        <if test="entityMap.examineLevel != null">
                                examine_level = #{entityMap.examineLevel,jdbcType=DECIMAL},
                        </if>   
                        <if test="currentTache != null">
                                current_tache = #{currentTache,jdbcType=BIGINT},
                        </if>     
                        <if test="entityMap.updateTime != null">
                                update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
                        </if>
                        <if test="entityMap.updateOperId != null">
                                update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
                        </if>
                        <if test="entityMap.materialCollectionTaskSchedule != null">
                                material_collection_task_schedule = #{entityMap.materialCollectionTaskSchedule,jdbcType=INTEGER},
                        </if>
                        <if test="entityMap.lossRegistrationTaskSchedule != null">
                                loss_registration_task_schedule = #{entityMap.lossRegistrationTaskSchedule,jdbcType=INTEGER},
                        </if>
                        <if test="entityMap.updateOperName != null">
                                update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR}
                        </if>
                        
          </set>
     where id = #{id,jdbcType=BIGINT} 
    </update>
    
    <!-- 查询返回值 -->
    <select id="selectByTaskNoR" parameterType="string" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
         from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo} and status = 0
    </select>
    
    <!-- 查询返回值 -->
    <select id="getunEndTaskByRepairDepotId" resultType="int">
    SELECT
     COUNT(*)
    FROM
     ${mtcSchema}.mtc_repair_task
    WHERE
     vehicle_check_task_schedule != 610 and status = 1
    AND repair_depot_id = #{repairDepotId,jdbcType=BIGINT}
    </select>
    
    <select id="getTimeoutTaskList" parameterType="map" resultType="com.extracme.evcard.mtc.bo.TimeoutTaskStatisticsBO">
        SELECT
            a.org_name orgName,
            a.vehicle_no vehicleNo,
            a.task_no taskNo,
            a.repair_depot_name repairDepotName,
            a.repair_type_name repairTypeName,
            a.repair_grade repairGrade,
            a.vehicle_model_info vehicleModelName,
            a.vin,
            DATE_FORMAT(a.vehicle_recive_time,'%Y-%m-%d %H:%i:%s') vehicleReciveTime,
            DATE_FORMAT(a.expected_repair_complete,'%Y-%m-%d %H:%i:%s') vehiclePlanFinishTime,
            DATE_FORMAT(a.vehicle_repair_time,'%Y-%m-%d %H:%i:%s') vehicleRepairTime,
            a.over_time_reasons timeoutReason,
            a.renttype renttype,
            a.fact_operate_tag factOperateTag
        FROM
            ${mtcSchema}.mtc_repair_task a
        WHERE a.vehicle_recive_time is not null and a.status = 1
            and a.expected_repair_complete is not null
            and a.expected_repair_complete &lt;= (case when a.vehicle_repair_time is not null then a.vehicle_repair_time else now() end)
        <if test="orgId != null and orgId !=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR} 
            </if>
            <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
            </if>
        <if test="repairGrade!=null and repairGrade!=''">
            and a.repair_grade = #{repairGrade}
        </if>
        <if test="vehicleNo != null and vehicleNo !=''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and a.repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime!= ''">
            and a.vehicle_recive_time &gt;= DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
            and a.vehicle_recive_time &lt;= DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime!= ''">
            and a.vehicle_repair_time &gt;= DATE_FORMAT(#{vehicleRepairStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
            and a.vehicle_repair_time &lt;= DATE_FORMAT(#{vehicleRepairEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and a.fact_operate_tag = #{factOperateTag}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
        order by a.update_time DESC
    </select>
    
    <select id="getTimeoutTaskNum" parameterType="map" resultType="integer">
        SELECT
            count(*)
        FROM
            ${mtcSchema}.mtc_repair_task a
        WHERE a.vehicle_recive_time is not null and a.status = 1
            and a.expected_repair_complete is not null
            and a.expected_repair_complete &lt;= (case when a.vehicle_repair_time is not null then a.vehicle_repair_time else now() end)
        <if test="orgId != null and orgId !=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
        <if test="repairTypeId != null">
            and a.repair_type_id = #{repairTypeId}
        </if>
        <if test="repairGrade!=null and repairGrade!=''">
            and a.repair_grade = #{repairGrade}
        </if>
        <if test="vehicleNo != null and vehicleNo !=''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and a.repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime!= ''">
            and a.vehicle_recive_time &gt;= DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
            and a.vehicle_recive_time &lt;= DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime!= ''">
            and a.vehicle_repair_time &gt;= DATE_FORMAT(#{vehicleRepairStartTime },'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
            and a.vehicle_repair_time &lt;= DATE_FORMAT(#{vehicleRepairEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and a.fact_operate_tag = #{factOperateTag}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
    </select>
    
    <select id="getOfferStatisticsList" parameterType="map" resultType="com.extracme.evcard.mtc.bo.OfferStatisticsBO">
        SELECT
            a.org_name orgName,
            a.vehicle_no vehicleNo,
            a.task_no taskNo,
            case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then "自费维修（原车辆保养）" else a.repair_type_name end as repairTypeName,
            a.repair_depot_name repairDepotName,
            a.repair_grade repairGrade,
            a.vehicle_model_info vehicleModelName,
            a.vin,
            a.repair_total_amount_first bidAmount,
            a.vehicle_insurance_total_amount passedAmount,
            DATE_FORMAT(a.vehicle_recive_time,'%Y-%m-%d %H:%i:%s') vehicleReciveTime,
            DATE_FORMAT(a.verification_loss_check_time,'%Y-%m-%d %H:%i:%s') pricingPasseTime,
            concat(b.name,'(',b.username,')') actorUser,
            a.renttype renttype,
            a.fact_operate_tag factOperateTag
       FROM
            ${mtcSchema}.mtc_repair_task a
       LEFT JOIN 
            ${mtcSchema}.mtc_user b on a.verification_loss_check_id=b.id
       WHERE a.repair_type_id=2 and a.vehicle_recive_time is not null and a.current_tache &gt;=50 and a.status = 1 and a.repair_total_amount_first != a.vehicle_insurance_total_amount
       <if test="orgId != null and orgId !=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
        <if test="repairGrade!=null and repairGrade!=''">
            and a.repair_grade = #{repairGrade}
        </if>
        <if test="vehicleNo != null and vehicleNo !=''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and a.repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime!= ''">
            and a.vehicle_recive_time &gt;= DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
            and a.vehicle_recive_time &lt;= DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="verificationLossCheckStartTime != null and verificationLossCheckStartTime!= ''">
            and a.verification_loss_check_time &gt;= DATE_FORMAT(#{verificationLossCheckStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="verificationLossCheckEndTime != null and verificationLossCheckEndTime != ''">
            and a.verification_loss_check_time &lt;= DATE_FORMAT(#{verificationLossCheckEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
         <if test="taskNo != null and taskNo !=''">
            and a.task_no like concat('%',#{taskNo},'%')
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and a.fact_operate_tag = #{factOperateTag}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
        order by a.update_time DESC
    </select>
    
    <select id="getOfferStatisticsNum" parameterType="map" resultType="integer">
        SELECT
            count(*)
       FROM
            ${mtcSchema}.mtc_repair_task a
       LEFT JOIN 
            ${mtcSchema}.mtc_user b on a.verification_loss_check_id=b.id
       WHERE a.repair_type_id=2 and a.vehicle_recive_time is not null and a.current_tache &gt;=50 and a.status = 1 and a.repair_total_amount_first != a.vehicle_insurance_total_amount
       <if test="orgId != null and orgId !=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
        <if test="repairGrade!=null and repairGrade!=''">
            and a.repair_grade = #{repairGrade}
        </if>
        <if test="vehicleNo != null and vehicleNo !=''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and a.repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime!= ''">
            and a.vehicle_recive_time &gt;= DATE_FORMAT(#{vehicleReciveStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
            and a.vehicle_recive_time &lt;= DATE_FORMAT(#{vehicleReciveEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="verificationLossCheckStartTime != null and verificationLossCheckStartTime!= ''">
            and a.verification_loss_check_time &gt;= DATE_FORMAT(#{verificationLossCheckStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="verificationLossCheckEndTime != null and verificationLossCheckEndTime != ''">
            and a.verification_loss_check_time &lt;= DATE_FORMAT(#{verificationLossCheckEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        <if test="factOperateTag != null">
            and a.fact_operate_tag = #{factOperateTag}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
    </select>
    
    <select id="getProcessResultDetail" parameterType="long" resultType="com.extracme.evcard.mtc.bo.ProcessResultBO">
        SELECT
            repair_depot_name repairDepotName,
            current_tache currentTacheId,
            vehicle_transfer_task_schedule vehicleTransferTaskSchedule,
            insurance_quote_task_schedule insuranceQuoteTaskSchedule,
            verification_loss_task_schedule verificationLossTaskSchedule,
            reassignment_task_schedule reassignmentTaskSchedule,
            vehicle_repair_task_schedule vehicleRepairTaskSchedule,
            vehicle_check_task_schedule vehicleCheckTaskSchedule,
            material_collection_task_schedule materialCollectionTaskSchedule,
            loss_registration_task_schedule lossRegistrationTaskSchedule,
            settlement_task_schedule settlementTaskSchedule,
            insurance_pre_review_task_schedule insurancePreReviewTaskSchedule,
            examine_level examineLevel
        FROM
            ${mtcSchema}.mtc_repair_task 
        WHERE id=#{id} and status = 1
    </select>

    
    <!-- 查询首页导出信息 -->
    <select id="getFirstPageExportInfo" parameterType="map" resultType="com.extracme.evcard.mtc.bo.RepairDepotInRepairingBO">
        SELECT
            a.vehicleFinishTime,
            a.org_name orgName,
            a.task_no taskNo,
            a.vehicle_no vehicleNo,
            a.vehicle_model_info vehicleModelName,
            a.vin,
            a.insurance_company_name insuranceCompanyName,
            a.repair_type_name repairTypeName,
            a.repair_grade repairGrade,
            a.vehicle_recive_time vehicleReciveTime,
            a.part_name partName,
            a.part_total_money partTotalMoney,
            a.repair_name repairName,
            a.repair_total_money repairTotalMoney,
            a.total_money totalMoney,
            a.noReciveNum noReciveNum,
            a.renttype renttype
        FROM(
        SELECT
            date_add(a.vehicle_recive_time, INTERVAL a.expected_repair_days DAY) vehicleFinishTime,
            a.org_name,
            a.task_no,
            a.vehicle_no,
            a.vehicle_model_info,
            a.vin,
            a.insurance_company_name,
            a.repair_type_name,
            a.repair_grade,
            a.vehicle_recive_time,
            a.update_time,
            a.org_id,
            f.noReciveNum,
            a.renttype,
            GROUP_CONCAT(DISTINCT b.part_name) as part_name,
            case verification_loss_task_schedule when 320 then CASE a.repair_type_id WHEN 1 THEN  a.repair_replace_total_amount ELSE a.vehicle_replace_total_amount END else '' end as part_total_money,
            GROUP_CONCAT(DISTINCT c.repair_name) as repair_name,
            case verification_loss_task_schedule when 320 then CASE a.repair_type_id WHEN 1 THEN a.repair_repair_total_amount ELSE a.vehicle_repair_total_amount END else '' end as repair_total_money,
            case verification_loss_task_schedule when 320 then CASE a.repair_type_id WHEN 1 THEN a.repair_insurance_total_amount ELSE a.vehicle_insurance_total_amount END else '' end as total_money
        FROM
            ${mtcSchema}.mtc_repair_task a
        LEFT JOIN ${mtcSchema}.mtc_replace_item_detail b FORCE INDEX (idx_task_no) ON a.task_no = b.task_no
        LEFT JOIN ${mtcSchema}.mtc_repair_item_detail c FORCE INDEX (idx_task_no) ON a.task_no = c.task_no
         LEFT JOIN (
            SELECT
                repair_depot_id,
                count(*) noReciveNum
            FROM
                 mtc_repair_task WHERE org_id like CONCAT(#{orgId},'%')
                 and create_time is null
                 and repair_type_id IN (1, 2, 6,9)
                 AND maintain_to_repair_flag = 0 
                GROUP BY repair_depot_id
        ) f ON a.repair_depot_id = f.repair_depot_id
        where
            a.repair_type_id in(1,2,6)
            and a.status = 1
            and a.maintain_to_repair_flag = 0
            AND a.vehicle_check_task_schedule !=610 
            AND a.check_result_flag !=0 
            AND a.reassignment_task_schedule!=410 
            AND a.create_time is not null
            AND (a.current_tache  &lt;=60 or a.current_tache = 110)
            AND a.vehicle_transfer_task_schedule != 130
            AND a.insurance_quote_task_schedule != 250
            AND a.verification_loss_task_schedule != 340
            AND a.reassignment_task_schedule != 420
            AND a.vehicle_repair_task_schedule != 530
            AND a.vehicle_check_task_schedule != 640
            AND a.insurance_pre_review_task_schedule != 1150
      <if test="orgId!=null and orgId!=''">
            and a.org_id like CONCAT(#{orgId},'%')
        </if>   
        <if test="repairDepotId!=null">
            and a.repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
        </if>
        GROUP BY a.task_no
         ) as a
        ORDER BY a.org_id asc, a.update_time desc
    </select>
    
    <select id="getTyreTaskView" resultMap="ViewResultMap">
    select
    a.id,
    a.task_no,
    a.current_tache,
    a.org_id,
    a.org_name,
    a.vehicle_no,
    a.vehicle_model_seq,
    a.vehicle_model_info,
    a.vin,
    a.insurance_company_name,
    a.repair_type_id,
    case when a.repair_type_id = 1 then '事故维修' when  a.repair_type_id=2 then '自费维修' when a.repair_type_id = 3 then '车辆保养' when a.repair_type_id = 4 then '轮胎任务' end as repair_type_name,
    a.repair_grade,
    a.repair_depot_org_id,
    a.repair_depot_id,
    a.repair_depot_name,
    a.task_inflow_time,
    a.vehicle_recive_time,
    a.tireNumber,
    a.tire_unit_price,
    a.tire_brand,
    a.expected_repair_days,
    a.expected_repair_complete,
    a.accident_report_number,
    a.repair_flag,
    a.terminal_id,
    a.total_mileage,
    a.associated_order,
    a.no_deductibles_flag,
    a.driver_name,
    a.driver_tel,
    a.routing_inspection_name,
    a.routing_inspection_tel,
    a.damaged_part_describe,
    a.accident_describe,
    a.trailer_flag,
    a.repair_replace_total_amount,
    a.repair_repair_total_amount,
    a.repair_insurance_total_amount,
    a.vehicle_replace_total_amount,
    a.vehicle_repair_total_amount,
    a.vehicle_insurance_total_amount,
    a.resurvey_flag,
    a.resurvey_part,
    a.duty_situation,
    a.recovery_amount,
    a.insurance_amount,
    a.acc_dep_amount,
    a.outage_loss_amount,
    a.vehicle_loss_amount,
    a.trailer_rescue_amount,
    a.maintain_amount,
    a.reassignment_repair_org_id,
    a.reassignment_reasons,
    a.reassignment_reject_reasons,
    a.check_result_flag,
    a.check_unqualified_reason,
    a.maintain_to_repair_flag,
    a.vehicle_transfer_task_schedule,
    a.insurance_quote_task_schedule,
    a.verification_loss_task_schedule,
    a.reassignment_task_schedule,
    a.vehicle_repair_task_schedule,
    a.vehicle_check_task_schedule,
    a.tire_task_schedule,
    a.current_tache,
    a.verification_loss_task_oper_id,
    a.examine_level,
    a.nuclear_loss_reversion_flag,
    a.verification_reject_reasons,
    a.verification_reject_reasons_detail,
    a.over_time_reasons,
    a.repair_total_amount_first,
    CASE
     WHEN b.pic_type = 10 THEN
        ( 
     
            CASE
            WHEN b.pic_url is not NULL
            AND b.pic_url != '' THEN
                (  
                    CASE
                    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
                        CONCAT(
                            '${evcard-aliyun-prefix}',
                            SUBSTR(b.pic_url, 2)
                        )
                    ELSE
                        CONCAT('${evcard-aliyun-prefix}', b.pic_url)
                    END 
                )
            ELSE
                null
            END
        ) 
     ELSE
        null
     END AS drivingLicense,
     
      CASE
     WHEN b.pic_type = 11 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS policy,
     
     CASE
     WHEN b.pic_type = 12 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS accident,
     
     CASE
     WHEN b.pic_type = 13 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS damageA,
     CASE
     WHEN b.pic_type = 14 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS damageB,
     
     CASE
     WHEN b.pic_type = 15 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS claims,
     
     CASE
     WHEN b.pic_type = 16 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS other,
     
     CASE
     WHEN b.pic_type = 2 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE 
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS repair,
     
     CASE
     WHEN b.pic_type = 1 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE 
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS damagedPart,
     CASE
     WHEN b.pic_type = 17 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS checkVideo,
     CASE
     WHEN b.pic_type = 18 THEN
      ( 
     
       CASE
       WHEN b.pic_url is not NULL
       AND b.pic_url != '' THEN
        (  
         CASE
         WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
          CONCAT(
           '${evcard-aliyun-prefix}',
           SUBSTR(b.pic_url, 2)
          )
         ELSE
          CONCAT('${evcard-aliyun-prefix}', b.pic_url)
         END 
        )
       ELSE
        null
       END
      ) 
     ELSE
      null
     END AS afterPic,

      CASE
      WHEN b.pic_type = 19 THEN
      (
      CASE
      WHEN b.pic_url IS NOT NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR( b.pic_url, 1, 1 ) = '/' THEN
      CONCAT( '${evcard-aliyun-prefix}', SUBSTR( b.pic_url, 2 ) ) ELSE CONCAT( '${evcard-aliyun-prefix}', b.pic_url )
      END
      ) ELSE NULL
      END
      ) ELSE NULL
      END AS accidentLiabilityConfirmationPicture,

      CASE
      WHEN b.pic_type = 20 THEN
      (
      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS insuranceCompanyLossOrderPicture,

      CASE
      WHEN b.pic_type = 21 THEN
      (
      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS ourDriverLicensePicture
      CASE
      WHEN b.pic_type = 23 THEN
      (

      CASE
      WHEN b.pic_url is not NULL
      AND b.pic_url != '' THEN
      (
      CASE
      WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
      CONCAT(
      '${evcard-aliyun-prefix}',
      SUBSTR(b.pic_url, 2)
      )
      ELSE
      CONCAT('${evcard-aliyun-prefix}', b.pic_url)
      END
      )
      ELSE
      null
      END
      )
      ELSE
      null
      END AS damagedPartV,
    from  ${mtcSchema}.mtc_repair_task a
    left join ${mtcSchema}.mtc_vehicle_repair_pic b on a.task_no = b.task_no
    <where>
     a.id= #{id,jdbcType=BIGINT} and a.status = 1
    </where>
    </select>  
    
    <select id="repairDepotId" resultType="String">
    select repair_depot_id from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo} and status = 1
    </select>
    
    
    <select id="queryTyreJoin" parameterType="com.extracme.evcard.mtc.bo.VehicleJoinQueryBO" resultType="com.extracme.evcard.mtc.bo.VehicleJoinBO">
        select 
            id,
            task_no taskNo,
            org_name orgName,
            vehicle_no vehicleNo,
            vehicle_model_info vehicleModelName,
            vin,
            insurance_company_name insuranceCompanyName,
            repair_type_name repairTypeName,
            repair_grade repairGrade,
            tireNumber,
            total_mileage as totalMileage,
            repair_depot_name repairDepotName,
            DATE_FORMAT(task_inflow_time,'%Y-%m-%d %H:%i:%s') createTime,
            DATE_FORMAT(vehicle_recive_time,'%Y-%m-%d %H:%i:%s') vehicleReciveTime,
            vehicle_transfer_task_schedule taskSchedule,
             case vehicle_transfer_task_schedule
             when 100 then N'未处理'
             when 110 then N'已完成'
             else '' end taskScheduleName,
             DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') as flowTaskTime
        from ${mtcSchema}.mtc_repair_task 
        WHERE `status`=1 and vehicle_transfer_task_schedule IN (100,110)
        and repair_type_id = 4
        and tire_task_schedule = #{tireTaskSchedule,jdbcType=INTEGER}
        <if test="orgId!=null and orgId!=''">
          and org_id like concat(#{orgId},'%')
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="vin!=null and vin!=''">
            and vin = #{vin}
        </if>
        <if test="taskNo!=null and taskNo!=''">
            and task_no = #{taskNo}
        </if>
        
        <if test="vehicleModelSeq!=null and vehicleModelSeq!=''">
            and vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="repairDepotId!=null and repairDepotId!=''">
            and repair_depot_id = #{repairDepotId}
        </if>
        <if test="taskInflowCreateTime!=null and taskInflowCreateTime!=''">
            and task_inflow_time &gt;=DATE_FORMAT(#{taskInflowCreateTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="taskInflowEndTime!=null and taskInflowEndTime!=''">
            and task_inflow_time &lt;=DATE_FORMAT(#{taskInflowEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        <if test="createStartTime!=null and createStartTime!=''">
            and create_time &gt;=DATE_FORMAT(#{createStartTime},'%Y-%m-%d 00:00:00.000')
        </if>
        <if test="createEndTime!=null and createEndTime!=''">
            and create_time &lt;=DATE_FORMAT(#{createEndTime},'%Y-%m-%d 23:59:59.999')
        </if>
        
        
            ORDER BY create_time DESC
        
    </select>
    
     <select id="materialCollectionInfo" resultType="com.extracme.evcard.mtc.bo.MaterialCollectionInfoBO">
     select 
     a.id,
     a.task_no taskNo,
     a.vehicle_no vehicleNo,
     case when repair_type_id = 0 then '自建任务' else a.repair_type_name end repairTypeName,
     a.accident_report_number accidentReportNumber,
     a.vehicle_model_info vehicleModelInfo,
     a.vin,
     a.repair_depot_name repairDepotName,
     a.insurance_company_name insuranceCompanyName,
     a.org_name orgName,
     b.name processingPerson,
     a.associated_order as associatedOrder,
     a.vehicle_insurance_total_amount as vehicleInsuranceTotalAmount
     from ${mtcSchema}.mtc_repair_task a
     left join ${mtcSchema}.mtc_user b on a.verification_loss_task_oper_id = b.id
     <where>
        <if test="vehicleOrgId!=null and vehicleOrgId!=''">
            a.org_id like concat(#{vehicleOrgId},'%')
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairTypeId!=null and repairTypeId!=''">
            and repair_type_id = #{repairTypeId}
        </if>
        <if test="taskNo!=null and taskNo!=''">
            and task_no = #{taskNo}
        </if>
        <if test="accidentReportNumber!=null and accidentReportNumber!=''">
            and accident_report_number like concat('%',#{accidentReportNumber},'%') 
        </if>
        <if test="insuranceCompanyName!=null and insuranceCompanyName!=''">
            and insurance_company_name like concat('%',#{insuranceCompanyName},'%') 
        </if>
        <if test="materialCollectionTaskSchedule !=null and materialCollectionTaskSchedule !=''">            
            and material_collection_task_schedule = #{materialCollectionTaskSchedule}
             
        </if>
                 
        <if test="materialCollectionTaskSchedule == null or materialCollectionTaskSchedule ==''">
        and material_collection_task_schedule >= 700 
        
        </if>
        and a.status = 1
     </where>
     order by a.update_time DESC
     </select>
     
     <select id="searchLatestCase" resultType="string">
     SELECT task_no FROM ${mtcSchema}.mtc_repair_task WHERE task_no like concat(#{prefix},#{nowTime},'%') and create_time and status = 1 ORDER BY create_time DESC LIMIT 1;
     </select>
     
     <select id="getTaskNoByVehicleNo" resultType="string">
     SELECT task_no FROM ${mtcSchema}.mtc_repair_task WHERE vehicle_no=#{vehicleNo} 
     and settlement_task_schedule in(900,910) and status = 1
     and update_time  ORDER BY update_time DESC LIMIT 1;
     </select>
     
     <!-- 保存数据 -->
    <insert id="saveCase" parameterType="com.extracme.evcard.mtc.model.RepairTask" useGeneratedKeys="true" keyProperty="id">
        insert into ${mtcSchema}.mtc_repair_task (
            id,
            task_no,
            
            org_id,
            org_name,
            vehicle_no,
            <if test="vehicleModelSeq!=null">
            vehicle_model_seq,
            </if>
            <if test="vehicleModelInfo!=null and vehicleModelInfo!=''">
            vehicle_model_info,
            </if>
            vin,
            <if test="insuranceCompanyName!=null and insuranceCompanyName!=''">
            insurance_company_name,
            </if>
            
            
            repair_depot_id,
       
            <if test="repairDepotName!=null and repairDepotName!=''">
            repair_depot_name,
            </if>
            <if test="repairDepotOrgId!=null and repairDepotOrgId!=''">
            repair_depot_org_id,
            </if>
            
            accident_report_number,
            
            driver_name,
            driver_tel,
            
            accident_describe,
            
            duty_situation,
            
            
            material_collection_task_schedule,
            current_tache,
            
            associated_task_no,
            insurance_flag,
            accident_day_time,
            repair_type_id,
            create_time,
            create_oper_id,
            create_oper_name,
            update_time,
            update_oper_id,
            update_oper_name
        ) values (
            #{id,jdbcType=BIGINT},
            #{taskNo,jdbcType=VARCHAR},
            
            #{orgId,jdbcType=VARCHAR},
            #{orgName,jdbcType=VARCHAR},
            #{vehicleNo,jdbcType=VARCHAR},
            <if test="vehicleModelSeq!=null">
            #{vehicleModelSeq,jdbcType=BIGINT},
            </if>
            <if test="vehicleModelInfo!=null and vehicleModelInfo!=''">
            #{vehicleModelInfo,jdbcType=VARCHAR},
            </if>
            #{vin,jdbcType=VARCHAR},
            <if test="insuranceCompanyName!=null and insuranceCompanyName!=''">
            #{insuranceCompanyName,jdbcType=VARCHAR},
            </if>
           
            #{repairDepotId,jdbcType=VARCHAR},
           
            <if test="repairDepotName!=null and repairDepotName!=''">
            #{repairDepotName,jdbcType=VARCHAR},
            </if>
            
            <if test="repairDepotOrgId!=null and repairDepotOrgId!=''">
           #{repairDepotOrgId,jdbcType=VARCHAR},
            </if>
            
            
            #{accidentReportNumber,jdbcType=VARCHAR},
            
            #{driverName,jdbcType=VARCHAR},
            #{driverTel,jdbcType=VARCHAR},
            
            #{accidentDescribe,jdbcType=VARCHAR},
           
            
            #{dutySituation,jdbcType=INTEGER},
           
            #{materialCollectionTaskSchedule,jdbcType=INTEGER},
            #{currentTache,jdbcType=BIGINT},
            #{associatedTaskNo,jdbcType=VARCHAR},
            #{insuranceFlag,jdbcType=INTEGER},
            #{accidentDayTime},
            #{repairTypeId},
            #{createTime,jdbcType=TIMESTAMP},
            #{createOperId,jdbcType=BIGINT},
            #{createOperName,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{updateOperId,jdbcType=BIGINT},
            #{updateOperName,jdbcType=VARCHAR}
        )
    </insert>
    
    
    <select id="getCaseTaskView" resultType="com.extracme.evcard.mtc.bo.MaterialBO">
    select
    a.id,
    a.task_no taskNo,
    a.verification_loss_task_oper_id verificationLossTaskOperId,
    a.org_id orgId,
    a.org_name orgName,
    a.vehicle_no vehicleNo,
    a.vehicle_model_seq vehicleModelSeq,
    a.vehicle_model_info vehicleModelInfo,
    a.vin,
    a.insurance_company_name insuranceCompanyName,
    a.repair_type_id repairTypeId,
    case when a.repair_type_id = 0 then '自建任务' when a.repair_type_id = 1 then '事故维修' when  a.repair_type_id=2 then '自费维修' when a.repair_type_id = 3 then '车辆保养' when a.repair_type_id = 9 then '短租包修' end as repairTypeName,
    a.insurance_flag insuranceFlag,
    case when insurance_flag=0 then '否'
    when insurance_flag=1 then '是' end insuranceFlagName,
    a.material_collection_task_schedule materialCollectionTaskSchedule,
    a.accident_report_number accidentReportNumber,
    a.repair_depot_name repairDepotName,
    
    a.driver_name driverName,
    a.driver_tel driverTel,
   
    a.accident_describe accidentDescribe,
    case when a.duty_situation = -1 then null 
    when a.duty_situation = 0 then null
    else a.duty_situation end dutySituation,
    
    case when a.duty_situation=1 then '全责'
    when a.duty_situation=2 then '主责'
    when a.duty_situation=3 then '次责'
    when a.duty_situation=4 then '平责'
    when a.duty_situation=5 then '无责' end dutySituationName,
    IFNULL(b.subject_vehicle_material,0)  subjectVehicleMaterial,
    IFNULL(b.human_injury_material,0) humanInjuryMaterial,
    IFNULL(b.hird_party_material,0) hirdPartyMaterial,
    IFNULL(b.material_loss_material,0) materialLossMaterial,
    IFNULL(b.other_material,0) otherMaterial,
    b.remark,
    a.accident_day_time accidentDayTime
    from  ${mtcSchema}.mtc_repair_task a
    left join ${mtcSchema}.mtc_material_collection b on a.task_no = b.task_no
    <where>
     a.id= #{id,jdbcType=BIGINT} and a.status = 1
    </where>
    </select>  
    
     <update id="updateMaterialMsg" parameterType="com.extracme.evcard.mtc.model.RepairTask">
        update ${mtcSchema}.mtc_repair_task
        <set>
            
            <if test="materialCollectionTaskSchedule != null">
                    material_collection_task_schedule = #{materialCollectionTaskSchedule,jdbcType=INTEGER},
            </if>
            <if test="accidentReportNumber != null">
                    accident_report_number = #{accidentReportNumber,jdbcType=VARCHAR},
            </if>
            
            <if test="driverName != null">
                    driver_name = #{driverName,jdbcType=VARCHAR},
            </if>
            <if test="driverTel != null">
                    driver_tel = #{driverTel,jdbcType=VARCHAR},
            </if>
            
            <if test="accidentDescribe != null">
                    accident_describe = #{accidentDescribe,jdbcType=VARCHAR},
            </if>
            
            <if test="dutySituation != null">
                    duty_situation = #{dutySituation,jdbcType=INTEGER},
            </if>
            <if test="accidentDayTime != null">
                    accident_day_time = #{accidentDayTime},
            </if>
            <if test="entityMap.insuranceFlag != null">
                    insurance_flag = #{entityMap.insuranceFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>            
            <if test="updateOperId != null">
                    update_oper_id = #{updateOperId,jdbcType=BIGINT},
            </if>
            <if test="updateOperName != null">
                    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
            </if>
        </set>
        where task_no = #{taskNo,jdbcType=VARCHAR}
    </update>
    
    <update id="updateMaterialCollectionTaskSchedule" parameterType="com.extracme.evcard.mtc.model.RepairTask" >
        <selectKey keyProperty='id' resultType='long' order='BEFORE'>
            select id FROM
            ${mtcSchema}.mtc_repair_task
            WHERE
             task_no = #{taskNo,jdbcType=VARCHAR}
        </selectKey>
        update ${mtcSchema}.mtc_repair_task
        <set>                                   
            <if test="entityMap.materialCollectionTaskSchedule != null">
                    material_collection_task_schedule = #{entityMap.materialCollectionTaskSchedule,jdbcType=INTEGER},
            </if>
            <if test="entityMap.currentTache != null">
                    current_tache = #{entityMap.currentTache,jdbcType=BIGINT},
            </if>
           <if test="entityMap.lossRegistrationTaskSchedule != null">
                    loss_registration_task_schedule = #{entityMap.lossRegistrationTaskSchedule,jdbcType=INTEGER},
           </if>
            verification_loss_task_oper_id = #{entityMap.verificationLossTaskOperId,jdbcType=BIGINT},
            
            <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>            
            <if test="updateOperId != null">
                    update_oper_id = #{updateOperId,jdbcType=BIGINT},
            </if>
            <if test="updateOperName != null">
                    update_oper_name = #{updateOperName,jdbcType=VARCHAR},
            </if>
            verification_loss_task_oper_id = null
        </set>
        where task_no = #{taskNo,jdbcType=VARCHAR}
    </update>
    
    <!-- 损失登记详情 -->
    <select id="getLossRegistrationDetail" resultType="com.extracme.evcard.mtc.bo.MtcLossRegistrationDetailBO">
        select
        a.id,
        a.task_no taskNo,
        a.accident_day_time accidentDayTime,
        a.org_id orgId,
        a.org_name orgName,
        a.vehicle_no vehicleNo,
        a.vehicle_model_seq vehicleModelSeq,
        a.vehicle_model_info vehicleModelInfo,
        a.vin,
        a.insurance_company_name insuranceCompanyName,
        a.repair_type_id repairTypeId,
        case when a.repair_type_id = 0 then '自建任务' when a.repair_type_id = 1 then '事故维修' when  a.repair_type_id=2 then '自费维修' when a.repair_type_id = 3 then '车辆保养' when a.repair_type_id = 4 then '轮胎任务' end as repairTypeName,
        a.repair_depot_name repairDepotName,
        a.driver_name driverName,
        a.driver_tel driverTel,
        a.damaged_part_describe damagedPartDescribe,
        a.duty_situation dutySituation,
        a.accident_describe accidentDescribe,
        a.accident_report_number accidentReportNumber,
        a.vehicle_insurance_total_amount vehicleInsuranceTotalAmount,
        a.accident_day_time accidentDayTime,
        case when (a.must_claim_amount - ifnull(b.incoming_amount, 0)&lt;0) then 0
        else (a.must_claim_amount - ifnull(b.incoming_amount, 0))
        end as mustClaimAmount,
    
        case when (IFNULL(a.must_pay_amount,0) - ifnull(a.already_pay_amount, 0)&lt;0) then 0.00
        else (IFNULL(a.must_pay_amount,0) - ifnull(a.already_pay_amount, 0))
        end as waitPayAmount,
    
        ifnull(b.incoming_amount, 0) alreadyIncomingAmount,
        a.already_pay_amount alreadyPayAmount,
        a.insurance_flag insuranceFlag,
        case when a.transfer_flag=0 then (case when ifnull(b.incoming_amount, 0)-a.already_pay_amount&lt;0 then 0.00 else ifnull(b.incoming_amount, 0)-a.already_pay_amount end)  else 0.00 end waitTransferredAmount,
        case when a.transfer_flag=1 then (case when ifnull(b.incoming_amount, 0)-a.already_pay_amount&lt;0 THEN 0.00 else ifnull(b.incoming_amount, 0)-a.already_pay_amount end) else 0.00 end incomeTransferredAmount
        from mtc.mtc_repair_task a
        left join
          (
         select task_no,vehicle_no,sum(incoming_amount) incoming_amount from
         mtc.mtc_accounts_receivable_detail where status =1
         group by task_no,vehicle_no
          )
          b on a.task_no=b.task_no and a.vehicle_no=b.vehicle_no
    <where>
     id= #{id,jdbcType=BIGINT}
     and a.status=1
    </where> 
    </select>
    
    <!-- 重新关联列表 -->
    <select id = "queryCorrelationList" resultType = "com.extracme.evcard.mtc.bo.CorrelationListBO" >
       SELECT 
            a.task_no as taskNo,
            a.vehicle_no,
            a.insurance_company_name AS insuranceCompanyName,
            a.create_time AS createTime,
            a.driver_name AS driverName,
            a.must_claim_amount as mustClaimAmount,
            case when  a.must_claim_amount - already_pay_amount &lt; 0 then 0.00 
            else  a.must_claim_amount - already_pay_amount end leftMustClaimAmount
           
       FROM ${mtcSchema}.mtc_repair_task a
       left join ${mtcSchema}.mtc_accounts_receivable_detail b on a.vehicle_no = b.vehicle_no and a.task_no=b.task_no
       <where>
          a.settlement_task_schedule in (900,910)
          and a.status = 1 and b.no_association !=2
          <if test="vehicleNo != null and vehicleNo != ''">
             and a.vehicle_no = #{vehicleNo}
          </if>
       </where>
       GROUP BY a.task_no,a.vehicle_no
    </select>
    
     <select id="getMaterialCollectionInfoCount" resultType="com.extracme.evcard.mtc.bo.GetMaterialCollectionInfoCountBO">
     SELECT 
     IFNULL(sum(case when a.material_collection_task_schedule = 700 or (a.vehicle_check_task_schedule=610 and current_tache = 60) then 1 else 0 end),0) as noHandle,
         IFNULL(sum(case when a.material_collection_task_schedule = 710 then 1 else 0 end),0) as handling,
         IFNULL(sum(case when a.material_collection_task_schedule = 720 then 1 else 0 end),0) as finished,
         IFNULL(sum(case when a.material_collection_task_schedule = 730 then 1 else 0 end),0) as sellCase,
         IFNULL(sum(case when a.material_collection_task_schedule = 740 then 1 else 0 end),0) as retreat
     from ${mtcSchema}.mtc_repair_task a
     left join ${mtcSchema}.mtc_user b on a.verification_loss_task_oper_id = b.id
     <where>
        <if test="vehicleOrgId!=null and vehicleOrgId!=''">
            a.org_id like concat(#{vehicleOrgId},'%')
        </if>
        <if test="vehicleNo!=null and vehicleNo!=''">
            and vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairTypeId!=null and repairTypeId!=''">
            and repair_type_id = #{repairTypeId}
        </if>
        <if test="taskNo!=null and taskNo!=''">
            and task_no = #{taskNo}
        </if>
        <if test="accidentReportNumber!=null and accidentReportNumber!=''">
            and accident_report_number like concat('%',#{accidentReportNumber},'%') 
        </if>
        <if test="insuranceCompanyName!=null and insuranceCompanyName!=''">
            and insurance_company_name like concat('%',#{insuranceCompanyName},'%') 
        </if>
        
        
        and material_collection_task_schedule >= 700 
       
     and a.status = 1
     </where>
     </select>
     
     <select id="getAccidentReportNumber" resultType="String">
        SELECT accident_report_number FROM ${mtcSchema}.mtc_repair_task 
        WHERE accident_report_number = #{accidentReportNumber} 
        and status = 1
        and current_tache &gt;=70
        and current_tache != 110
        <if test="id!=null">
        and id not in (#{id})
        </if>
        and material_collection_task_schedule != 730
     </select>
     
     <update id="updatebyTaskNoS">
          update ${mtcSchema}.mtc_repair_task set settlement_task_schedule = 910 where task_no = #{taskNo}
     </update>
     
     <select id="selectByTaskNoCR" resultType = "com.extracme.evcard.mtc.bo.CheckrRepairTaskByRepairBO" >
        select settlement_task_schedule as settlementTaskSchedule,vehicle_no as vehicleNo
              from ${mtcSchema}.mtc_repair_task where
                 task_no =  #{taskNo} and status = 1
     </select>
     
     <select id="getTaskSchedule" resultType="com.extracme.evcard.mtc.bo.TaskScheduleBO">
        select vehicle_transfer_task_schedule vehicleTransferTaskSchedule,
        insurance_quote_task_schedule insuranceQuoteTaskSchedule,
        verification_loss_task_schedule verificationLossTaskSchedule,
        reassignment_task_schedule reassignmentTaskSchedule,
        vehicle_repair_task_schedule vehicleRepairTaskSchedule,
        vehicle_check_task_schedule vehicleCheckTaskSchedule,
        material_collection_task_schedule materialCollectionTaskSchedule,
        loss_registration_task_schedule lossRegistrationTaskSchedule,
        settlement_task_schedule settlementTaskSchedule,
        insurance_pre_review_task_schedule insurancePreReviewTaskSchedule,
        current_tache currentTache
        from ${mtcSchema}.mtc_repair_task 
        where task_no =  #{taskNo} and status = 1
    </select>
    
    <select id="getIdByTaskNo" resultType = "String" >
        select id
              from ${mtcSchema}.mtc_repair_task where
                 task_no =  #{taskNo} 
                 and status=1
     </select>

    <select id="queryMaintainNum" resultType = "java.lang.Integer">
        SELECT
			    COUNT(*) as maintainNum
			FROM
			    mtc_repair_task
			WHERE
			    repair_type_id = 3
			AND maintain_to_repair_flag = 0
			AND vehicle_check_task_schedule != 610
			AND insurance_quote_task_schedule != 230
            AND insurance_quote_task_schedule != 250
			AND reassignment_task_schedule != 410
			and check_result_flag !=0
			and status = 1
			<if test="orgId!=null and orgId!=''">
                and org_id like CONCAT(#{orgId},'%')
	        </if>   
	        <if test="repairDepotId!=null and repairDepotId!=''">
	            and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
	        </if>
    </select> 
    <select id="getVehicleModelSeq" resultType = "String" >
        select vehicle_model_seq
              from ${mtcSchema}.mtc_repair_task where
                 task_no =  #{taskNo} and
                  status = 1
     </select>
     
       <select id="queryRationalIndemnityCnt" resultType = "Integer" >
        select rational_indemnity_cnt
              from ${mtcSchema}.mtc_repair_task where
                 task_no =  #{taskNo} and
                  status = 1
     </select>
 
     <select id="queryMaintenanceToselfNum" resultType = "java.lang.Integer">
		    SELECT
		    COUNT(*) as maintenanceToselfNum
				FROM
				    mtc_repair_task
				WHERE
				    repair_type_id = 2
				AND maintain_to_repair_flag = 1
				AND vehicle_check_task_schedule != 610 
				AND reassignment_task_schedule != 410
                and vehicle_transfer_task_schedule != 130
                and insurance_quote_task_schedule != 250
                and verification_loss_task_schedule != 340
                and reassignment_task_schedule != 420
                and vehicle_repair_task_schedule != 530
                and vehicle_check_task_schedule != 640
				and check_result_flag !=0
                and status = 1
            <if test="orgId!=null and orgId!=''">
                and org_id like CONCAT(#{orgId},'%')
            </if>   
            <if test="repairDepotId!=null and repairDepotId!=''">
                and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
            </if>
    </select> 
    <select id="getRepairGrade" resultType = "String">
        select repair_grade from ${mtcSchema}.mtc_repair_task where
                 task_no =  #{taskNo} 
    </select>   
 
    <select id="queryReportFirst" resultType="com.extracme.evcard.mtc.bo.ReportByWeek">
            SELECT 
            IFNULL(h.createTime,0) as createTime,
            IFNULL(m.currentRepairNum,0) as currentRepairNum,
                        IFNULL(m.aRepairNum,0) as aRepairNum,
                        IFNULL(m.bRepairNum,0) as bRepairNum,
                        IFNULL(m.cRepairNum,0) as cRepairNum
				     FROM (select  ODay as createTime from 
				            (SELECT DATE_FORMAT(DATE_ADD(NOW(), INTERVAL - 0 DAY),'%Y-%m-%d')  AS ODay) a UNION all
				            select  ODay as createTime from 
				            (SELECT DATE_FORMAT(DATE_ADD(NOW(), INTERVAL - 1 DAY),'%Y-%m-%d')  AS ODay) b UNION all
				            select  ODay as createTime from 
				            (SELECT DATE_FORMAT(DATE_ADD(NOW(), INTERVAL - 2 DAY),'%Y-%m-%d')  AS ODay) c UNION all
				            select  ODay as createTime from 
				            (SELECT DATE_FORMAT(DATE_ADD(NOW(), INTERVAL - 3 DAY),'%Y-%m-%d')  AS ODay) d UNION all
				                select  ODay as createTime from 
				            (SELECT DATE_FORMAT(DATE_ADD(NOW(), INTERVAL - 4 DAY),'%Y-%m-%d')  AS ODay) e UNION all
				             select  ODay as createTime from 
				            (SELECT DATE_FORMAT(DATE_ADD(NOW(), INTERVAL - 5 DAY),'%Y-%m-%d')  AS ODay) f UNION all
				             select  ODay as createTime from 
				            (SELECT DATE_FORMAT(DATE_ADD(NOW(), INTERVAL - 6 DAY),'%Y-%m-%d')  AS ODay) g) h  
				        LEFT JOIN   
				(SELECT  a.create_time as createTime,
				        count(*) currentRepairNum,
				        SUM(CASE a.repair_grade WHEN 'A' THEN 1 ELSE 0 END ) aRepairNum,
				        SUM(CASE a.repair_grade WHEN 'B' THEN 1 ELSE 0 END ) bRepairNum,
				        SUM(CASE a.repair_grade WHEN 'C' THEN 1 ELSE 0 END ) cRepairNum
				        FROM(
				            SELECT
				                repair_depot_id,
				                repair_grade,
				                DATE_FORMAT(create_time,'%Y-%m-%d') as create_time
				            FROM
				                ${mtcSchema}.mtc_repair_task
				            WHERE
				                repair_type_id IN (1, 2, 6,9) and status = 1
				            AND maintain_to_repair_flag = 0
				            AND check_result_flag != 0
				            AND reassignment_task_schedule != 410 
				            and current_tache  &lt;= 60
				           <if test="orgId!=null and orgId!=''">
			                and org_id like CONCAT(#{orgId},'%')
			               </if>   
				            ) AS a
				                GROUP BY
				                    a.create_time)m
				ON h.createTime = m.createTime  ORDER BY h.createTime 
    </select>
    
     <select id="queryCountA" resultType = "java.lang.Integer">
        SELECT COUNT(*) as countA from ${mtcSchema}.mtc_repair_task 
        WHERE repair_type_id IN (1,2,6,9)
        AND maintain_to_repair_flag = 0 
        AND reassignment_task_schedule != 410 
        AND status = 1
        <if test="orgId!=null and orgId!=''">
        and org_id like CONCAT(#{orgId},'%')
        </if> 
     </select>
     
     <select id="queryCountB" resultType = "java.lang.Integer">
     SELECT COUNT(*) as countB from ${mtcSchema}.mtc_repair_task 
     WHERE repair_type_id = 3 
     AND maintain_to_repair_flag = 0 
     AND status = 1 
     AND reassignment_task_schedule != 410 
     <if test="orgId!=null and orgId!=''">
     and org_id like CONCAT(#{orgId},'%')
     </if>      
     </select>
     
     <select id="queryCountC" resultType = "java.lang.Integer">
     SELECT COUNT(*) as countC  from ${mtcSchema}.mtc_repair_task 
     WHERE repair_type_id = 2 
     AND maintain_to_repair_flag = 1 
     AND status = 1 
     AND reassignment_task_schedule != 410
     <if test="orgId!=null and orgId!=''">
     and org_id like CONCAT(#{orgId},'%')
     </if>
     </select>
     
     <select id="queryCountD" resultType = "java.lang.Integer">
     SELECT COUNT(*) as countD from ${mtcSchema}.mtc_repair_task
      WHERE repair_type_id = 4 and status = 1
      AND reassignment_task_schedule != 410
      <if test="orgId!=null and orgId!=''">
      and org_id like CONCAT(#{orgId},'%')
      </if> 
     </select>
     
     <select id="queryGradeMaintenance" resultType="com.extracme.evcard.mtc.bo.ReportFirst">
     SELECT   
        SUM(CASE  repair_grade WHEN 'A' THEN 1 ELSE 0 END ) aRepairNum,
        SUM(CASE  repair_grade WHEN 'B' THEN 1 ELSE 0 END ) bRepairNum,
        SUM(CASE  repair_grade WHEN 'C' THEN 1 ELSE 0 END ) cRepairNum
        
            FROM
                mtc.mtc_repair_task
            WHERE
                repair_type_id IN (1, 2, 6,9)
            AND status = 1 
            AND maintain_to_repair_flag = 0 
            AND reassignment_task_schedule != 410 
            <if test="orgId!=null and orgId!=''">
		    and org_id like CONCAT(#{orgId},'%')
		    </if>
     </select>
  
    <select id="getAllTaskSchedule" resultType = "com.extracme.evcard.mtc.bo.RealTimeAnalysisBO">
	    SELECT
			task_no as taskNo,
			repair_depot_id as repairDepotId,
			repair_depot_name as repairDepotName,
			org_id as orgId,
			vehicle_transfer_task_schedule as vehicleTransferTaskSchedule,
			insurance_quote_task_schedule as insuranceQuoteTaskSchedule,
			verification_loss_task_schedule as verificationLossTaskSchedule,
			vehicle_repair_task_schedule as vehicleRepairTaskSchedule,
			vehicle_check_task_schedule as vehicleCheckTaskSchedule
		FROM
			${mtcSchema}.mtc_repair_task
		WHERE
		 	update_time &gt;= #{startTime}
        AND update_time &lt;= #{endTime}
        AND vehicle_transfer_task_schedule > 100 
        AND status = 1

    </select>
    
    <update id="updateDeductFlag" parameterType="com.extracme.evcard.mtc.model.RepairTask">
        update ${mtcSchema}.mtc_repair_task
        <set>           
            <if test="deductFlag != null">
                    deduct_flag = #{deductFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>            
            <if test="updateOperId != null">
                    update_oper_id = #{updateOperId,jdbcType=BIGINT},
            </if>
            <if test="updateOperName != null">
                    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
            </if>
        </set>
        where task_no = #{taskNo,jdbcType=VARCHAR}
    </update>
    
    <update id="updateTransferAmount" parameterType="com.extracme.evcard.mtc.model.RepairTask">
        update ${mtcSchema}.mtc_repair_task
        <set>
            <if test="transferFlag != null">
                    transfer_flag = #{transferFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>            
            <if test="updateOperId != null">
                    update_oper_id = #{updateOperId,jdbcType=BIGINT},
            </if>
            <if test="updateOperName != null">
                    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
            </if>
        </set>
        where task_no = #{taskNo,jdbcType=VARCHAR}
    </update>
    
    <select id="getId" resultType = "com.extracme.evcard.mtc.bo.RemarkMesseage">
        select id,current_tache currentTache from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo,jdbcType=VARCHAR}
    </select>
    
    <select id="getClaimsSettlementStatementList" resultType="com.extracme.evcard.mtc.bo.ClaimsSettlementStatementInfo">
		SELECT
            b.count,
			a.task_no taskNo,
			CASE
		WHEN a.settlement_task_schedule = 910 THEN
			'处理中'
		WHEN a.settlement_task_schedule = 920 THEN
			'待结案'
		WHEN a.settlement_task_schedule = 920 THEN
			'待结案'
		WHEN a.settlement_task_schedule = 930 THEN
			'已结案'
		END AS settlementTaskScheduleName,
		 a.vehicle_model_info vehicleModelInfo,
		 a.vehicle_no vehicleNo,
		 a.accident_day_time accidentDayTime,
		 a.insurance_company_name insuranceCompanyName
		FROM
			${mtcSchema}.mtc_repair_task a
            left join (select task_no,count(*) count from ${mtcSchema}.mtc_accounts_receivable_detail where create_payment_list_flag = 1 group by task_no) b
            on a.task_no = b.task_no
		WHERE
			a.current_tache = 90 and settlement_task_schedule >= 910
            and  a.org_id LIKE CONCAT(#{orgId}, '%') and  b.count>0
        <if test="vehicleNo != null and vehicleNo !=''">
             and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="taskNo != null and taskNo !=''">
             and a.task_no = #{taskNo}
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
             and vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="insuranceCompanyName != null and insuranceCompanyName != ''">
             and insurance_company_name = #{insuranceCompanyName}
        </if>
        <if test="taskSchedule != null and taskSchedule != ''">
             and settlement_task_schedule = #{taskSchedule}
        </if>       
        <if test="accidentDayTimeStart != null and accidentDayTimeStart != '' and accidentDayTimeEnd !=null and accidentDayTimeEnd !=''">
             and accident_day_time &gt;= #{accidentDayTimeStart} and accident_day_time &lt;= #{accidentDayTimeEnd}
        </if>	
		ORDER BY
			a.accident_day_time DESC
			  
    </select>
    
    <select id="whetherInRepair" resultType="integer">
    select
    count(1)
    from 
    ${mtcSchema}.mtc_repair_task
    where current_tache &lt;= 60 and status=1 and vin = #{vin,jdbcType=VARCHAR}
    </select>
  
  <update id="updateRepairTaskInfo" parameterType="com.extracme.evcard.mtc.model.RepairTask">
    update ${mtcSchema}.mtc_repair_task
    <set>
      <if test="entityMap.taskNo != null">
        task_no = #{entityMap.taskNo,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.orgId != null">
        org_id = #{entityMap.orgId,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.orgName != null">
        org_name = #{entityMap.orgName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.vehicleNo != null">
        vehicle_no = #{entityMap.vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.vehicleModelSeq != null">
        vehicle_model_seq = #{entityMap.vehicleModelSeq,jdbcType=BIGINT},
      </if>
      <if test="entityMap.vehicleModelInfo != null">
        vehicle_model_info = #{entityMap.vehicleModelInfo,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.vin != null">
        vin = #{entityMap.vin,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.insuranceCompanyName != null">
        insurance_company_name = #{entityMap.insuranceCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.repairTypeId != null">
        repair_type_id = #{entityMap.repairTypeId,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.repairTypeName != null">
        repair_type_name = #{entityMap.repairTypeName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.repairGrade != null">
        repair_grade = #{entityMap.repairGrade,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.repairDepotId != null">
        repair_depot_id = #{entityMap.repairDepotId,jdbcType=BIGINT},
      </if>
      <if test="entityMap.repairDepotName != null">
        repair_depot_name = #{entityMap.repairDepotName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.repairDepotOrgId != null">
        repair_depot_org_id = #{entityMap.repairDepotOrgId,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.taskInflowTime != null">
        task_inflow_time = #{entityMap.taskInflowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.vehicleReciveTime != null">
        vehicle_recive_time = #{entityMap.vehicleReciveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.vehicleCheckTime != null">
        vehicle_check_time = #{entityMap.vehicleCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.vehicleRepairTime != null">
        vehicle_repair_time = #{entityMap.vehicleRepairTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.expectedRepairDays != null">
        expected_repair_days = #{entityMap.expectedRepairDays,jdbcType=BIGINT},
      </if>
      <if test="entityMap.expectedRepairComplete != null">
        expected_repair_complete = #{entityMap.expectedRepairComplete,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.accidentReportNumber != null">
        accident_report_number = #{entityMap.accidentReportNumber,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.repairFlag != null">
        repair_flag = #{entityMap.repairFlag,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.terminalId != null">
        terminal_id = #{entityMap.terminalId,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.totalMileage != null">
        total_mileage = #{entityMap.totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.associatedOrder != null">
        associated_order = #{entityMap.associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.noDeductiblesFlag != null">
        no_deductibles_flag = #{entityMap.noDeductiblesFlag,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.driverName != null">
        driver_name = #{entityMap.driverName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.driverTel != null">
        driver_tel = #{entityMap.driverTel,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.routingInspectionName != null">
        routing_inspection_name = #{entityMap.routingInspectionName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.routingInspectionTel != null">
        routing_inspection_tel = #{entityMap.routingInspectionTel,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.damagedPartDescribe != null">
        damaged_part_describe = #{entityMap.damagedPartDescribe,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.accidentDescribe != null">
        accident_describe = #{entityMap.accidentDescribe,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.trailerFlag != null">
        trailer_flag = #{entityMap.trailerFlag,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.repairReplaceTotalAmount != null">
        repair_replace_total_amount = #{entityMap.repairReplaceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.estimatedClaimAmount != null">
        estimated_claim_amount = #{entityMap.estimatedClaimAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.repairRepairTotalAmount != null">
        repair_repair_total_amount = #{entityMap.repairRepairTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.repairInsuranceTotalAmount != null">
        repair_insurance_total_amount = #{entityMap.repairInsuranceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.vehicleReplaceTotalAmount != null">
        vehicle_replace_total_amount = #{entityMap.vehicleReplaceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.vehicleRepairTotalAmount != null">
        vehicle_repair_total_amount = #{entityMap.vehicleRepairTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.vehicleInsuranceTotalAmount != null">
        vehicle_insurance_total_amount = #{entityMap.vehicleInsuranceTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.vehicleManageViewFlag != null">
        vehicle_manage_view_flag = #{entityMap.vehicleManageViewFlag,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.resurveyFlag != null">
        resurvey_flag = #{entityMap.resurveyFlag,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.resurveyPart != null">
        resurvey_part = #{entityMap.resurveyPart,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.dutySituation != null">
        duty_situation = #{entityMap.dutySituation,jdbcType=DECIMAL},
      </if>
      recovery_amount = #{entityMap.recoveryAmount,jdbcType=DECIMAL},
      insurance_amount = #{entityMap.insuranceAmount,jdbcType=DECIMAL},
      acc_dep_amount = #{entityMap.accDepAmount,jdbcType=DECIMAL},
      outage_loss_amount = #{entityMap.outageLossAmount,jdbcType=DECIMAL},
      vehicle_loss_amount = #{entityMap.vehicleLossAmount,jdbcType=DECIMAL},
      trailer_rescue_amount = #{entityMap.trailerRescueAmount,jdbcType=DECIMAL},
      maintain_amount = #{entityMap.maintainAmount,jdbcType=DECIMAL},
      <if test="entityMap.lossOrderAmount != null">
        loss_order_amount = #{entityMap.lossOrderAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.reassignmentRepairOrgId != null">
        reassignment_repair_org_id = #{entityMap.reassignmentRepairOrgId,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.reassignmentReasons != null">
        reassignment_reasons = #{entityMap.reassignmentReasons,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.reassignmentRejectReasons != null">
        reassignment_reject_reasons = #{entityMap.reassignmentRejectReasons,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.checkResultFlag != null">
        check_result_flag = #{entityMap.checkResultFlag,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.checkUnqualifiedReason != null">
        check_unqualified_reason = #{entityMap.checkUnqualifiedReason,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.vehicleTransferTaskSchedule != null">
        vehicle_transfer_task_schedule = #{entityMap.vehicleTransferTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="entityMap.insuranceQuoteTaskSchedule != null">
        insurance_quote_task_schedule = #{entityMap.insuranceQuoteTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="entityMap.verificationLossTaskSchedule != null">
        verification_loss_task_schedule = #{entityMap.verificationLossTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="entityMap.reassignmentTaskSchedule != null">
        reassignment_task_schedule = #{entityMap.reassignmentTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="entityMap.vehicleRepairTaskSchedule != null">
        vehicle_repair_task_schedule = #{entityMap.vehicleRepairTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="entityMap.vehicleCheckTaskSchedule != null">
        vehicle_check_task_schedule = #{entityMap.vehicleCheckTaskSchedule,jdbcType=BIGINT},
      </if>
      <if test="entityMap.materialCollectionTaskSchedule != null">
        material_collection_task_schedule = #{entityMap.materialCollectionTaskSchedule,jdbcType=INTEGER},
      </if>
      <if test="entityMap.lossRegistrationTaskSchedule != null">
        loss_registration_task_schedule = #{entityMap.lossRegistrationTaskSchedule,jdbcType=INTEGER},
      </if>
      <if test="entityMap.settlementTaskSchedule != null">
        settlement_task_schedule = #{entityMap.settlementTaskSchedule,jdbcType=INTEGER},
      </if>
      <if test="entityMap.currentTache != null">
        current_tache = #{entityMap.currentTache,jdbcType=BIGINT},
      </if>
      <if test="entityMap.advancedAuditLeve != null">
        advanced_audit_leve = #{entityMap.advancedAuditLeve,jdbcType=BIT},
      </if>
      <if test="entityMap.maintainToRepairFlag != null">
        maintain_to_repair_flag = #{entityMap.maintainToRepairFlag,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.repairTotalAmountFirst != null">
        repair_total_amount_first = #{entityMap.repairTotalAmountFirst,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.nuclearLossReversionFlag != null">
        nuclear_loss_reversion_flag = #{entityMap.nuclearLossReversionFlag,jdbcType=DECIMAL},
      </if>
      over_time_reasons = #{entityMap.overTimeReasons,jdbcType=VARCHAR},
      <if test="entityMap.verificationLossCheckTime != null">
        verification_loss_check_time = #{entityMap.verificationLossCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.verificationLossCheckId != null">
        verification_loss_check_id = #{entityMap.verificationLossCheckId,jdbcType=BIGINT},
      </if>
      <if test="entityMap.confirmType != null">
        confirm_type = #{entityMap.confirmType, jdbcType=INTEGER},
      </if>
      <if test="entityMap.confirmCarDamageType != null">
        confirm_car_damage_type = #{entityMap.confirmCarDamageType, jdbcType=INTEGER},
      </if>
      <if test="entityMap.status != null">
        status = #{entityMap.status,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.remark != null">
        remark = #{entityMap.remark,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.createOperId != null">
        create_oper_id = #{entityMap.createOperId,jdbcType=BIGINT},
      </if>
      <if test="entityMap.createOperName != null">
        create_oper_name = #{entityMap.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.updateTime != null">
        update_time = #{entityMap.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entityMap.updateOperId != null">
        update_oper_id = #{entityMap.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="entityMap.updateOperName != null">
        update_oper_name = #{entityMap.updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.isUsedApplets != null">
        is_used_applets = #{entityMap.isUsedApplets,jdbcType=VARCHAR},
      </if>
      <if test="entityMap.custPaysDirect != null">
        cust_pays_direct = #{entityMap.custPaysDirect,jdbcType=INTEGER},
      </if>
      <if test="entityMap.custAmount != null">
        cust_amount = #{entityMap.custAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.userAssumedAmount != null">
        user_assumed_amount = #{entityMap.userAssumedAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.notUserAssumedAmount != null">
        not_user_assumed_amount = #{entityMap.notUserAssumedAmount,jdbcType=DECIMAL},
      </if>
      <if test="entityMap.selfFundedAmount != null">
        self_funded_amount = #{entityMap.selfFundedAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    <if test="entityMap.controlStatusCurrentTache != null ">
      and current_tache = #{entityMap.controlStatusCurrentTache,jdbcType=BIGINT}
    </if>
  </update>
    
    <!-- 维修历史(资产用) -->
    <select id="selectRepairHistroy" resultType="com.evcard.mtc.provider.dto.QueryRepairHistroyDTO">
	SELECT
	    DATE_FORMAT(a.vehicle_recive_time,'%Y-%m-%d %H:%i:%s') repairStartTime,
	    DATE_FORMAT(MAX(b.create_time),'%Y-%m-%d %H:%i:%s')  repairEndTime,
	    a.vehicle_insurance_total_amount repairAmount,
	    timestampdiff(day,a.vehicle_recive_time,MAX(b.create_time))+1 repairInterval
	FROM
	    ${mtcSchema}.`mtc_repair_task` a
	LEFT JOIN ${mtcSchema}.mtc_operator_log b ON a.id = b.recoder_id
	AND b.ope_content = '申请验收'
	WHERE
	    a.vin = #{vin,jdbcType=VARCHAR} and a.current_tache >= 60 and a.repair_type_id = 2 and a.reassignment_task_schedule = -1
	GROUP BY a.id
	
	UNION ALL
	
    SELECT
        DATE_FORMAT(a.vehicle_recive_time,'%Y-%m-%d %H:%i:%s') repairStartTime,
        DATE_FORMAT(MAX(b.create_time),'%Y-%m-%d %H:%i:%s')  repairEndTime,
        IFNULL(a.maintain_amount,0) repairAmount,
        timestampdiff(day,a.vehicle_recive_time,MAX(b.create_time))+1 repairInterval
    FROM
        ${mtcSchema}.`mtc_repair_task` a
    LEFT JOIN ${mtcSchema}.mtc_operator_log b ON a.id = b.recoder_id
    AND b.ope_content Like '保养完成%'
    WHERE
        a.vin = #{vin,jdbcType=VARCHAR} and a.repair_type_id = 3 and a.insurance_quote_task_schedule = 230 and a.reassignment_task_schedule = -1
    GROUP BY a.id
	ORDER BY repairStartTime desc
    </select>
    
        <!-- 维修历史总条数(资产用) -->
    <select id="selectRepairHistroyCount" resultType="java.lang.Integer">
    SELECT
       count(1)
    FROM
        ${mtcSchema}.`mtc_repair_task` a
    WHERE
        (a.vin = #{vin,jdbcType=VARCHAR} and a.current_tache >= 60 and a.repair_type_id = 2 and a.reassignment_task_schedule = -1)  or 
        (a.vin = #{vin,jdbcType=VARCHAR} and a.repair_type_id = 3 and a.insurance_quote_task_schedule = 230 and a.reassignment_task_schedule = -1)

    </select>
    
    <!--维修历史（资产用）月度统计  -->
    <select id="selectRepairHistroyByMonth" resultType="com.evcard.mtc.provider.dto.QueryRepairHistroyByMonthDTO">
    SELECT
	repairMonth,
	sum(repairAmount) repairAmount
	from
	(

			SELECT
			DATE_FORMAT(b.create_time,'%Y-%m') repairMonth,
			a.vehicle_insurance_total_amount repairAmount
			FROM
			${mtcSchema}.`mtc_repair_task` a
			LEFT JOIN ${mtcSchema}.mtc_operator_log b ON a.id = b.recoder_id
			AND b.ope_content = '申请验收'
			WHERE
			a.vin = #{vin,jdbcType=VARCHAR}
			AND a.current_tache >= 60 and a.repair_type_id = 2
			AND a.reassignment_task_schedule = - 1
			GROUP BY a.id
			
			UNION ALL
			
			SELECT
			DATE_FORMAT(b.create_time,'%Y-%m') repairMonth,
			IFNULL(a.maintain_amount,0) repairAmount
			FROM
			${mtcSchema}.`mtc_repair_task` a
			LEFT JOIN ${mtcSchema}.mtc_operator_log b ON a.id = b.recoder_id
			AND b.ope_content Like '保养完成%'
			WHERE
			a.vin = #{vin,jdbcType=VARCHAR} and a.repair_type_id = 3 and a.insurance_quote_task_schedule = 230 and a.reassignment_task_schedule = -1
			GROUP BY a.id

         ) c

       GROUP BY  repairMonth ORDER BY repairMonth DESC
    
    
    
    </select>
    
    <resultMap id="VehicleRepairHistroyResultMap" type="com.extracme.evcard.mtc.bo.QueryVehicleRepairHistoryBO">
        <result column="repair_type_name" jdbcType="VARCHAR" property="repairTypeName" />
        <result column="repair_depot_name" jdbcType="VARCHAR" property="repairDepotName" />
        <result column="vehicle_recive_time" jdbcType="VARCHAR" property="vehicleReciveTime" />
        <result column="vehicle_check_time" jdbcType="VARCHAR" property="vehicleCheckTime" />
        <result column="vehicle_insurance_total_amount" jdbcType="DECIMAL" property="vehicleRepairTotalAmount" />
        <result column="recovery_amount" jdbcType="DECIMAL" property="recoveryAmount" />
        <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
        <result column="current_tache" jdbcType="BIGINT" property="currentTache" />
        <result column="currentLink" jdbcType="VARCHAR" property="currentLink" />
        <result column="vehicle_transfer_task_schedule" jdbcType="BIGINT" property="vehicleTransferTaskSchedule" />
        <result column="insurance_quote_task_schedule" jdbcType="BIGINT" property="insuranceQuoteTaskSchedule" />
        <result column="verification_loss_task_schedule" jdbcType="BIGINT" property="verificationLossTaskSchedule" />
        <result column="reassignment_task_schedule" jdbcType="BIGINT" property="reassignmentTaskSchedule" />
        <result column="vehicle_repair_task_schedule" jdbcType="BIGINT" property="vehicleRepairTaskSchedule" />
        <result column="vehicle_check_task_schedule" jdbcType="BIGINT" property="vehicleCheckTaskSchedule" />
        <result column="material_collection_task_schedule" jdbcType="BIGINT" property="materialCollectionTaskSchedule" />
        <result column="loss_registration_task_schedule" jdbcType="BIGINT" property="lossRegistrationTaskSchedule" />
        <result column="settlement_task_schedule" jdbcType="BIGINT" property="settlementTaskSchedule" />
        <result column="task_schedule" jdbcType="BIGINT" property="taskSchedule" />
     </resultMap>
    
    
    <!-- 维修记录 -->
    <select id = "selectVehicleRepairHistroy" resultMap="VehicleRepairHistroyResultMap">
        SELECT 
            repair_type_name,
            repair_depot_name,
            DATE_FORMAT(vehicle_recive_time,'%Y-%m-%d') vehicle_recive_time,
            DATE_FORMAT(vehicle_check_time,'%Y-%m-%d') vehicle_check_time,
            vehicle_insurance_total_amount,
            b.recovery_amount,
            task_no,
            vehicle_transfer_task_schedule,
            insurance_quote_task_schedule,
            verification_loss_task_schedule,
            reassignment_task_schedule,
            vehicle_repair_task_schedule,
            vehicle_check_task_schedule,
            material_collection_task_schedule,
            loss_registration_task_schedule,
            settlement_task_schedule,
            b.task_schedule,
            current_tache,
            CASE current_tache WHEN 10 THEN '车辆交接'
                               WHEN 20 THEN '维修报价'
                               WHEN 30 THEN '核损核价'
                               WHEN 40 THEN '改派中'
                               WHEN 50 THEN '车辆维修'
                               WHEN 60 THEN '车辆验收'
                               WHEN 70 THEN '资料收集'
                               WHEN 80 THEN '损失登记' 
                               WHEN 90 THEN '结算管理'
                               when 110 THEN '进保预审'
                               ELSE '其他' END currentLink
        FROM ${mtcSchema}.mtc_repair_task 
        LEFT JOIN ${mtcSchema}.mtc_cost_recourse_task b ON ${mtcSchema}.mtc_repair_task.id = b.repair_task_id
        WHERE
           vin = #{vin,jdbcType=VARCHAR}
        AND
           repair_type_id != 0
        ORDER BY ${mtcSchema}.mtc_repair_task.update_time DESC
    </select>
    
    <select id="selectByUser" resultType="com.evcard.mtc.provider.dto.UserRepairHistroyDTO">
     select 
    task_no taskNo
    from ${mtcSchema}.mtc_repair_task where driver_name = #{userName,jdbcType=VARCHAR} and driver_tel = #{mobilePhone,jdbcType=VARCHAR} and status = 1
    and (material_collection_task_schedule != 730 and loss_registration_task_schedule !=820) and repair_type_id in (1,2,6,9)
    </select>

    <select id="selectByAuthId" parameterType="string" resultType="com.evcard.mtc.provider.dto.UserRepairHistroyDTO">
    select
    task_no as taskNo
    from
    ${mtcSchema}.mtc_repair_task
    where
    create_time >= '2018-08-28'
    and auth_id = #{authId,jdbcType=VARCHAR}
    and status = 1
    and repair_type_id in (1,2,6,9)
    and vehicle_transfer_task_schedule not in (120,130)
    and insurance_quote_task_schedule != 250
    and verification_loss_task_schedule != 340
    and reassignment_task_schedule not in (410,420)
    and vehicle_repair_task_schedule != 530
    and vehicle_check_task_schedule not in (610, 640)
    and material_collection_task_schedule != 730
    and settlement_task_schedule != 930
    union
    select
    task_no as taskNo
    from
    ${mtcSchema}.mtc_repair_task
    where
    create_time &lt; '2018-08-28'
    and auth_id = #{authId,jdbcType=VARCHAR}
    and status = 1
    and vehicle_check_task_schedule != 610
    and repair_type_id in (1,2,6,9)
    </select>


    <select id="selectByRepairDepotId" parameterType="string" resultType="com.evcard.mtc.provider.dto.UserRepairHistroyDTO">
        select
            task_no as taskNo
        from
            ${mtcSchema}.mtc_repair_task
        where
          repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
          and status = 1
          and repair_type_id in (1,2,6,9)
          and vehicle_transfer_task_schedule not in (120,130)
          and insurance_pre_review_task_schedule != 1150
          and insurance_quote_task_schedule != 250
          and verification_loss_task_schedule != 340
          and reassignment_task_schedule not in (410,420)
          and vehicle_repair_task_schedule != 530
          and vehicle_check_task_schedule not in(610, 640)
    </select>

    <!-- 更新提车信息 -->
    <update id="updateTakeInfo" parameterType="com.evcard.mtc.provider.dto.UpdateTakeInfoDTO">
        update
          ${mtcSchema}.mtc_repair_task
        set
          take_user_name = #{takeUserName,jdbcType=VARCHAR},
          take_user_phone = #{takeUserPhone,jdbcType=VARCHAR},
          take_voucher = #{takeVoucher,jdbcType=VARCHAR},
          syn_take_time = sysdate()
        where
          task_no = #{taskNo,jdbcType=VARCHAR} and status = 1
    </update>


    <!-- 查询提车信息 -->
    <select id="queryRepairTakeInfo" parameterType="com.extracme.evcard.mtc.bo.QueryRepairTakeInfoBO" resultType="com.extracme.evcard.mtc.bo.RepairTakeInfoBO">
        select
            task_no as taskNo,
            vehicle_no as vehicleNo,
            renttype as renttype,
            vehicle_model_info as vehicleModelInfo,
            vin as vin,
            repair_type_name as repairTypeName,
            repair_depot_name as repairDepotName,
            take_user_name as takeUserName,
            take_user_phone as takeUserPhone,
            take_voucher as takeVoucher,
            date_format(syn_take_time, '%Y-%m-%d %H:%i:%s') as synTakeTime
        from
          ${mtcSchema}.mtc_repair_task a
        where
          a.status = 1 and a.syn_take_time is not null
        <if test="orgId != null and orgId !=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
        <if test="repairDepotId != null and  repairDepotId != ''">
            and repair_depot_id = #{repairDepotId}
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
        <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
            and a.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskNo != null and taskNo != ''">
            and a.task_no = #{taskNo}
        </if>
        <if test="vin != null and vin != ''">
            and a.vin = #{vin}
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairDepotName != null and repairDepotName != ''">
            and a.repair_depot_name like concat('%',#{repairDepotName},'%')
        </if>
        <if test="synTakeStartTime != null">
            and a.syn_take_time >= #{synTakeStartTime}
        </if>
        <if test="synTakeEndTime != null">
            and a.syn_take_time <![CDATA[<]]> #{synTakeEndTime}
        </if>
        order by
        a.syn_take_time desc
    </select>

    <!-- 查询提车信息数量 -->
    <select id="queryRepairTakeInfoNum" parameterType="com.extracme.evcard.mtc.bo.QueryRepairTakeInfoBO" resultType="int">
        select
          count(*)
        from
        ${mtcSchema}.mtc_repair_task a
        where
        a.status = 1 and a.syn_take_time is not null
        <if test="orgId != null and orgId !=''">
            and a.org_id like concat(#{orgId},'%')
        </if>
        <if test="repairDepotId != null and repairDepotId != ''">
            and repair_depot_id = #{repairDepotId}
        </if>
        <if test="renttype != null">
            and a.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
            <if test="renttypeList.size() > 0">
                and renttype in
                <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="renttypeList.size() == 0">
                and renttype = -2
            </if>
        </if>
        <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test= "repairTypeId == 5 " >
            and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
            and a.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskNo != null and taskNo != ''">
            and a.task_no = #{taskNo}
        </if>
        <if test="vin != null and vin != ''">
            and a.vin = #{vin}
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
            and a.vehicle_no like concat(#{vehicleNo},'%')
        </if>
        <if test="repairDepotName != null and repairDepotName != ''">
            and a.repair_depot_name like concat('%',#{repairDepotName},'%')
        </if>
        <if test="synTakeStartTime != null">
            and a.syn_take_time >= #{synTakeStartTime}
        </if>
        <if test="synTakeEndTime != null">
            and a.syn_take_time <![CDATA[<]]> #{synTakeEndTime}
        </if>
    </select>

    <!-- 批量查询换件信息 -->
    <select id="queryReplaceItemDetailList" resultMap="ReplaceItemDetailResultMap">
	SELECT
		d.task_no AS replace_task_no,
		d.id AS replaceId,
		d.price_programme AS replace_price_programme,
		d.grouping_id AS replace_grouping_id,
		d.grouping_name AS replace_grouping_name,
		d.part_name,
		d.part_number,
		d.unit_price,
		d.insurance_quote_amount,
		d.view_amount AS replace_view_amount,
		d.remark AS replace_remark,
		d.original_factory_part_no
	FROM
		${mtcSchema}.mtc_replace_item_detail d
	WHERE
		d.status = 1 and d.task_no in
		<foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.taskNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 批量查询工时信息 -->
    <select id="queryRepairItemDetailList" resultMap="RepairItemDetailResultMap">
    SELECT
         c.task_no AS repair_task_no,
         c.id AS repairId,
         c.price_programme,
         c.grouping_id,
         c.grouping_name,
         c.repair_name,
         c.repair_amount,
         c.view_amount,
         c.remark
	 from
	    ${mtcSchema}.mtc_repair_item_detail c
	 WHERE
	    c.status = 1 and c.task_no in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.taskNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 批量销案 -->
    <update id="batchSellCase">
        update
          ${mtcSchema}.mtc_repair_task
        set
          material_collection_task_schedule = 730,
          verification_loss_task_oper_id = null,
          update_time = #{updateTime,jdbcType=TIMESTAMP},
          update_oper_id = #{updateOperId,jdbcType=BIGINT},
          update_oper_name = #{updateOperName,jdbcType=VARCHAR}
        where
          id in
          <foreach collection="idList" item="item" open="(" close=")" separator=",">
              #{item,jdbcType=BIGINT}
          </foreach>
    </update>

    <!-- 清除任务占据人 -->
    <update id="clearOwner">
        update
        ${mtcSchema}.mtc_repair_task
        set
        verification_loss_task_schedule = 300,
        verification_loss_task_oper_id = null,
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
        update_oper_name = #{updateOperName,jdbcType=VARCHAR}
        where
        id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据运营单位查询修理厂列表 -->
    <select id="queryRepairDepotListByOrg" resultType="com.evcard.mtc.provider.dto.RepairDepotDTO">
      select
        repair_depot_id as repairDepotId,
        repair_depot_name as repairDepotName
      from
        ${mtcSchema}.mtc_repair_depot_info
      where
        repair_depot_org_id like concat(#{orgId,jdbcType=VARCHAR},'%') and del_flag = 0 and sso_flag = 1 and status = 1
      union
      select
        b.repair_depot_id as repairDepotId,
        b.repair_depot_name as repairDepotName
      from
        ${mtcSchema}.mtc_repair_depot_cooperative_branch a left join
        ${mtcSchema}.mtc_repair_depot_info b on a.repair_depot_id = b.repair_depot_id
      where
        a.org_id like concat(#{orgId,jdbcType=VARCHAR},'%') and b.del_flag = 0 and b.sso_flag = 1 and b.status = 1
    </select>

    <!-- 查询车辆未完成任务 -->
    <select id="queryRepairTaskListByVin" parameterType="string" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List" />
        from
        ${mtcSchema}.mtc_repair_task
        where
        vin = #{vin,jdbcType=VARCHAR} and create_time > '2019-09-03'
    </select>
  
    <!-- 查询车辆未完成任务 -->
    <select id="queryUnclosedTaskList" parameterType="string" resultType="com.extracme.evcard.mtc.model.RepairTask">
      select
        repair_type_id as repairTypeId,
        vehicle_transfer_task_schedule as vehicleTransferTaskSchedule,
        insurance_quote_task_schedule as insuranceQuoteTaskSchedule,
        verification_loss_task_schedule as verificationLossTaskSchedule,
        reassignment_task_schedule as reassignmentTaskSchedule,
        vehicle_check_task_schedule as vehicleCheckTaskSchedule
      from
        ${mtcSchema}.mtc_repair_task
      where
        vin = #{vin,jdbcType=VARCHAR} and create_time > '2019-09-03' and current_tache <![CDATA[<=]]> 60
      order by
        update_time desc
    </select>

    <sql id="Query_Sub_Settlement_Report_Info_Where">
      <where>
        a.task_no = m.task_no 
        AND m.`status` = 1
        AND a.`status` = 1
        AND (
          (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
          OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
          OR (m.repair_type_id = 4)
        )
        <if test="orgId != null and orgId != ''">
          AND m.org_id LIKE CONCAT(#{orgId}, '%')
        </if>
        <if test="repairTypeId != null and repairTypeId != 5">
          AND m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test="repairTypeId == 5">
          AND m.maintain_to_repair_flag = 1 
          AND m.repair_type_id != 3
        </if>
        <if test="taskNo != null and taskNo != ''">
          AND m.task_no = #{taskNo}
        </if>
        
        <if test="vehicleNo != null and vehicleNo != ''">
          AND m.vehicle_no LIKE CONCAT(#{vehicleNo}, '%')
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
          AND m.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
          AND m.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
          AND m.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
          AND m.vehicle_recive_time &gt;= #{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
          AND m.vehicle_recive_time &lt;= #{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
          AND m.vehicle_repair_time &gt;= #{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
          AND m.vehicle_repair_time &lt;= #{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
          AND m.vehicle_check_time &gt;= #{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
          AND m.vehicle_check_time &lt;= #{vehicleCheckEndTime}
        </if>
        <if test="repairDepotIds != null and repairDepotIds.size() > 0">
          AND m.repair_depot_id IN
          <foreach item="item" index="index" collection="repairDepotIds" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        <if test="renttype != null">
          AND m.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
          <if test="renttypeList.size() > 0">
            AND m.renttype IN
            <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
              #{item}
            </foreach>
          </if>
          <if test="renttypeList.size() == 0">
            AND m.renttype = -2
          </if>
        </if>
      </where>
    </sql>

    <!-- 针对 mtc_replace_item_detail 表的 WHERE 条件 -->
    <sql id="Query_Sub_Settlement_Report_Info_Where_Replace_Item">
      <where>
        a.task_no = m.task_no
        AND m.`status` = 1
        AND a.`status` = 1
        AND (
          (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
          OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
          OR (m.repair_type_id = 4)
        )
        <if test="orgId != null and orgId != ''">
          AND m.org_id LIKE CONCAT(#{orgId}, '%')
        </if>
        <if test="repairTypeId != null and repairTypeId != 5">
          AND m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test="repairTypeId == 5">
          AND m.maintain_to_repair_flag = 1
          AND m.repair_type_id != 3
        </if>
        <if test="taskNo != null and taskNo != ''">
          AND m.task_no = #{taskNo}
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
          AND m.vehicle_no LIKE CONCAT(#{vehicleNo}, '%')
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
          AND m.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
          AND m.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
          AND m.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
          AND m.vehicle_recive_time &gt;= #{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
          AND m.vehicle_recive_time &lt;= #{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
          AND m.vehicle_repair_time &gt;= #{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
          AND m.vehicle_repair_time &lt;= #{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
          AND m.vehicle_check_time &gt;= #{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
          AND m.vehicle_check_time &lt;= #{vehicleCheckEndTime}
        </if>
        <if test="repairDepotId != null and repairDepotId != ''">
          AND m.repair_depot_id = #{repairDepotId}
        </if>
        <if test="renttype != null">
          AND m.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
          <if test="renttypeList.size() > 0">
            AND m.renttype IN
            <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
              #{item}
            </foreach>
          </if>
          <if test="renttypeList.size() == 0">
            AND m.renttype = -2
          </if>
        </if>
        <if test="projectType != null and projectType != ''">
          AND a.part_name LIKE CONCAT('%', #{projectType}, '%')
        </if>
      </where>
    </sql>

    <!-- 针对 mtc_repair_item_detail 表的 WHERE 条件 -->
    <sql id="Query_Sub_Settlement_Report_Info_Where_Repair_Item">
      <where>
        a.task_no = m.task_no
        AND m.`status` = 1
        AND a.`status` = 1
        AND (
          (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
          OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
          OR (m.repair_type_id = 4)
        )
        <if test="orgId != null and orgId != ''">
          AND m.org_id LIKE CONCAT(#{orgId}, '%')
        </if>
        <if test="repairTypeId != null and repairTypeId != 5">
          AND m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test="repairTypeId == 5">
          AND m.maintain_to_repair_flag = 1
          AND m.repair_type_id != 3
        </if>
        <if test="taskNo != null and taskNo != ''">
          AND m.task_no = #{taskNo}
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
          AND m.vehicle_no LIKE CONCAT(#{vehicleNo}, '%')
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
          AND m.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
          AND m.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
          AND m.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
          AND m.vehicle_recive_time &gt;= #{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
          AND m.vehicle_recive_time &lt;= #{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
          AND m.vehicle_repair_time &gt;= #{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
          AND m.vehicle_repair_time &lt;= #{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
          AND m.vehicle_check_time &gt;= #{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
          AND m.vehicle_check_time &lt;= #{vehicleCheckEndTime}
        </if>
        <if test="repairDepotId != null and repairDepotId != ''">
          AND m.repair_depot_id = #{repairDepotId}
        </if>
        <if test="renttype != null">
          AND m.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
          <if test="renttypeList.size() > 0">
            AND m.renttype IN
            <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
              #{item}
            </foreach>
          </if>
          <if test="renttypeList.size() == 0">
            AND m.renttype = -2
          </if>
        </if>
        <if test="projectType != null and projectType != ''">
          AND a.repair_name LIKE CONCAT('%', #{projectType}, '%')
        </if>
      </where>
    </sql>

    <sql id="Query_Sub_Settlement_Report_Info_Where_With_Check_State">
      <where>
        a.task_no = m.task_no 
        AND m.`status` = 1
        AND a.`status` = 1
        AND a.check_state = "01"
        AND (
          (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
          OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
          OR (m.repair_type_id = 4)
        )
        <if test="orgId != null and orgId != ''">
          AND m.org_id LIKE CONCAT(#{orgId}, '%')
        </if>
        <if test="repairTypeId != null and repairTypeId != 5">
          AND m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test="repairTypeId == 5">
          AND m.maintain_to_repair_flag = 1 
          AND m.repair_type_id != 3
        </if>
        <if test="taskNo != null and taskNo != ''">
          AND m.task_no = #{taskNo}
        </if>
        
        <if test="vehicleNo != null and vehicleNo != ''">
          AND m.vehicle_no LIKE CONCAT(#{vehicleNo}, '%')
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
          AND m.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
          AND m.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
          AND m.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
          AND m.vehicle_recive_time &gt;= #{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
          AND m.vehicle_recive_time &lt;= #{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
          AND m.vehicle_repair_time &gt;= #{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
          AND m.vehicle_repair_time &lt;= #{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
          AND m.vehicle_check_time &gt;= #{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
          AND m.vehicle_check_time &lt;= #{vehicleCheckEndTime}
        </if>
        <if test="repairDepotIds != null and repairDepotIds.size() > 0">
          AND m.repair_depot_id IN
          <foreach item="item" index="index" collection="repairDepotIds" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        <if test="renttype != null">
          AND m.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
          <if test="renttypeList.size() > 0">
            AND m.renttype IN
            <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
              #{item}
            </foreach>
          </if>
          <if test="renttypeList.size() == 0">
            AND m.renttype = -2
          </if>
        </if>
      </where>
    </sql>

    <!-- 针对带 item_name 字段的表的 WHERE 条件 -->
    <sql id="Query_Sub_Settlement_Report_Info_Where_Item_Name">
      <where>
        a.task_no = m.task_no
        AND m.`status` = 1
        AND a.`status` = 1
        AND a.check_state = "01"
        AND (
          (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
          OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
          OR (m.repair_type_id = 4)
        )
        <if test="orgId != null and orgId != ''">
          AND m.org_id LIKE CONCAT(#{orgId}, '%')
        </if>
        <if test="repairTypeId != null and repairTypeId != 5">
          AND m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test="repairTypeId == 5">
          AND m.maintain_to_repair_flag = 1
          AND m.repair_type_id != 3
        </if>
        <if test="taskNo != null and taskNo != ''">
          AND m.task_no = #{taskNo}
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
          AND m.vehicle_no LIKE CONCAT(#{vehicleNo}, '%')
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
          AND m.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
          AND m.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
          AND m.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
          AND m.vehicle_recive_time &gt;= #{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
          AND m.vehicle_recive_time &lt;= #{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
          AND m.vehicle_repair_time &gt;= #{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
          AND m.vehicle_repair_time &lt;= #{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
          AND m.vehicle_check_time &gt;= #{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
          AND m.vehicle_check_time &lt;= #{vehicleCheckEndTime}
        </if>
        <if test="repairDepotId != null and repairDepotId != ''">
          AND m.repair_depot_id = #{repairDepotId}
        </if>
        <if test="renttype != null">
          AND m.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
          <if test="renttypeList.size() > 0">
            AND m.renttype IN
            <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
              #{item}
            </foreach>
          </if>
          <if test="renttypeList.size() == 0">
            AND m.renttype = -2
          </if>
        </if>
        <if test="projectType != null and projectType != ''">
          AND a.item_name LIKE CONCAT('%', #{projectType}, '%')
        </if>
      </where>
    </sql>

    <!-- 针对 mtc_loss_info 表的 WHERE 条件（拖车费用，固定值，不需要projectType过滤） -->
    <sql id="Query_Sub_Settlement_Report_Info_Where_Loss_Info">
      <where>
        a.task_no = m.task_no
        AND m.`status` = 1
        AND a.`status` = 1
        AND (
          (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
          OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
          OR (m.repair_type_id = 4)
        )
        <if test="orgId != null and orgId != ''">
          AND m.org_id LIKE CONCAT(#{orgId}, '%')
        </if>
        <if test="repairTypeId != null and repairTypeId != 5">
          AND m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
        </if>
        <if test="repairTypeId == 5">
          AND m.maintain_to_repair_flag = 1
          AND m.repair_type_id != 3
        </if>
        <if test="taskNo != null and taskNo != ''">
          AND m.task_no = #{taskNo}
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
          AND m.vehicle_no LIKE CONCAT(#{vehicleNo}, '%')
        </if>
        <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
          AND m.vehicle_model_seq = #{vehicleModelSeq}
        </if>
        <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
          AND m.create_time &gt;= #{taskInflowStartTime}
        </if>
        <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
          AND m.create_time &lt;= #{taskInflowEndTime}
        </if>
        <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
          AND m.vehicle_recive_time &gt;= #{vehicleReciveStartTime}
        </if>
        <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
          AND m.vehicle_recive_time &lt;= #{vehicleReciveEndTime}
        </if>
        <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
          AND m.vehicle_repair_time &gt;= #{vehicleRepairStartTime}
        </if>
        <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
          AND m.vehicle_repair_time &lt;= #{vehicleRepairEndTime}
        </if>
        <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
          AND m.vehicle_check_time &gt;= #{vehicleCheckStartTime}
        </if>
        <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
          AND m.vehicle_check_time &lt;= #{vehicleCheckEndTime}
        </if>
        <if test="repairDepotId != null and repairDepotId != ''">
          AND m.repair_depot_id = #{repairDepotId}
        </if>
        <if test="renttype != null">
          AND m.renttype = #{renttype}
        </if>
        <if test="renttypeList != null">
          <if test="renttypeList.size() > 0">
            AND m.renttype IN
            <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
              #{item}
            </foreach>
          </if>
          <if test="renttypeList.size() == 0">
            AND m.renttype = -2
          </if>
        </if>
        <if test="projectType != null and projectType != '' and projectType != '拖车费用'">
          AND 1 = 0  <!-- 如果查询的不是拖车费用，则排除此表的结果 -->
        </if>
      </where>
    </sql>

   <!-- 获取结算子报表明细 -->
    <select id="getSubSettlementReportInfoForCount" parameterType="com.extracme.evcard.mtc.bo.SettlementReportInfoQueryBO"
            resultType="java.lang.Integer">
        <if test="subType == 1">
            select IFNULL(count1,0) + IFNULL(count2,0) as total
            FROM
            (
            SELECT
            COUNT(*) as count1
            FROM
            ${mtcSchema}.mtc_replace_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Replace_Item" />
            ) a,
            (
            SELECT
            COUNT(*) as count2
            FROM
            ${mtcSchema}.mtc_loss_fit_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            ) b
        </if>
        <if test="subType == 2">
            select IFNULL(count1,0) + IFNULL(count2,0) as total
            FROM
            (
            SELECT
            COUNT(*) as count1
            FROM
            ${mtcSchema}.mtc_repair_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Repair_Item" />
            ) a,
            (
            SELECT
            COUNT(*) as count2
            FROM
            ${mtcSchema}.mtc_loss_repair_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            ) b
        </if>

        <if test="subType == 3" >
            SELECT
            count(*) as total
            FROM
            ${mtcSchema}.mtc_loss_assist_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
        </if>

        <if test="subType == 4" >
            SELECT
            count(*) as total
            FROM
            ${mtcSchema}.mtc_loss_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Loss_Info" />
        </if>

        <if test="subType == null" >
            select IFNULL(count1,0) + IFNULL(count2,0) + IFNULL(count3,0) + IFNULL(count4,0) + IFNULL(count5,0) + IFNULL(count6,0) as total
            FROM
            (
            SELECT
            COUNT(*) as count1
            FROM
            ${mtcSchema}.mtc_replace_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Replace_Item" />
            ) a,
            (
            SELECT
            COUNT(*) as count2
            FROM
            ${mtcSchema}.mtc_repair_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Repair_Item" />
            ) b,
            (
            SELECT
            COUNT(*) as count3
            FROM
            ${mtcSchema}.mtc_loss_fit_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            ) c,
            (
            SELECT
            COUNT(*) as count4
            FROM
            ${mtcSchema}.mtc_loss_repair_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            ) d,
            (
            SELECT
            COUNT(*) as count5
            FROM
            mtc.mtc_loss_assist_info a,
            mtc.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            ) e,
            (
            SELECT
            COUNT(*) as count6
            FROM
            ${mtcSchema}.mtc_loss_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Loss_Info" />
            ) f
        </if>

    </select>

   <!-- 获取结算子报表明细 -->
    <select id="getSubSettlementReportInfo" parameterType="com.extracme.evcard.mtc.bo.SettlementReportInfoQueryBO"
            resultType="com.extracme.evcard.mtc.bo.SubSettlementReportBO">

        <bind name="key_offset" value="(pageNum - 1) * pageSize"></bind>

        <if test="subType == 1">
            select * from (
            (
            SELECT
            '配件项目' AS subType,
            a.part_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.insurance_quote_amount AS refFee,
            a.view_amount AS lossFee,
            a.part_number AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他' WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_replace_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Replace_Item" />
            LIMIT 1000
            )

            UNION ALL

            (
            SELECT
            '配件项目' AS subType,
            a.item_name AS projectType,
            CASE a. chg_comp_set_code WHEN  1 THEN '系统4S店价' WHEN 2 THEN '系统市场价' ELSE '' END AS priceOrigin,
            '' AS workType,
            CASE a.chg_comp_set_code WHEN  1 THEN a.sys_guide_price WHEN 2 THEN a.sys_market_price else '' end AS refFee,
            a.audit_material_fee AS lossFee,
            a.audit_count AS partNum,
            a.appr_remains_price as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_fit_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            LIMIT 1000
            )
            ) as a
            ORDER BY a.taskNo
            LIMIT #{pageSize} OFFSET #{key_offset}
        </if>

        <if test="subType == 2">
            select * from (
            (
            SELECT
            '工时项目' AS subType,
            a.repair_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.repair_amount AS refFee,
            a.view_amount AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_repair_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Repair_Item" />
            LIMIT 1000
            )

            UNION ALL

            (
            SELECT
            '工时项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            CASE a. repair_mode_code  WHEN 1 THEN '喷漆项目' WHEN 2 THEN '钣金项目' WHEN 3 THEN '电工项目' WHEN 4 THEN '机修项目' WHEN 5 THEN
            '拆装项目' ELSE '' END AS workType,
            a.manpower_ref_fee AS refFee,
            a.audit_manpower_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN'课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_repair_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            LIMIT 1000
            )
            ) as a
            ORDER BY a.taskNo
            LIMIT #{pageSize} OFFSET #{key_offset}
        </if>

        <if test="subType == 3" >
            SELECT
            '辅料项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            a.eval_mate_sum AS refFee,
            a.appr_mate_sum AS lossFee,
            a.audit_count AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_assist_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            LIMIT #{pageSize} OFFSET #{key_offset}
        </if>

        <if test="subType == 4" >
            SELECT
            '拖车费用' AS subType,
            '拖车费用' AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            '' AS refFee,
            a.audit_salvage_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Loss_Info" />
            LIMIT #{pageSize} OFFSET #{key_offset}
        </if>

        <if test="subType == null" >
            select * from (
            (
            SELECT
            '配件项目' AS subType,
            a.part_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.insurance_quote_amount AS refFee,
            a.view_amount AS lossFee,
            a.part_number AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他' WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_replace_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Replace_Item" />
            LIMIT 1000
            )

            UNION ALL

            (
            SELECT
            '工时项目' AS subType,
            a.repair_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.repair_amount AS refFee,
            a.view_amount AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_repair_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Repair_Item" />
            LIMIT 1000
            )

            UNION ALL

            (
            SELECT
            '配件项目' AS subType,
            a.item_name AS projectType,
            CASE a. chg_comp_set_code WHEN  1 THEN '系统4S店价' WHEN 2 THEN '系统市场价' ELSE '' END AS priceOrigin,
            '' AS workType,
            CASE a.chg_comp_set_code WHEN  1 THEN a.sys_guide_price WHEN 2 THEN a.sys_market_price else '' end AS refFee,
            a.audit_material_fee AS lossFee,
            a.audit_count AS partNum,
            a.appr_remains_price as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_fit_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            LIMIT 1000
            )

            UNION ALL

            (
            SELECT
            '工时项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            CASE a. repair_mode_code  WHEN 1 THEN '喷漆项目' WHEN 2 THEN '钣金项目' WHEN 3 THEN '电工项目' WHEN 4 THEN '机修项目' WHEN 5 THEN
            '拆装项目' ELSE '' END AS workType,
            a.manpower_ref_fee AS refFee,
            a.audit_manpower_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN'课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_repair_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            LIMIT 1000
            )

            UNION ALL

            (
            SELECT
            '辅料项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            a.eval_mate_sum AS refFee,
            a.appr_mate_sum AS lossFee,
            a.audit_count AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_assist_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Item_Name" />
            LIMIT 1000
            )

            UNION ALL

            (
            SELECT
            '拖车费用' AS subType,
            '拖车费用' AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            '' AS refFee,
            a.audit_salvage_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Query_Sub_Settlement_Report_Info_Where_Loss_Info" />
            LIMIT 1000
            )
            ) as a
            ORDER BY a.taskNo
            LIMIT #{pageSize} OFFSET #{key_offset}
        </if>

    </select>

    <sql id="Export_Sub_Settlement_Report_Info_Where">
        <where>
            a.task_no = m.task_no 
            AND m.`status` = 1
            AND a.`status` = 1
            AND (
            (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
            OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
            OR (m.repair_type_id = 4)
            )
            <if test="orgId != null and orgId != ''">
                and m.org_id like concat(#{orgId},'%')
            </if>
            <if test="repairTypeId != null and repairTypeId != 5">
                and m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId == 5">
                and m.maintain_to_repair_flag = 1 and m.repair_type_id != 3
            </if>
            <if test="taskNo != null and taskNo != ''">
                and m.task_no=#{taskNo}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                and m.vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
                and m.vehicle_model_seq = #{vehicleModelSeq}
            </if>
            <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
                and m.create_time &gt;= #{taskInflowStartTime}
            </if>
            <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
                and m.create_time &lt;= #{taskInflowEndTime}
            </if>
            <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
                and m.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
            </if>
            <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
                and m.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
            </if>
            <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
                and m.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
            </if>
            <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
                and m.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
            </if>
            <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
                and m.vehicle_check_time &gt;=#{vehicleCheckStartTime}
            </if>
            <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
                and m.vehicle_check_time &lt;=#{vehicleCheckEndTime}
            </if>
            <if test="repairDepotIds != null and repairDepotIds.size() > 0">
                and m.repair_depot_id in
                <foreach item = "item" index = "index" collection = "repairDepotIds" open = "(" separator = "," close = ")">
                    #{item}
                </foreach>
            </if>
            <if test="renttype != null">
                and m.renttype = #{renttype}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and m.renttype in
                    <foreach collection="renttypeList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and m.renttype = -2
                </if>
            </if>
            <if test="list != null and list.size() > 0">
                and m.task_no in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.taskNo,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="Export_Sub_Settlement_Report_Info_Where_With_Check_State">
        <where>
            a.task_no = m.task_no 
            AND m.`status` = 1
            AND a.`status` = 1
            AND a.check_state = "01"
            AND (
            (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
            OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
            OR (m.repair_type_id = 4)
            )
            <if test="orgId != null and orgId != ''">
                and m.org_id like concat(#{orgId},'%')
            </if>
            <if test="repairTypeId != null and repairTypeId != 5">
                and m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId == 5">
                and m.maintain_to_repair_flag = 1 and m.repair_type_id != 3
            </if>
            <if test="taskNo != null and taskNo != ''">
                and m.task_no=#{taskNo}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                and m.vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
                and m.vehicle_model_seq = #{vehicleModelSeq}
            </if>
            <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
                and m.create_time &gt;= #{taskInflowStartTime}
            </if>
            <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
                and m.create_time &lt;= #{taskInflowEndTime}
            </if>
            <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
                and m.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
            </if>
            <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
                and m.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
            </if>
            <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
                and m.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
            </if>
            <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
                and m.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
            </if>
            <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
                and m.vehicle_check_time &gt;=#{vehicleCheckStartTime}
            </if>
            <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
                and m.vehicle_check_time &lt;=#{vehicleCheckEndTime}
            </if>
            <if test="repairDepotIds != null and repairDepotIds.size() > 0">
                and m.repair_depot_id in
                <foreach item = "item" index = "index" collection = "repairDepotIds" open = "(" separator = "," close = ")">
                    #{item}
                </foreach>
            </if>
            <if test="renttype != null">
                and m.renttype = #{renttype}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and m.renttype in
                    <foreach collection="renttypeList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and m.renttype = -2
                </if>
            </if>
            <if test="list != null and list.size() > 0">
                and m.task_no in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.taskNo,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- Export版本 - 针对 mtc_replace_item_detail 表的 WHERE 条件 -->
    <sql id="Export_Sub_Settlement_Report_Info_Where_Replace_Item">
        <where>
            a.task_no = m.task_no
            AND m.`status` = 1
            AND a.`status` = 1
            AND (
            (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
            OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
            OR (m.repair_type_id = 4)
            )
            <if test="orgId != null and orgId != ''">
                and m.org_id like concat(#{orgId},'%')
            </if>
            <if test="repairTypeId != null and repairTypeId != 5">
                and m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId == 5">
                and m.maintain_to_repair_flag = 1 and m.repair_type_id != 3
            </if>
            <if test="taskNo != null and taskNo != ''">
                and m.task_no=#{taskNo}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                and m.vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
                and m.vehicle_model_seq = #{vehicleModelSeq}
            </if>
            <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
                and m.create_time &gt;= #{taskInflowStartTime}
            </if>
            <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
                and m.create_time &lt;= #{taskInflowEndTime}
            </if>
            <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
                and m.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
            </if>
            <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
                and m.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
            </if>
            <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
                and m.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
            </if>
            <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
                and m.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
            </if>
            <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
                and m.vehicle_check_time &gt;=#{vehicleCheckStartTime}
            </if>
            <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
                and m.vehicle_check_time &lt;=#{vehicleCheckEndTime}
            </if>
            <if test="repairDepotId != null and repairDepotId != ''">
                and m.repair_depot_id = #{repairDepotId}
            </if>
            <if test="renttype != null">
                and m.renttype = #{renttype}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and m.renttype in
                    <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and m.renttype = -2
                </if>
            </if>
            <if test="list != null and list.size() > 0">
                and m.task_no in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.taskNo,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="projectType != null and projectType != ''">
                and a.part_name like concat('%',#{projectType},'%')
            </if>
        </where>
    </sql>

    <!-- Export版本 - 针对 mtc_repair_item_detail 表的 WHERE 条件 -->
    <sql id="Export_Sub_Settlement_Report_Info_Where_Repair_Item">
        <where>
            a.task_no = m.task_no
            AND m.`status` = 1
            AND a.`status` = 1
            AND (
            (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
            OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
            OR (m.repair_type_id = 4)
            )
            <if test="orgId != null and orgId != ''">
                and m.org_id like concat(#{orgId},'%')
            </if>
            <if test="repairTypeId != null and repairTypeId != 5">
                and m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId == 5">
                and m.maintain_to_repair_flag = 1 and m.repair_type_id != 3
            </if>
            <if test="taskNo != null and taskNo != ''">
                and m.task_no=#{taskNo}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                and m.vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
                and m.vehicle_model_seq = #{vehicleModelSeq}
            </if>
            <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
                and m.create_time &gt;= #{taskInflowStartTime}
            </if>
            <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
                and m.create_time &lt;= #{taskInflowEndTime}
            </if>
            <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
                and m.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
            </if>
            <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
                and m.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
            </if>
            <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
                and m.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
            </if>
            <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
                and m.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
            </if>
            <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
                and m.vehicle_check_time &gt;=#{vehicleCheckStartTime}
            </if>
            <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
                and m.vehicle_check_time &lt;=#{vehicleCheckEndTime}
            </if>
            <if test="repairDepotId != null and repairDepotId != ''">
                and m.repair_depot_id = #{repairDepotId}
            </if>
            <if test="renttype != null">
                and m.renttype = #{renttype}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and m.renttype in
                    <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and m.renttype = -2
                </if>
            </if>
            <if test="list != null and list.size() > 0">
                and m.task_no in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.taskNo,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="projectType != null and projectType != ''">
                and a.repair_name like concat('%',#{projectType},'%')
            </if>
        </where>
    </sql>

    <!-- Export版本 - 针对带 item_name 字段的表的 WHERE 条件 -->
    <sql id="Export_Sub_Settlement_Report_Info_Where_Item_Name">
        <where>
            a.task_no = m.task_no
            AND m.`status` = 1
            AND a.`status` = 1
            AND a.check_state = "01"
            AND (
            (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
            OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
            OR (m.repair_type_id = 4)
            )
            <if test="orgId != null and orgId != ''">
                and m.org_id like concat(#{orgId},'%')
            </if>
            <if test="repairTypeId != null and repairTypeId != 5">
                and m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId == 5">
                and m.maintain_to_repair_flag = 1 and m.repair_type_id != 3
            </if>
            <if test="taskNo != null and taskNo != ''">
                and m.task_no=#{taskNo}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                and m.vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
                and m.vehicle_model_seq = #{vehicleModelSeq}
            </if>
            <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
                and m.create_time &gt;= #{taskInflowStartTime}
            </if>
            <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
                and m.create_time &lt;= #{taskInflowEndTime}
            </if>
            <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
                and m.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
            </if>
            <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
                and m.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
            </if>
            <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
                and m.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
            </if>
            <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
                and m.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
            </if>
            <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
                and m.vehicle_check_time &gt;=#{vehicleCheckStartTime}
            </if>
            <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
                and m.vehicle_check_time &lt;=#{vehicleCheckEndTime}
            </if>
            <if test="repairDepotId != null and repairDepotId != ''">
                and m.repair_depot_id = #{repairDepotId}
            </if>
            <if test="renttype != null">
                and m.renttype = #{renttype}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and m.renttype in
                    <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and m.renttype = -2
                </if>
            </if>
            <if test="list != null and list.size() > 0">
                and m.task_no in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.taskNo,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="projectType != null and projectType != ''">
                and a.item_name like concat('%',#{projectType},'%')
            </if>
        </where>
    </sql>

    <!-- Export版本 - 针对 mtc_loss_info 表的 WHERE 条件 -->
    <sql id="Export_Sub_Settlement_Report_Info_Where_Loss_Info">
        <where>
            a.task_no = m.task_no
            AND m.`status` = 1
            AND a.`status` = 1
            AND (
            (m.repair_type_id IN (1, 2, 6,9) AND m.vehicle_check_time IS NOT NULL AND m.check_result_flag = 0)
            OR (m.repair_type_id = 3 AND m.insurance_quote_task_schedule = 230)
            OR (m.repair_type_id = 4)
            )
            <if test="orgId != null and orgId != ''">
                and m.org_id like concat(#{orgId},'%')
            </if>
            <if test="repairTypeId != null and repairTypeId != 5">
                and m.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId == 5">
                and m.maintain_to_repair_flag = 1 and m.repair_type_id != 3
            </if>
            <if test="taskNo != null and taskNo != ''">
                and m.task_no=#{taskNo}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                and m.vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
                and m.vehicle_model_seq = #{vehicleModelSeq}
            </if>
            <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
                and m.create_time &gt;= #{taskInflowStartTime}
            </if>
            <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
                and m.create_time &lt;= #{taskInflowEndTime}
            </if>
            <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
                and m.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
            </if>
            <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
                and m.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
            </if>
            <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
                and m.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
            </if>
            <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
                and m.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
            </if>
            <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
                and m.vehicle_check_time &gt;=#{vehicleCheckStartTime}
            </if>
            <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
                and m.vehicle_check_time &lt;=#{vehicleCheckEndTime}
            </if>
            <if test="repairDepotId != null and repairDepotId != ''">
                and m.repair_depot_id = #{repairDepotId}
            </if>
            <if test="renttype != null">
                and m.renttype = #{renttype}
            </if>
            <if test="renttypeList != null">
                <if test="renttypeList.size() > 0">
                    and m.renttype in
                    <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="renttypeList.size() == 0">
                    and m.renttype = -2
                </if>
            </if>
            <if test="list != null and list.size() > 0">
                and m.task_no in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.taskNo,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="projectType != null and projectType != '' and projectType != '拖车费用'">
                and 1 = 0  <!-- 如果查询的不是拖车费用，则排除此表的结果 -->
            </if>
        </where>
    </sql>

   <!-- 获取结算子报表明细 -->
    <select id="getSubSettlementReportInfoForExcel" parameterType="com.extracme.evcard.mtc.bo.SettlementReportInfoQueryBO"
            resultType="com.extracme.evcard.mtc.bo.SubSettlementReportBO">

        <if test="subType == 1">
            (
            SELECT
            '配件项目' AS subType,
            a.part_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.insurance_quote_amount AS refFee,
            a.view_amount AS lossFee,
            a.part_number AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他' WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_replace_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Replace_Item" />
            )

            UNION ALL

            (
            SELECT
            '配件项目' AS subType,
            a.item_name AS projectType,
            CASE a. chg_comp_set_code WHEN  1 THEN '系统4S店价' WHEN 2 THEN '系统市场价' ELSE '' END AS priceOrigin,
            '' AS workType,
            CASE a.chg_comp_set_code WHEN  1 THEN a.sys_guide_price WHEN 2 THEN a.sys_market_price else '' end AS refFee,
            a.audit_material_fee AS lossFee,
            a.audit_count AS partNum,
            a.appr_remains_price as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_fit_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Item_Name" />
            )
        </if>

        <if test="subType == 2">

            (
            SELECT
            '工时项目' AS subType,
            a.repair_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.repair_amount AS refFee,
            a.view_amount AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_repair_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Repair_Item" />
            )

            UNION ALL

            (
            SELECT
            '工时项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            CASE a. repair_mode_code  WHEN 1 THEN '喷漆项目' WHEN 2 THEN '钣金项目' WHEN 3 THEN '电工项目' WHEN 4 THEN '机修项目' WHEN 5 THEN
            '拆装项目' ELSE '' END AS workType,
            a.manpower_ref_fee AS refFee,
            a.audit_manpower_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN'课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_repair_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Item_Name" />
            )
        </if>

        <if test="subType == 3">
            (
            SELECT
            '辅料项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            a.eval_mate_sum AS refFee,
            a.appr_mate_sum AS lossFee,
            a.audit_count AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_assist_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Item_Name" />
            )
        </if>

        <if test="subType == 4">
            (
            SELECT
            '拖车费用' AS subType,
            '拖车费用' AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            '' AS refFee,
            a.audit_salvage_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Loss_Info" />
            )
        </if>

        <if test="subType == null">
            (
            SELECT
            '配件项目' AS subType,
            a.part_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.insurance_quote_amount AS refFee,
            a.view_amount AS lossFee,
            a.part_number AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他' WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_replace_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Replace_Item" />
            )

            UNION ALL

            (
            SELECT
            '工时项目' AS subType,
            a.repair_name AS projectType,
            CASE a.price_programme WHEN 0 THEN '本地市场价' WHEN 1 THEN '自定义项目' ELSE '' END AS priceOrigin,
            '' AS workType,
            a.repair_amount AS refFee,
            a.view_amount AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_repair_item_detail a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Repair_Item" />
            )

            UNION ALL

            (
            SELECT
            '配件项目' AS subType,
            a.item_name AS projectType,
            CASE a. chg_comp_set_code WHEN  1 THEN '系统4S店价' WHEN 2 THEN '系统市场价' ELSE '' END AS priceOrigin,
            '' AS workType,
            CASE a.chg_comp_set_code WHEN  1 THEN a.sys_guide_price WHEN 2 THEN a.sys_market_price else '' end AS refFee,
            a.audit_material_fee AS lossFee,
            a.audit_count AS partNum,
            a.appr_remains_price as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_fit_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Item_Name" />
            )

            UNION ALL

            (
            SELECT
            '工时项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            CASE a. repair_mode_code  WHEN 1 THEN '喷漆项目' WHEN 2 THEN '钣金项目' WHEN 3 THEN '电工项目' WHEN 4 THEN '机修项目' WHEN 5 THEN
            '拆装项目' ELSE '' END AS workType,
            a.manpower_ref_fee AS refFee,
            a.audit_manpower_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN'课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_repair_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Item_Name" />
            )

            UNION ALL

            (
            SELECT
            '辅料项目' AS subType,
            a.item_name AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            a.eval_mate_sum AS refFee,
            a.appr_mate_sum AS lossFee,
            a.audit_count AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_assist_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Item_Name" />
            )

            UNION ALL

            (
            SELECT
            '拖车费用' AS subType,
            '拖车费用' AS projectType,
            '' AS priceOrigin,
            '' AS workType,
            '' AS refFee,
            a.audit_salvage_fee AS lossFee,
            '' AS partNum,
            '' as residualValue,
            m.task_no AS taskNo,
            m.vehicle_no AS vehicleNo,
            CASE m.renttype WHEN 0 THEN '分时租赁' WHEN 1 THEN'长租' WHEN 2 THEN '专车' WHEN 3 THEN '短租' WHEN 4 THEN '公务用车'
            WHEN 5 THEN '市场用车' WHEN 6 THEN '其他'  WHEN 7 THEN '课题测试' ELSE '' END AS rentType,
            m.vehicle_model_info AS vehicleModelName,
            m.vin AS vin,
            IF (maintain_to_repair_flag = 1 and repair_type_id != 3,'自费维修（原车辆保养）',m.repair_type_name) AS repairTypeName,
            m.repair_depot_name AS repairDepotName,
            ifnull(DATE_FORMAT(m.vehicle_recive_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleReceiveTime,
            ifnull(DATE_FORMAT(m.vehicle_repair_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleRepairFinishTime,
            ifnull(DATE_FORMAT(m.create_time,'%Y-%m-%d %H:%i:%S'),'') AS taskInflowTime,
            ifnull(DATE_FORMAT(m.vehicle_check_time,'%Y-%m-%d %H:%i:%S'),'') AS vehicleCheckFinishTime,
            m.repair_grade AS taskGrade,
            m.org_name AS operationOrgName
            FROM
            ${mtcSchema}.mtc_loss_info a,
            ${mtcSchema}.mtc_repair_task m
            <include refid="Export_Sub_Settlement_Report_Info_Where_Loss_Info" />
            )
        </if>

    </select>
   <!-- 查询结算报表 -->
  <select id="getSettlementReportInfoForExcel" parameterType="com.extracme.evcard.mtc.bo.SettlementReportInfoQueryBO"
          resultType="com.extracme.evcard.mtc.bo.SettlementReportBO">
    select
      a.repair_depot_id,
      mrdi.repair_depot_name repairDepotName,
      a.org_name orgName,
      a.operate_org_name operateOrgName,
      a.task_no taskNo,
      if(maintain_to_repair_flag = 1 and repair_type_id != 3, '自费维修（原车辆保养）', a.repair_type_name) as repairTypeName,
      a.repair_grade repairGrade,
      a.vehicle_no vehicleNo,
      a.vehicle_model_info vehicleModelName,
      a.vin,
      a.create_time taskInflowTime,
      a.vehicle_recive_time vehicleReciveTime,
      a.verification_loss_check_time verificationLossCheckTime,
      a.vehicle_repair_time vehicleRepairFinishTime,
      a.vehicle_check_time vehicleCheckFinishTime,
      a.vehicle_replace_total_amount partTotalMoney,
      a.vehicle_repair_total_amount repairTotalMoney,
      a.vehicle_insurance_total_amount totalMoney,
      a.vehicle_insurance_total_amount - (a.vehicle_replace_total_amount + a.vehicle_repair_total_amount) as otherMoney,
      a.org_id,
      a.repair_type_id repairTypeId,
      a.vehicle_model_seq,
      a.update_time updateTime,
      a.maintain_to_repair_flag,
      a.renttype renttype,
      ifnull(a.maintain_amount, 0) as maintainAmount,
      a.tireNumber tireNumber,
      a.total_mileage as totalMileage,
      a.no_deductibles_flag as noDeductiblesFlag,
      a.fact_operate_tag as factOperateTag,
      a.confirm_type as confirmType,
      a.vehicle_scrape_value as vehicleScrapeValue,
      a.review_to_sel_fee_flag as reviewToSelFeeFlag,
      a.accident_report_number as accidentReportNumber,
      a.estimated_claim_amount as estimatedClaimAmount,
      a.repair_settle_amount as repairSettleAmount,
      a.repair_fax_settle_amount as repairFaxSettleAmount,
      mtrt.manual_no as manualNo,
      a.confirm_car_damage_type as confirmCarDamageType,
      ifnull(a.repair_review_total_amount,0) as repairReviewTotalAmount,
      ifnull(a.repair_insurance_total_amount,0)  as repairInsuranceTotalAmount,
      ifnull(a.vehicle_insurance_total_amount,0)  as vehicleInsuranceTotalAmount,
      a.user_assumed_amount userAssumedAmount,
      a.self_funded_amount selfFundedAmount,
      a.associated_order as associatedOrder,
      a.settlement_no as settlementNo,
      a.continue_days as continueDays
    from ${mtcSchema}.mtc_repair_task a
    left join ${mtcSchema}.mtc_manual_task_repair_task mtrt on mtrt.task_no = a.task_no and mtrt.status = 1
    left join ${mtcSchema}.mtc_repair_depot_info mrdi on a.repair_depot_id = mrdi.repair_depot_id
    where
    a.`status` = 1
    and (
    (a.repair_type_id in (1, 2, 3, 6, 7,9) and a.vehicle_check_time is not null and a.check_result_flag = 0)
    or
    (a.repair_type_id = 4)
    )
    <if test="orgId != null and orgId != ''">
      and a.org_id like concat(#{orgId},'%')
    </if>
    <if test="repairTypeId != null and repairTypeId != 5">
      and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
    </if>
    <if test="repairTypeId == 5">
      and a.maintain_to_repair_flag = 1 and a.repair_type_id != 3
    </if>
    <if test="taskNo != null and taskNo != ''">
      and a.task_no=#{taskNo}
    </if>
    <if test="vehicleNo != null and vehicleNo != ''">
      and a.vehicle_no like concat(#{vehicleNo},'%')
    </if>
    <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
      and a.vehicle_model_seq = #{vehicleModelSeq}
    </if>
    <if test="taskInflowStartTime != null and taskInflowStartTime != ''">
      and a.create_time &gt;= #{taskInflowStartTime}
    </if>
    <if test="taskInflowEndTime != null and taskInflowEndTime != ''">
      and a.create_time &lt;= #{taskInflowEndTime}
    </if>
    <if test="vehicleReciveStartTime != null and vehicleReciveStartTime != ''">
      and a.vehicle_recive_time &gt;=#{vehicleReciveStartTime}
    </if>
    <if test="vehicleReciveEndTime != null and vehicleReciveEndTime != ''">
      and a.vehicle_recive_time &lt;=#{vehicleReciveEndTime}
    </if>
    <if test="vehicleRepairStartTime != null and vehicleRepairStartTime != ''">
      and a.vehicle_repair_time &gt;=#{vehicleRepairStartTime}
    </if>
    <if test="vehicleRepairEndTime != null and vehicleRepairEndTime != ''">
      and a.vehicle_repair_time &lt;=#{vehicleRepairEndTime}
    </if>
    <if test="vehicleCheckStartTime != null and vehicleCheckStartTime != ''">
      and a.vehicle_check_time &gt;=#{vehicleCheckStartTime}
    </if>
    <if test="vehicleCheckEndTime != null and vehicleCheckEndTime != ''">
      and a.vehicle_check_time &lt;=#{vehicleCheckEndTime}
    </if>
    <if test="verificationLossCheckStartTime != null and verificationLossCheckStartTime != ''">
      and a.verification_loss_check_time &gt;=#{verificationLossCheckStartTime}
    </if>
    <if test="verificationLossCheckEndTime != null and verificationLossCheckStartTime != ''">
      and a.verification_loss_check_time &lt;=#{verificationLossCheckEndTime}
    </if>
    <if test="repairDepotIds != null and repairDepotIds.size() > 0">
      and a.repair_depot_id in
      <foreach item="item" index="index" collection="repairDepotIds" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="renttype != null">
      and a.renttype = #{renttype}
    </if>
    <if test="factOperateTag != null">
      and a.fact_operate_tag = #{factOperateTag}
    </if>
    <if test="renttypeList != null">
      <if test="renttypeList.size() > 0">
        and a.renttype in
        <foreach collection="renttypeList" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="renttypeList.size() == 0">
        and a.renttype = -2
      </if>
    </if>
    <if test="noDeductiblesFlag != null or noDeductiblesFlag == 0">
      and a.no_deductibles_flag = #{noDeductiblesFlag}
    </if>
    <if test="accidentNo != null and accidentNo != ''">
      and a.accident_no = #{accidentNo}
    </if>
    <if test="reviewToSelFeeFlag != null">
      and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
    </if>
    <if test="queryIndex != null">
      and a.task_no > #{queryIndex,jdbcType=VARCHAR}
    </if>
    order by a.task_no ASC
    limit #{pageSize,jdbcType=INTEGER}
  </select>
    
    <select id="queryRepairTypeId" parameterType="java.lang.String" resultType="java.lang.Integer">
     select repair_type_id from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo}
    </select>

   <!-- 保养待维修列表 -->
    <select id="queryMaintainToRepairList" parameterType="com.extracme.evcard.mtc.bo.MaintainToRepairQueryBO"
     resultType="com.extracme.evcard.mtc.bo.MaintainToRepairBO">
     select
      id as id,
      task_no as taskNo,
      vehicle_no as vehicleNo,
      renttype as renttype,
      vehicle_model_info as vehicleModelInfo,
      vin as vin,
      repair_type_id as repairTypeId,
      repair_type_name as repairTypeName,
      repair_depot_name as repairDepotName,
      vehicle_recive_time as vehicleReciveTime,
      maintain_to_repair_flag as maintainToRepairFlag,
      associated_task_no as associatedTaskNo,
      task_inflow_time as taskInflowTime,
      org_name as vehicleOrgName
     from ${mtcSchema}.mtc_repair_task
     where 
     current_tache = 100
     <if test="repairFlag == 0">
      and maintain_to_repair_flag = -1
     </if>
     <if test="repairFlag == 1">
      and maintain_to_repair_flag in (0, 1)
     </if>
     <if test="orgId!=null and orgId!=''">
      and org_id like concat(#{orgId},'%') 
     </if>
     <if test="vehicleNo != null and vehicleNo != ''">
      and vehicle_no like concat(#{vehicleNo,jdbcType=VARCHAR},'%')
     </if>
     <if test="taskNo != null and taskNo != ''">
      and task_no = #{taskNo,jdbcType=VARCHAR}
     </if>
     <if test="repairDepotName != null and repairDepotName != ''">
      and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
     </if>
     <if test="taskInflowTimeStart != null and taskInflowTimeStart != ''">
      and task_inflow_time >= #{taskInflowTimeStart,jdbcType=TIMESTAMP} 
     </if>
     <if test="taskInflowTimeEnd != null and taskInflowTimeEnd != ''">
      and DATE_ADD(task_inflow_time,INTERVAL -1 day) <![CDATA[<]]> #{taskInflowTimeEnd,jdbcType=TIMESTAMP} 
     </if>
     <if test="vehicleReciveTimeStart != null and vehicleReciveTimeStart != ''">
      and vehicle_recive_time >= #{vehicleReciveTimeStart,jdbcType=TIMESTAMP} 
     </if>
     <if test="vehicleReciveTimeEnd != null and vehicleReciveTimeEnd != ''">
      and DATE_ADD(vehicle_recive_time,INTERVAL -1 day) <![CDATA[<]]> #{vehicleReciveTimeEnd,jdbcType=TIMESTAMP} 
     </if> 
     <if test="vin != null and vin != ''">
      and vin = #{vin,jdbcType=VARCHAR} 
     </if>
     <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
      and vehicle_model_seq = #{vehicleModelSeq,jdbcType=BIGINT} 
     </if>
     <if test="renttype != null">
      and renttype = #{renttype, jdbcType=INTEGER}
     </if>
     <if test="associatedTaskNo != null and associatedTaskNo != ''">
      and associated_task_no like concat('%',#{associatedTaskNo, jdbcType=VARCHAR},'%')
     </if>
     order by create_time desc
    </select>
    
    <update id="updateMaintainToRepairFlag">
     update ${mtcSchema}.mtc_repair_task set 
      maintain_to_repair_flag = #{maintainToRepairFlag, jdbcType=INTEGER},
      <if test="associatedTaskNo !=null and associatedTaskNo != ''">
       associated_task_no = #{associatedTaskNo, jdbcType=VARCHAR},
      </if>
      update_oper_id = #{operId,jdbcType=BIGINT},
      update_oper_name = #{operName, jdbcType=VARCHAR},
      update_time = #{operTime, jdbcType=TIMESTAMP}
     where id in 
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
       #{id, jdbcType=BIGINT}
      </foreach>
    </update>
    
    <select id="queryAssociatedTaskNo" parameterType="java.lang.String" resultType="java.lang.String">
     select associated_task_no from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo}
    </select>
    
    <select id="queryMaintainToRepairDetail" resultType="com.extracme.evcard.mtc.bo.MaintainToRepairBO">
     select
      id as id,
      task_no as taskNo,
      vehicle_no as vehicleNo,
      renttype as renttype,
      vehicle_model_info as vehicleModelInfo,
      vin as vin,
      repair_type_id as repairTypeId,
      repair_type_name as repairTypeName,
      vehicle_recive_time as vehicleReciveTime,
      maintain_to_repair_flag as maintainToRepairFlag,
      associated_task_no as associatedTaskNo
     from ${mtcSchema}.mtc_repair_task
     where 
     id = #{id}
    </select>
    
    <update id="updateTireTask" parameterType="com.extracme.evcard.mtc.bo.UpdateTireTaskBO">
     update ${mtcSchema}.mtc_repair_task
      <set>
       <if test="tireTaskSchedule != null">
        tire_task_schedule = #{tireTaskSchedule},
       </if> 
       <if test="tireUnitPrice != null">
        tire_unit_price = #{tireUnitPrice},
       </if>
       <if test="tireBrand != null">
        tire_brand = #{tireBrand},
       </if>
       <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP} ,
       </if>
       <if test="updateOperId != null and updateOperId != 0">
        update_oper_id = #{updateOperId,jdbcType=BIGINT} , 
       </if>
       <if test="updateOperName != null and updateOperName != ''">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR} ,
       </if> 
      </set>
      where id = #{id}
    </update>
    
    <update id="updateSyncSapStatus">
     update ${mtcSchema}.mtc_repair_task
      set sap_send_status = #{status, jdbcType=INTEGER}
      where task_no = #{taskNo, jdbcType=VARCHAR}
    </update>


    <update id="updateSapSendSuccessTime">
      update ${mtcSchema}.mtc_repair_task
       set sap_success_time = #{successTime}
      where task_no = #{taskNo, jdbcType=VARCHAR}
    </update>
    
    <update id="updateRepairDepotSapCode">
     update ${mtcSchema}.mtc_repair_task
      set repair_depot_sap_code = #{repairDepotSapCode, jdbcType=VARCHAR},
      sap_sync_flag = 1
      where id = #{id, jdbcType=BIGINT}
    </update>
    
    <update id="updateReSendSap">
     update ${mtcSchema}.mtc_repair_task
      set sap_send_status = #{sapSendStatus, jdbcType=INTEGER},
      sap_sync_flag = 0,
      update_time = #{updateTime}
      where id = #{id, jdbcType=BIGINT}
    </update>

    <update id="updateBatchReSendSap">
        update ${mtcSchema}.mtc_repair_task
        set sap_send_status = 0,
            sap_sync_flag = 0,
            update_time = #{updateTime}
        where sap_send_status = 5
    </update>

    <select id="getMailByTaskNo" parameterType="com.extracme.evcard.mtc.dto.MtcUserDTO" resultType="com.extracme.evcard.mtc.dto.MtcUserDTO">
        SELECT
	         mi.repair_depot_account as username,
	         mi.sso_user_id as id,
	         mt.org_id as orgId,
	         mt.advanced_audit_leve as leve,
	         mt.repair_type_id as repairTypeId
        FROM
	         ${mtcSchema}.mtc_repair_task AS mt
	    LEFT JOIN ${mtcSchema}.mtc_repair_depot_info AS mi
	    ON
	      mt.repair_depot_id = mi.repair_depot_id
        WHERE
	         mt.task_no = #{repairTaskNo,jdbcType=VARCHAR}
    </select>
    <select id="getRepairTaskViewList" resultType="com.extracme.evcard.mtc.bo.RepairTaskViewBO">
        select
            a.id,
            a.task_no as 'taskNo'
        from
            mtc.mtc_repair_task a
        where verification_loss_task_schedule = 320
          and a.id != #{id, jdbcType=BIGINT} and vin = #{vin, jdbcType=VARCHAR}
          and a.total_mileage between #{minTotalMileage, jdbcType=DECIMAL} and #{maxTotalMileage, jdbcType=DECIMAL}
    </select>

    <select id="selectMtcDetailTaskInfo" parameterType="java.lang.String" resultType="com.evcard.mtc.provider.dto.RepairTaskViewDTO">
        select
            DATE_FORMAT(task_inflow_time, '%Y-%m-%d %H:%i:%s' ) as taskInflowTime,
            DATE_FORMAT(vehicle_check_time, '%Y-%m-%d %H:%i:%s' ) as vehicleCheckTime,
            DATE_FORMAT(vehicle_repair_time, '%Y-%m-%d %H:%i:%s' )  as vehicleRepairTime,
            DATE_FORMAT(vehicle_recive_time, '%Y-%m-%d %H:%i:%s' ) as vehicleReciveTime,
            DATE_FORMAT(syn_take_time, '%Y-%m-%d %H:%i:%s' ) as synTakeTime,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s' ) as taskCreateTime,
            task_no as taskNo,
            repair_type_name as repairTypeName
        from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo}
    </select>

    <select id="selectRepairTimeInfo" resultType="com.evcard.mtc.provider.dto.RepairTaskViewDTO">
        select
            DATE_FORMAT(task_inflow_time, '%Y-%m-%d %H:%i:%s' ) as taskInflowTime,
            DATE_FORMAT(vehicle_check_time, '%Y-%m-%d %H:%i:%s' ) as vehicleCheckTime,
            DATE_FORMAT(vehicle_repair_time, '%Y-%m-%d %H:%i:%s' )  as vehicleRepairTime,
            DATE_FORMAT(vehicle_recive_time, '%Y-%m-%d %H:%i:%s' ) as vehicleReciveTime,
            DATE_FORMAT(syn_take_time, '%Y-%m-%d %H:%i:%s' ) as synTakeTime,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s' ) as taskCreateTime,
            task_no as taskNo
        from ${mtcSchema}.mtc_repair_task where vin = #{vin}
        AND IF(vehicle_check_time IS NULL, DATE_ADD(now(), INTERVAL 100 YEAR) >= #{illegalTime, jdbcType=VARCHAR},vehicle_check_time <![CDATA[ >= ]]> #{illegalTime, jdbcType=VARCHAR})
        AND create_time <![CDATA[ <= ]]> #{illegalTime,jdbcType=VARCHAR}
    </select>

  <select id="selectRepairTimeInfoByDeliveryTime" resultType="com.evcard.mtc.provider.dto.RepairTaskViewDTO">
    select
      DATE_FORMAT(mrt.task_inflow_time, '%Y-%m-%d %H:%i:%s' ) as taskInflowTime,
      DATE_FORMAT(mrt.vehicle_check_time, '%Y-%m-%d %H:%i:%s' ) as vehicleCheckTime,
      DATE_FORMAT(mrt.vehicle_repair_time, '%Y-%m-%d %H:%i:%s' ) as vehicleRepairTime,
      DATE_FORMAT(mrt.vehicle_recive_time, '%Y-%m-%d %H:%i:%s' ) as vehicleReciveTime,
      DATE_FORMAT(mrt.syn_take_time, '%Y-%m-%d %H:%i:%s' ) as synTakeTime,
      DATE_FORMAT(mrt.create_time, '%Y-%m-%d %H:%i:%s' ) as taskCreateTime,
      mrt.task_no as taskNo
    from ${mtcSchema}.mtc_repair_task mrt
    left join ${mtcSchema}.mtc_repair_task_leaving_factory mrtlf on mrtlf.task_no = mrt.task_no
    where mrt.vin = #{vin}
      AND mrtlf.delivery_time is not null
      AND mrt.create_time <![CDATA[ <= ]]> #{illegalTime,jdbcType=VARCHAR}
      AND mrtlf.delivery_time <![CDATA[ >= ]]> #{illegalTime,jdbcType=VARCHAR}
  </select>

    <select id="queryMtcDetailTaskStatus" parameterType="java.lang.String" resultType="com.evcard.mtc.provider.dto.RepairTaskStatusStatusDTO">
        select task_no as taskNo,current_tache as currentTache,vehicle_transfer_task_schedule as vehicleTransferTaskSchedule,
               insurance_quote_task_schedule as insuranceQuoteTaskSchedule,verification_loss_task_schedule as verificationLossTaskSchedule,
               reassignment_task_schedule as reassignmentTaskSchedule,vehicle_repair_task_schedule as vehicleRepairTaskSchedule,
               vehicle_check_task_schedule as vehicleCheckTaskSchedule, vehicle_insurance_total_amount as vehicleInsuranceTotalAmount,
               material_collection_task_schedule as materialCollectionTaskSchedule, loss_registration_task_schedule as lossRegistrationTaskSchedule, settlement_task_schedule as settlementTaskSchedule
        from ${mtcSchema}.mtc_repair_task where task_no = #{taskNo}
    </select>

    <select id="queryMtcTaskStatusList" parameterType="com.extracme.evcard.mtc.bo.QueryMtcBo"
            resultType="com.evcard.mtc.provider.dto.RepairTaskStatusStatusDTO">
      select
        m.task_no as taskNo,m.current_tache as currentTache,
        m.vehicle_transfer_task_schedule as vehicleTransferTaskSchedule,
        m.insurance_quote_task_schedule as insuranceQuoteTaskSchedule,
        m.verification_loss_task_schedule as verificationLossTaskSchedule,
        m.reassignment_task_schedule as reassignmentTaskSchedule,
        m.vehicle_repair_task_schedule as vehicleRepairTaskSchedule,
        m.vehicle_check_task_schedule as vehicleCheckTaskSchedule,
        m.associated_order as associatedOrder,
        m.accident_report_number as insuranceReportNumber,
        m.vehicle_insurance_total_amount as vehicleInsuranceTotalAmount
        from ${mtcSchema}.mtc_repair_task m
        <where>
            and m.vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
            and m.repair_type_id in
            <foreach collection="repairTypeIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and m.create_time &gt;= DATE_FORMAT(#{orderStartTime,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
            and m.create_time &lt;= DATE_FORMAT(#{orderEndTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </where>
    </select>
  
  <select id="queryRepairTaskListByAccidentNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${mtcSchema}.mtc_repair_task
    where
     accident_no = #{accidentNo,jdbcType=VARCHAR}
  </select>
  
  <update id="updateConfirmCarDamageTypeByTaskNo">
    update ${mtcSchema}.mtc_repair_task set confirm_car_damage_type = #{confirmCarDamageType} where task_no = #{taskNo}
  </update>
  
  <update id="updateTotalMileageByTaskNo">
    update ${mtcSchema}.mtc_repair_task set total_mileage = #{totalMileage} where task_no = #{taskNo}
  </update>
  
  <select id="queryRepairAppletsTaskList" parameterType="com.extracme.evcard.mtc.bo.RepairAppletsQueryBO" 
          resultType="com.extracme.evcard.mtc.dto.RepairAppletsTaskDTO">
    select a.id,
           task_no,
           vin,
           vehicle_no,
           vehicle_model_seq,
           vehicle_model_info,
           repair_type_id,
           repair_type_name,
           a.org_id,
           a.org_name,
           review_to_sel_fee_flag,
           DATE_FORMAT(a.create_time, '%Y-%m-%d %H:%i:%s')         as taskCreateTime,
           DATE_FORMAT(vehicle_recive_time, '%Y-%m-%d %H:%i:%s') as vehicleReciveTime,
           current_tache,
           vehicle_transfer_task_schedule as vehicleTransferTaskSchedule,
           insurance_quote_task_schedule as insuranceQuoteTaskSchedule,
           vehicle_repair_task_schedule as vehicleRepairTaskSchedule,
           insurance_pre_review_task_schedule as insurancePreReviewTaskSchedule,
           verification_loss_task_schedule as verificationLossTaskSchedule,
           vehicle_check_task_schedule as vehicleCheckTaskSchedule,
           review_to_sel_fee_flag as reviewToSelFeeFlag,
           case when vehicle_repair_time is not null 
               then (case when date_add(verification_loss_check_time, interval expected_repair_days day) &gt; vehicle_repair_time then 1 else 0 end)
               else (case when date_add(verification_loss_check_time, interval expected_repair_days day) &gt; NOW() then 1 else 0 end)
           end as isOutTime,
           repair_grade as repairDepotGrade,
           repair_depot_type,
           repair_depot_name,
           relate_type,
           vehicle_insurance_total_amount as vehicleInsuranceTotalAmount,
           estimated_claim_amount as estimatedClaimAmount,
           user_assumed_amount as userAssumedAmount,
           self_funded_amount as selfFundedAmount,
           repair_insurance_total_amount as repairInsuranceTotalAmount,
            advanced_audit_leve as advancedAuditLeve,
          verification_reject_level as verificationRejectLevel,
            c.name
<!--    <if test=" lAPricelevel==0 or lAPricelevel==1">
      case when verification_loss_task_schedule = 300 and examine_level = 0 then N'未处理'
      when verification_loss_task_schedule = 310 and examine_level = 0 then N'处理中'
      when verification_loss_task_schedule = 320 then N'已完成'
      when verification_loss_task_schedule = 340 then N'已关闭'
      when verification_loss_task_schedule in (300,310) and examine_level = 1 then N'提交上级'
    </if>
    <if test=" lAPricelevel>=2">
      case when verification_loss_task_schedule = 300 then N'未处理'
      when verification_loss_task_schedule = 310 then N'处理中'
      when verification_loss_task_schedule = 320 then N'已完成'
      when verification_loss_task_schedule = 340 then N'已关闭'
    </if>65
    else '' end as verification_loss_task_name-->
    from ${mtcSchema}.mtc_repair_task a
    left join ${mtcSchema}.mtc_user c on c.id = a.verification_loss_task_oper_id
    <where>
      repair_type_id in (1,2,3,6,7,9)
      <if test="orgId != null and orgId == '00'">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="orgId != null and orgId != '00' and isOperationFlag == 1">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="repairDepotOrgId!=null and repairDepotOrgId!=''">
        and repair_depot_org_id = #{repairDepotOrgId}
      </if>
      <if test="repairDepotId!=null and repairDepotId!=''">
        and repair_depot_id = #{repairDepotId}
      </if>
      <if test="repairDepotType != null and repairDepotType != ''">
        and repair_depot_type = #{repairDepotType}
      </if>
      <if test="currentTache == 10">
        and (current_tache = 10 and vehicle_transfer_task_schedule = 100)
      </if>
      <if test="currentTache == 20">
        and (current_tache = 20 and insurance_quote_task_schedule in (200, 210, 240))
      </if>
      <if test="currentTache == 30">
        and (current_tache = 30 and verification_loss_task_schedule in (300, 310, 315) and advanced_audit_leve = #{lAPricelevel})
      </if>
      <if test="currentTache == 50">
        and (current_tache = 50 and vehicle_repair_task_schedule in (500, 510))
      </if>
      <if test="currentTache == 60">
        and (current_tache = 60 and vehicle_check_task_schedule = 600)
      </if>
      <if test="currentTache == 110 and isOperationFlag == 1">
        and (current_tache = 110 and insurance_pre_review_task_schedule = 1120 and insurance_pre_review_level = #{insurancePreReviewLevel})
      </if>
      <if test="currentTache == 110 and isOperationFlag == 2">
        and (current_tache = 110 and insurance_pre_review_task_schedule in (1100, 1110))
      </if>

      <if test="currentTache == null and isOperationFlag == 2">
        and ((current_tache = 10 and vehicle_transfer_task_schedule = 100)
        or (current_tache = 110 and insurance_pre_review_task_schedule in (1100, 1110))
        or (current_tache = 20 and insurance_quote_task_schedule in (200, 210, 240))
        or (current_tache = 50 and vehicle_repair_task_schedule in (500, 510))
        )
      </if>
      <if test="currentTache == null and isOperationFlag == 1">
        and (
        (current_tache = 110 and insurance_pre_review_task_schedule = 1120 and insurance_pre_review_level = #{insurancePreReviewLevel} )
        or (current_tache = 30 and verification_loss_task_schedule in (300, 310, 315) and advanced_audit_leve = #{lAPricelevel})
        or (current_tache = 60 and vehicle_check_task_schedule = 600)
        )
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
    </where>
    order by a.create_time desc
  </select>
  
  <select id="queryRepairAppletsTaskCount" parameterType="com.extracme.evcard.mtc.bo.RepairAppletsQueryBO"
          resultType="java.lang.Integer">
    select count(1)
    from ${mtcSchema}.mtc_repair_task
    <where>
      repair_type_id in (1,2,3,6,7,9)
      <if test="orgId != null and orgId == '00'">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="orgId != null and orgId != '00' and isOperationFlag == 1">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="repairDepotOrgId!=null and repairDepotOrgId!=''">
        and repair_depot_org_id = #{repairDepotOrgId}
      </if>
      <if test="repairDepotId!=null and repairDepotId!=''">
        and repair_depot_id = #{repairDepotId}
      </if>
      <if test="repairDepotType != null and repairDepotType != ''">
        and repair_depot_type = #{repairDepotType}
      </if>
      <if test="currentTache == 10">
        and (current_tache = 10 and vehicle_transfer_task_schedule = 100)
      </if>
      <if test="currentTache == 20">
        and (current_tache = 20 and insurance_quote_task_schedule in (200, 210, 240))
      </if>
      <if test="currentTache == 30">
        and (current_tache = 30 and verification_loss_task_schedule in (300, 310, 315)  and advanced_audit_leve = #{lAPricelevel})
      </if>
      <if test="currentTache == 50">
        and (current_tache = 50 and vehicle_repair_task_schedule in (500, 510))
      </if>
      <if test="currentTache == 60">
        and (current_tache = 60 and vehicle_check_task_schedule = 600)
      </if>
      <if test="currentTache == 110 and isOperationFlag == 2">
        and (current_tache = 110 and insurance_pre_review_task_schedule in (1100, 1110))
      </if>
      <if test="currentTache == 110 and isOperationFlag == 1">
        and (current_tache = 110 and insurance_pre_review_task_schedule = 1120 and insurance_pre_review_level = #{insurancePreReviewLevel})
      </if>
      <if test="currentTache == null and isOperationFlag == 2">
        and (
        (current_tache = 10 and vehicle_transfer_task_schedule = 100)
        or (current_tache = 110 and insurance_pre_review_task_schedule = 1120)
        or (current_tache = 20 and insurance_quote_task_schedule in (200, 210, 240))
        or (current_tache = 50 and vehicle_repair_task_schedule in (500, 510))
        )
      </if>
      <if test="currentTache == null and isOperationFlag == 1">
        and (
        (current_tache = 110 and insurance_pre_review_task_schedule = 1120 and insurance_pre_review_level = #{insurancePreReviewLevel})
        or (current_tache = 30 and verification_loss_task_schedule in (300, 310, 315) and advanced_audit_leve = #{lAPricelevel})
        or (current_tache = 60 and vehicle_check_task_schedule = 600)
        )
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
    </where>
  </select>

  <select id="queryLeavingFactorySchedule" parameterType="com.extracme.evcard.mtc.bo.VehicleLeavingFactoryQueryBO"
          resultType="com.extracme.evcard.mtc.bo.VehicleLeavingFactoryResultBO">
    SELECT
      a.id as id,
      a.task_no as taskNo,
      a.org_id as orgId,
      a.org_name as orgName,
      a.vehicle_no as vehicleNo,
      a.vehicle_model_seq as vehicleModelSeq,
      a.vehicle_model_info as vehicleModelInfo,
      a.vin as vin,
      a.repair_type_id as repairTypeId,
      case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）'else a.repair_type_name end
      as repairTypeName,
      b.repair_depot_org_id as repairDepotOrgId,
      b.repair_depot_name as repairDepotName,
      b.repair_depot_sap_code as repairDepotSapCode,
      a.sap_send_status as sapSendStatus,
      a.sap_sync_flag as sapSyncFlag,
      b.repair_task_inflow_time as taskInflowTime,
      b.repair_task_receive_time as vehicleReciveTime,
      a.resurvey_flag as resurveyFlag,
      a.vehicle_check_time as vehicleCheckTime,
      a.vehicle_check_task_schedule as vehicleCheckTaskSchedule,
      a.vehicle_insurance_total_amount as totalMoney,
      a.repair_grade as repairGrade,
      a.renttype as renttype,
      a.fact_operate_tag as factOperateTag,
      a.review_to_sel_fee_flag as reviewToSelFeeFlag,
      b.create_time as submitDateTime,
      b.delivery_time as deliveryTime,
      case a.current_tache
      when 10 then N'车辆交接'
      when 20 then N'维修报价'
      when 30 then N'核损核价'
      when 40 then N'改派中'
      when 50 then N'车辆维修'
      when 60 then N'车辆验收'
      when 70 then N'资料收集'
      when 80 then N'损失登记'
      when 90 then N'结算管理'
      when 100 then N'保养待维修'
      when 110 then N'进保预审'
      else '' end currentTache,
      a.current_tache currentTacheId,
      a.vehicle_transfer_task_schedule vehicleTransferTaskSchedule,
      a.insurance_quote_task_schedule insuranceQuoteTaskSchedule,
      a.verification_loss_task_schedule verificationLossTaskSchedule,
      a.reassignment_task_schedule reassignmentTaskSchedule,
      a.vehicle_repair_task_schedule vehicleRepairTaskSchedule,
      a.material_collection_task_schedule materialCollectionTaskSchedule,
      a.loss_registration_task_schedule lossRegistrationTaskSchedule,
      a.settlement_task_schedule settlementTaskSchedule,
      a.insurance_pre_review_task_schedule insurancePreReviewTaskSchedule,
      a.examine_level examineLevel,
      b.id as leavingFactoryId
    FROM
    ${mtcSchema}.mtc_repair_task_leaving_factory b
    LEFT JOIN ${mtcSchema}.mtc_repair_task a ON a.task_no = b.task_no
    <where>
       <!--a.`status` = 1 AND a.repair_type_id in (1,2,3,6,7) and a.create_time >= '2024-03-29 00:00:00'-->
       <!--<if test="taskType == 2 ">
        and b.leaving_status is not null
      </if>
      <if test="taskType == 1 ">
        and b.task_no is null
      </if>-->
      <if test="taskType != null and taskType != ''">
        and b.leaving_status = #{taskType}
      </if>
      <if test="taskNo != null and taskNo != ''">
        and a.task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and a.vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and a.vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and a.vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and a.maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and b.repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="repairDepotId != null and repairDepotId != ''">
        and b.repair_depot_id = #{repairDepotId}
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and a.resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add( a.vehicle_recive_time,INTERVAL a.expected_repair_days DAY ) >
        (CASE WHEN a.vehicle_repair_time IS NOT NULL THEN a.vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="startVehicleCheckTime != null and startVehicleCheckTime != ''">
        and a.vehicle_check_time >= DATE_FORMAT(#{startVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleCheckTime != null and endVehicleCheckTime != ''">
        and a.vehicle_check_time &lt;= DATE_FORMAT(#{endVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="sapSendStatus != null">
        and a.sap_send_status = #{sapSendStatus, jdbcType=INTEGER}
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and a.accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
      <if test="currentTacheId!=null">
        and a.current_tache = #{currentTacheId}
      </if>
      <if test="startDeliveryTime != null and startDeliveryTime != ''">
        and b.delivery_time &gt;= #{startDeliveryTime}
      </if>
      <if test="endVDeliveryTime != null and endVDeliveryTime != ''">
        and b.delivery_time &lt;= #{endVDeliveryTime}
      </if>
    </where>
    order by b.create_time DESC
  </select>

  <select id="queryLeavingFactoryCount" parameterType="com.extracme.evcard.mtc.bo.VehicleLeavingFactoryQueryBO"
          resultType="java.lang.Long">
    SELECT
        count(1)
    FROM
    ${mtcSchema}.mtc_repair_task_leaving_factory b
    LEFT JOIN ${mtcSchema}.mtc_repair_task a ON a.task_no = b.task_no
    <where>
       <!--a.`status` = 1 AND status = 1
      AND a.repair_type_id in (1,2,3,6,7)  and a.create_time >= '2024-03-29 00:00:00'-->
       <!--<if test="taskType == 2 ">
        and b.leaving_status is not null
      </if>
      <if test="taskType == 1 ">
        and b.task_no is null
      </if>-->
      <if test="taskType != null and taskType != ''">
        and b.leaving_status = #{taskType}
      </if>
      <if test="taskNo != null and taskNo != ''">
        and a.task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and a.vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and a.vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="repairDepotId != null and repairDepotId != ''">
        and b.repair_depot_id = #{repairDepotId}
      </if>
      <if test="vin != null and vin != ''">
        and a.vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and a.maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and b.repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and a.resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add( a.vehicle_recive_time,INTERVAL a.expected_repair_days DAY ) >
        (CASE WHEN a.vehicle_repair_time IS NOT NULL THEN a.vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="startVehicleCheckTime != null and startVehicleCheckTime != ''">
        and a.vehicle_check_time >= DATE_FORMAT(#{startVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleCheckTime != null and endVehicleCheckTime != ''">
        and a.vehicle_check_time &lt;= DATE_FORMAT(#{endVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="sapSendStatus != null">
        and a.sap_send_status = #{sapSendStatus, jdbcType=INTEGER}
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and a.accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
      <if test="currentTacheId!=null">
        and a.current_tache = #{currentTacheId}
      </if>
      <if test="startDeliveryTime != null and startDeliveryTime != ''">
        and b.delivery_time &gt;= #{startDeliveryTime}
      </if>
      <if test="endVDeliveryTime != null and endVDeliveryTime != ''">
        and b.delivery_time &lt;= #{endVDeliveryTime}
      </if>
    </where>
    order by a.create_time DESC
  </select>

  <select id="queryLeavingFactoryExport" parameterType="com.extracme.evcard.mtc.bo.VehicleLeavingFactoryQueryBO"
          resultType="com.extracme.evcard.mtc.bo.VehicleLeavingFactoryResultBO">
    SELECT
    a.id as id,
    a.task_no as taskNo,
    a.org_id as orgId,
    a.org_name as orgName,
    a.vehicle_no as vehicleNo,
    a.vehicle_model_seq as vehicleModelSeq,
    a.vehicle_model_info as vehicleModelInfo,
    a.vin as vin,
    a.repair_type_id as repairTypeId,
    case when (a.maintain_to_repair_flag = 1 and a.repair_type_id != 3) then N'自费维修（原车辆保养）'else a.repair_type_name end
    as repairTypeName,
    b.repair_depot_org_id as repairDepotOrgId,
    b.repair_depot_name as repairDepotName,
    b.repair_depot_sap_code as repairDepotSapCode,
    a.sap_send_status as sapSendStatus,
    a.sap_sync_flag as sapSyncFlag,
    b.repair_task_inflow_time as taskInflowTime,
    b.repair_task_receive_time as vehicleReciveTime,
    a.resurvey_flag as resurveyFlag,
    a.vehicle_check_time as vehicleCheckTime,
    a.vehicle_check_task_schedule as vehicleCheckTaskSchedule,
    a.vehicle_insurance_total_amount as totalMoney,
    a.repair_grade as repairGrade,
    a.renttype as renttype,
    a.fact_operate_tag as factOperateTag,
    a.review_to_sel_fee_flag as reviewToSelFeeFlag,
    b.create_time as submitDateTime,
    b.delivery_time as deliveryTime,
    case a.current_tache
    when 10 then N'车辆交接'
    when 20 then N'维修报价'
    when 30 then N'核损核价'
    when 40 then N'改派中'
    when 50 then N'车辆维修'
    when 60 then N'车辆验收'
    when 70 then N'资料收集'
    when 80 then N'损失登记'
    when 90 then N'结算管理'
    when 100 then N'保养待维修'
    when 110 then N'进保预审'
    else '' end currentTache,
    a.current_tache currentTacheId,
    a.current_tache currentTacheId,
    a.vehicle_transfer_task_schedule vehicleTransferTaskSchedule,
    a.insurance_quote_task_schedule insuranceQuoteTaskSchedule,
    a.verification_loss_task_schedule verificationLossTaskSchedule,
    a.reassignment_task_schedule reassignmentTaskSchedule,
    a.vehicle_repair_task_schedule vehicleRepairTaskSchedule,
    a.material_collection_task_schedule materialCollectionTaskSchedule,
    a.loss_registration_task_schedule lossRegistrationTaskSchedule,
    a.settlement_task_schedule settlementTaskSchedule,
    a.insurance_pre_review_task_schedule insurancePreReviewTaskSchedule,
    a.examine_level examineLevel,
    b.id as leavingFactoryId,
    b.create_time as createTime
    FROM
    ${mtcSchema}.mtc_repair_task_leaving_factory b
    LEFT JOIN ${mtcSchema}.mtc_repair_task a ON a.task_no = b.task_no
    <where>
       <!--a.`status` = 1 AND a.repair_type_id in (1,2,3,6,7) and a.create_time >= '2024-03-29 00:00:00'-->
      <if test="taskType != null and taskType != ''">
        and b.leaving_status = #{taskType}
      </if>
      <if test="taskNo != null and taskNo != ''">
        and a.task_no = #{taskNo}
      </if>
      <if test="orgId != null and orgId != ''">
        and a.org_id like concat(#{orgId},'%')
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and a.vehicle_no like concat(#{vehicleNo},'%')
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and a.vehicle_model_seq = #{vehicleModelSeq}
      </if>
      <if test="vin != null and vin != ''">
        and a.vin = #{vin}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and a.maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and b.repair_depot_name like concat('%',#{repairDepotName},'%')
      </if>
      <if test="repairDepotId != null and repairDepotId != ''">
        and b.repair_depot_id = #{repairDepotId}
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and a.resurvey_flag = #{resurveyFlag}
      </if>
      <if test="timeOut != null and timeOut != ''">
        and CASE WHEN date_add( a.vehicle_recive_time,INTERVAL a.expected_repair_days DAY ) >
        (CASE WHEN a.vehicle_repair_time IS NOT NULL THEN a.vehicle_repair_time ELSE now() END )
        THEN 1 ELSE 0 END = #{timeOut}
      </if>
      <if test="startTaskInflowTime != null and startTaskInflowTime != ''">
        and a.task_inflow_time &gt;= DATE_FORMAT(#{startTaskInflowTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endTaskInflowTime != null and endTaskInflowTime != ''">
        and a.task_inflow_time &lt;= DATE_FORMAT(#{endTaskInflowTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="startVehicleReciveTime != null and startVehicleReciveTime != ''">
        and a.vehicle_recive_time &gt;= DATE_FORMAT(#{startVehicleReciveTime},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleReciveTime != null and endVehicleReciveTime != ''">
        and a.vehicle_recive_time &lt;= DATE_FORMAT(#{endVehicleReciveTime},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="startVehicleCheckTime != null and startVehicleCheckTime != ''">
        and a.vehicle_check_time >= DATE_FORMAT(#{startVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00.000')
      </if>
      <if test="endVehicleCheckTime != null and endVehicleCheckTime != ''">
        and a.vehicle_check_time &lt;= DATE_FORMAT(#{endVehicleCheckTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59.999')
      </if>
      <if test="sapSendStatus != null">
        and a.sap_send_status = #{sapSendStatus, jdbcType=INTEGER}
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and a.accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and a.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
      <if test="currentTacheId!=null">
        and a.current_tache = #{currentTacheId}
      </if>
      <!--<if test="lastUpdateTime != null">
        and b.create_time &lt; #{lastUpdateTime}
      </if>-->
      <if test="startDeliveryTime != null and startDeliveryTime != ''">
        and b.delivery_time &gt;= #{startDeliveryTime}
      </if>
      <if test="endVDeliveryTime != null and endVDeliveryTime != ''">
        and b.delivery_time &lt;= #{endVDeliveryTime}
      </if>
    </where>
    order by b.create_time DESC
    /*limit #{exportSize}*/
  </select>

  <update id="updateCriteriaIdByTaskNo">
    update ${mtcSchema}.mtc_repair_task set criteria_id = #{criteriaId} where task_no = #{taskNo}
  </update>

  <update id="updateLeavingFactory" parameterType="com.evcard.mtc.provider.dto.UpdateTakeInfoDTO">
    update ${mtcSchema}.mtc_repair_task
    <set>
      <if test="takeUserName != null">
        take_user_name = #{takeUserName},
      </if>
      <if test="takeUserPhone != null">
        take_user_phone = #{takeUserPhone},
      </if>
      <if test="takeVoucher != null">
        take_voucher = #{takeVoucher},
      </if>
      <if test="synTakeTime != null">
        syn_take_time = #{synTakeTime,jdbcType=TIMESTAMP} ,
      </if>
    </set>
    where task_no = #{taskNo,jdbcType=VARCHAR} and status = 1
  </update>



  <select id="selectCheckedTaskList" parameterType="com.evcard.mtc.provider.dto.QueryCheckedTaskDTO" resultMap="ViewResultMap">
    select
    a.id,
    a.task_no,
    a.current_tache,
    a.confirm_type,
    a.advanced_audit_leve,
    a.org_id,
    a.org_name,
    a.operate_org_id,
    a.operate_org_name,
    a.vehicle_no,
    a.vehicle_model_seq,
    a.vehicle_model_info,
    a.vin,
    a.insurance_company_name,
    a.repair_type_id,
    case when a.repair_type_id = 1 then '事故维修' when  a.repair_type_id=2 then '自费维修' when a.repair_type_id = 3 then '车辆保养' when a.repair_type_id = 7 then '终端维修' when a.repair_type_id = 9 then '短租包修' end as repair_type_name,
    a.repair_grade,
    a.repair_depot_org_id,
    a.repair_depot_id,
    a.repair_depot_name,
    a.repair_depot_sap_code,
    a.repair_depot_type,
    a.task_inflow_time,
    a.verification_loss_check_time,
    a.vehicle_recive_time,
    a.vehicle_repair_time,
    a.vehicle_check_time,
    a.send_repair_time,
    a.expected_repair_days,
    a.expected_repair_complete,
    a.accident_report_number,
    a.repair_flag,
    a.terminal_id,
    a.total_mileage,
    a.associated_order,
    a.order_type,
    a.relate_type,
    a.order_remark,
    a.no_deductibles_flag,
    a.driver_name,
    a.driver_tel,
    a.routing_inspection_name,
    a.routing_inspection_tel,
    a.damaged_part_describe,
    a.accident_describe,
    a.trailer_flag,
    a.repair_review_total_amount,
    a.repair_replace_total_amount,
    a.repair_repair_total_amount,
    a.repair_insurance_total_amount,
    a.vehicle_replace_total_amount,
    a.vehicle_repair_total_amount,
    a.vehicle_insurance_total_amount,
    a.resurvey_flag,
    a.resurvey_part,
    a.duty_situation,
    a.recovery_amount,
    a.insurance_amount,
    a.acc_dep_amount,
    a.outage_loss_amount,
    a.vehicle_loss_amount,
    a.trailer_rescue_amount,
    a.maintain_amount,
    a.loss_order_amount,
    a.reassignment_repair_org_id,
    a.reassignment_reasons,
    a.reassignment_reject_reasons,
    a.check_result_flag,
    a.check_unqualified_reason,
    a.maintain_to_repair_flag,
    a.vehicle_transfer_task_schedule,
    a.insurance_quote_task_schedule,
    a.verification_loss_task_schedule,
    a.reassignment_task_schedule,
    a.vehicle_repair_task_schedule,
    a.vehicle_check_task_schedule,
    a.material_collection_task_schedule,
    a.loss_registration_task_schedule,
    a.insurance_pre_review_task_schedule,
    a.current_tache,
    a.verification_loss_task_oper_id,
    a.examine_level,
    a.nuclear_loss_reversion_flag,
    a.verification_reject_reasons,
    a.verification_reject_reasons_detail,
    a.over_time_reasons,
    a.repair_total_amount_first,
    a.verification_loss_check_id,
    a.accident_day_time,
    a.origin,
    a.renttype,
    a.take_user_name,
    a.take_user_phone,
    a.take_voucher,
    a.syn_take_time,
    a.close_reason,
    a.create_time,
    a.claims_flag,
    a.estimated_claim_amount,
    a.car_damage_type,
    a.fact_operate_tag,
    a.vehicle_scrape_value,
    a.review_to_sel_fee_flag,
    a.confirm_car_damage_type,
    a.accident_no,
    a.insurance_pre_review_level,
    CASE
    WHEN b.pic_type = 10 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS drivingLicense,

    CASE
    WHEN b.pic_type = 11 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS policy,

    CASE
    WHEN b.pic_type = 12 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS accident,

    CASE
    WHEN b.pic_type = 13 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS damageA,
    CASE
    WHEN b.pic_type = 14 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS damageB,

    CASE
    WHEN b.pic_type = 15 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS claims,

    CASE
    WHEN b.pic_type = 16 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS other,

    CASE
    WHEN b.pic_type = 2 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS repair,

    CASE
    WHEN b.pic_type = 1 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS damagedPart,

    CASE
    WHEN b.pic_type = 17 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS checkVideo,
    CASE
    WHEN b.pic_type = 18 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS afterPic,

    CASE
    WHEN b.pic_type = 19 THEN
    (
    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS accidentLiabilityConfirmationPicture,

    CASE
    WHEN b.pic_type = 20 THEN
    (
    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS insuranceCompanyLossOrderPicture,
    CASE
    WHEN b.pic_type = 21 THEN
    (
    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS ourDriverLicensePicture,
    CASE
    WHEN b.pic_type = 22 THEN
    (
    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS custPicture,
    CASE
    WHEN b.pic_type = 23 THEN
    (

    CASE
    WHEN b.pic_url is not NULL
    AND b.pic_url != '' THEN
    (
    CASE
    WHEN SUBSTR(b.pic_url, 1, 1) = '/' THEN
    CONCAT(
    '${evcard-aliyun-prefix}',
    SUBSTR(b.pic_url, 2)
    )
    ELSE
    CONCAT('${evcard-aliyun-prefix}', b.pic_url)
    END
    )
    ELSE
    null
    END
    )
    ELSE
    null
    END AS damagedPartV,
    a.pre_review_vehicle_scrape_value,
    a.pre_review_vehicle_scrape_time,
    a.cust_pays_direct,
    a.cust_amount,
    a.send_repair_time,
    a.self_funded_amount
    from  ${mtcSchema}.mtc_repair_task a
    left join ${mtcSchema}.mtc_vehicle_repair_pic b on a.task_no = b.task_no
    where
      a.repair_type_id = #{repairTypeId}
      <if test="taskInflowTime != null and taskInflowTime != ''">
        AND a.task_inflow_time &gt;= #{taskInflowTime}
      </if>
  </select>

  <update id="updateAssociatedOrder" parameterType="com.extracme.evcard.mtc.dto.RelateOrderDTO">
    update ${mtcSchema}.mtc_repair_task
    set
      order_type = #{orderType} ,
      relate_type = #{relateType} ,
      <if test="orderRemark != null">
        order_remark = #{orderRemark} ,
      </if>
     <!-- 长租订单字段 -->
      <if test="orderType != null and orderType == 1">
        <if test="orderInfoDTO != null and orderInfoDTO.violationName != null">
          violation_name = #{orderInfoDTO.violationName} ,
        </if>
        <if test="orderInfoDTO != null and orderInfoDTO.violationTelNo != null">
          violation_tel_no = #{orderInfoDTO.violationTelNo} ,
        </if>
        <if test="orderInfoDTO != null and orderInfoDTO.vehicleClientMaintenanceFee != null">
          vehicle_client_maintenance_fee = #{orderInfoDTO.vehicleClientMaintenanceFee} ,
        </if>
        <if test="orderInfoDTO != null and orderInfoDTO.clientInspectTag != null">
          client_inspect_tag = #{orderInfoDTO.clientInspectTag} ,
        </if>
        <if test="orderInfoDTO != null and orderInfoDTO.clientUpkeepTag != null">
          client_upkeep_tag = #{orderInfoDTO.clientUpkeepTag} ,
        </if>
      </if>
     <!-- 门店订单字段 -->
      <if test="orderType != null and orderType == 2">
        <if test="orderInfoDTO != null and orderInfoDTO.serviceType != null">
          service_type = #{orderInfoDTO.serviceType} ,
        </if>
        <if test="orderInfoDTO != null and orderInfoDTO.serviceContent != null">
          service_content = #{orderInfoDTO.serviceContent} ,
        </if>
      </if>
     <!-- 解除关联变更 -->
      <if test="operateType != null and operateType == 2">
          violation_name = '' ,
          violation_tel_no = '' ,
          vehicle_client_maintenance_fee = 0 ,
          client_inspect_tag = 0 ,
          client_upkeep_tag = 0 ,
          service_type = 0 ,
          service_content = '' ,
      </if>
      associated_order = #{associatedOrder}
    where task_no = #{taskNo}
  </update>

  <update id="adjustCustAmount" parameterType="com.extracme.evcard.mtc.dto.RepairCustInfoDTO">
    update ${mtcSchema}.mtc_repair_task
    set cust_pays_direct = #{custPaysDirect} ,
    cust_amount = #{custAmount} ,
    user_assumed_amount = #{userAssumedAmount} ,
    not_user_assumed_amount = #{notUserAssumedAmount}
    where id = #{id}
  </update>

  <update id="updateSelfFundedAmount">
    update ${mtcSchema}.mtc_repair_task set self_funded_amount = #{selfFundedAmount} where id = #{id}
  </update>

  <select id="searchMtcRepairTaskList" parameterType="com.extracme.evcard.mtc.bo.SearchMtcRepairTaskListReqBO"
          resultType="com.extracme.evcard.mtc.dto.RepairAppletsTaskDTO">
    select
      id,
      task_no,
      vin,
      vehicle_no,
      vehicle_model_seq,
      vehicle_model_info,
      repair_type_id,
      repair_type_name,
      org_id,
      org_name,
      review_to_sel_fee_flag,
      DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s')         as taskCreateTime,
      DATE_FORMAT(vehicle_recive_time, '%Y-%m-%d %H:%i:%s') as vehicleReciveTime,
      current_tache,
      vehicle_transfer_task_schedule,
      insurance_quote_task_schedule,
      vehicle_repair_task_schedule,
      verification_loss_task_schedule,
      insurance_pre_review_task_schedule,
      verification_loss_task_schedule,
      vehicle_check_task_schedule,
      reassignment_task_schedule,
      vehicle_check_task_schedule,
      material_collection_task_schedule,
      loss_registration_task_schedule,
      settlement_task_schedule,
      take_user_name,
      review_to_sel_fee_flag,
      case
        when vehicle_repair_time is not null then (case when date_add(verification_loss_check_time, interval expected_repair_days day) &gt; vehicle_repair_time then 1 else 0 end)
        else (
                case
                  when date_add(verification_loss_check_time, interval expected_repair_days day) &gt; NOW() then 1
                  else 0
                end
              )
      end as isOutTime,
      repair_grade as repairDepotGrade,
      repair_depot_type,
      repair_depot_name,
      relate_type,
      advanced_audit_leve as advancedAuditLeve,
      vehicle_insurance_total_amount as vehicleInsuranceTotalAmount,
      estimated_claim_amount as estimatedClaimAmount,
      user_assumed_amount as userAssumedAmount,
      self_funded_amount as selfFundedAmount,
      repair_insurance_total_amount as repairInsuranceTotalAmount
    from
      ${mtcSchema}.mtc_repair_task
    <where>
      repair_type_id in (1,2,3,6,7,9)
      <if test="orgId != null and orgId == '00'">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="orgId != null and orgId != '00' and isOperationFlag == 1">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="repairDepotOrgId!=null and repairDepotOrgId!=''">
        and repair_depot_org_id = #{repairDepotOrgId}
      </if>
      <if test="repairDepotId!=null and repairDepotId!=''">
        and repair_depot_id = #{repairDepotId}
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
      <if test="repairDepotType != null and repairDepotType != ''">
        and repair_depot_type = #{repairDepotType}
      </if>
       <!-- <if test="currentTache == null and isOperationFlag == 1">
      and (
      (current_tache = 110 and insurance_pre_review_task_schedule = 1120 and insurance_pre_review_level = #{insurancePreReviewLevel} )
      or (current_tache = 30 and verification_loss_task_schedule in (300, 310) and advanced_audit_leve = #{lAPricelevel})
      or (current_tache = 60 and vehicle_check_task_schedule = 600)
      ) and current_tache in (30, 60 , 110)
      </if>-->
    </where>
    order by create_time desc
  </select>

  <select id="countMtcRepairTaskList" parameterType="com.extracme.evcard.mtc.bo.SearchMtcRepairTaskListReqBO"
          resultType="java.lang.Integer">
    select
      count(1)
    from
      ${mtcSchema}.mtc_repair_task
    <where>
      repair_type_id in (1,2,3,6,7,9)
      <if test="orgId != null and orgId == '00'">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="orgId != null and orgId != '00' and isOperationFlag == 1">
        and org_id like concat(#{orgId},'%')
      </if>
      <if test="repairDepotOrgId!=null and repairDepotOrgId!=''">
        and repair_depot_org_id = #{repairDepotOrgId}
      </if>
      <if test="repairDepotId!=null and repairDepotId!=''">
        and repair_depot_id = #{repairDepotId}
      </if>
      <if test="repairDepotType != null and repairDepotType != ''">
        and repair_depot_type = #{repairDepotType}
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no like concat(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo}
      </if>
    </where>
  </select>

  <select id="selectByVinList" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from
      ${mtcSchema}.mtc_repair_task
    WHERE
      status != 0
      <if test="vinList != null and vinList.size() > 0">
        and vin in
        <foreach collection="vinList" item="item" open="(" close=")" separator=",">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
  </select>

  <update id="updateContinueDays">
    update
      ${mtcSchema}.mtc_repair_task
    set
      continue_days = #{continueDays}
    where
      task_no = #{taskNo}
  </update>
</mapper>
