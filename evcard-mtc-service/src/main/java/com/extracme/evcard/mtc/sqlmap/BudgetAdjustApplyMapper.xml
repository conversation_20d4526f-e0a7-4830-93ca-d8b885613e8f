<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.mtc.dao.BudgetAdjustApplyMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.mtc.model.BudgetAdjustApply" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="task_no" property="taskNo" jdbcType="VARCHAR" />
    <result column="before_application_id" property="beforeApplicationId" jdbcType="VARCHAR" />
    <result column="budget_adjust_application_id" property="budgetAdjustApplicationId" jdbcType="VARCHAR" />
    <result column="request_no" property="requestNo" jdbcType="VARCHAR" />
    <result column="sync_status" property="syncStatus" jdbcType="INTEGER" />
    <result column="error_message" property="errorMessage" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    id, task_no, before_application_id, budget_adjust_application_id, request_no, sync_status, 
    error_message, remark, create_time, create_oper_id, create_oper_name
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.extracme.evcard.mtc.model.BudgetAdjustApplyExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from budget_adjust_apply
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from budget_adjust_apply
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    delete from budget_adjust_apply
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.extracme.evcard.mtc.model.BudgetAdjustApplyExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    delete from budget_adjust_apply
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.mtc.model.BudgetAdjustApply" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    insert into budget_adjust_apply (id, task_no, before_application_id, 
      budget_adjust_application_id, request_no, sync_status, 
      error_message, remark, create_time, 
      create_oper_id, create_oper_name)
    values (#{id,jdbcType=INTEGER}, #{taskNo,jdbcType=VARCHAR}, #{beforeApplicationId,jdbcType=VARCHAR}, 
      #{budgetAdjustApplicationId,jdbcType=VARCHAR}, #{requestNo,jdbcType=VARCHAR}, #{syncStatus,jdbcType=INTEGER}, 
      #{errorMessage,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.mtc.model.BudgetAdjustApply" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    insert into budget_adjust_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskNo != null" >
        task_no,
      </if>
      <if test="beforeApplicationId != null" >
        before_application_id,
      </if>
      <if test="budgetAdjustApplicationId != null" >
        budget_adjust_application_id,
      </if>
      <if test="requestNo != null" >
        request_no,
      </if>
      <if test="syncStatus != null" >
        sync_status,
      </if>
      <if test="errorMessage != null" >
        error_message,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskNo != null" >
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="beforeApplicationId != null" >
        #{beforeApplicationId,jdbcType=VARCHAR},
      </if>
      <if test="budgetAdjustApplicationId != null" >
        #{budgetAdjustApplicationId,jdbcType=VARCHAR},
      </if>
      <if test="requestNo != null" >
        #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null" >
        #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="errorMessage != null" >
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.mtc.model.BudgetAdjustApplyExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    select count(*) from budget_adjust_apply
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    update budget_adjust_apply
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.taskNo != null" >
        task_no = #{record.taskNo,jdbcType=VARCHAR},
      </if>
      <if test="record.beforeApplicationId != null" >
        before_application_id = #{record.beforeApplicationId,jdbcType=VARCHAR},
      </if>
      <if test="record.budgetAdjustApplicationId != null" >
        budget_adjust_application_id = #{record.budgetAdjustApplicationId,jdbcType=VARCHAR},
      </if>
      <if test="record.requestNo != null" >
        request_no = #{record.requestNo,jdbcType=VARCHAR},
      </if>
      <if test="record.syncStatus != null" >
        sync_status = #{record.syncStatus,jdbcType=INTEGER},
      </if>
      <if test="record.errorMessage != null" >
        error_message = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null" >
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null" >
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    update budget_adjust_apply
    set id = #{record.id,jdbcType=INTEGER},
      task_no = #{record.taskNo,jdbcType=VARCHAR},
      before_application_id = #{record.beforeApplicationId,jdbcType=VARCHAR},
      budget_adjust_application_id = #{record.budgetAdjustApplicationId,jdbcType=VARCHAR},
      request_no = #{record.requestNo,jdbcType=VARCHAR},
      sync_status = #{record.syncStatus,jdbcType=INTEGER},
      error_message = #{record.errorMessage,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.mtc.model.BudgetAdjustApply" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    update budget_adjust_apply
    <set >
      <if test="taskNo != null" >
        task_no = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="beforeApplicationId != null" >
        before_application_id = #{beforeApplicationId,jdbcType=VARCHAR},
      </if>
      <if test="budgetAdjustApplicationId != null" >
        budget_adjust_application_id = #{budgetAdjustApplicationId,jdbcType=VARCHAR},
      </if>
      <if test="requestNo != null" >
        request_no = #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null" >
        sync_status = #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="errorMessage != null" >
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.mtc.model.BudgetAdjustApply" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 05 15:24:29 CST 2025.
    -->
    update budget_adjust_apply
    set task_no = #{taskNo,jdbcType=VARCHAR},
      before_application_id = #{beforeApplicationId,jdbcType=VARCHAR},
      budget_adjust_application_id = #{budgetAdjustApplicationId,jdbcType=VARCHAR},
      request_no = #{requestNo,jdbcType=VARCHAR},
      sync_status = #{syncStatus,jdbcType=INTEGER},
      error_message = #{errorMessage,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>